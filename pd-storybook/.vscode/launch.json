{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Launch Storybook",
      "type": "node",
      "request": "launch",
      "runtimeExecutable": "pnpm",
      "args": [
        "run",
        "start:dev",
        "--",
        "--inspect-brk"
      ],
      "cwd": "${workspaceFolder}",
      "sourceMaps": true,
      "env": {
        "NODE_ENV": "development"
      },
      "runtimeArgs": [
        "--inspect-brk"
      ],
      "outFiles": [
        "${workspaceFolder}/**/*.js"
      ],
      "console": "integratedTerminal",
      "internalConsoleOptions": "neverOpen"
    },
    {
      "type": "node",
      "request": "launch",
      "name": "test - current file",
      "runtimeExecutable": "${workspaceFolder}/node_modules/.bin/jest",
      "runtimeArgs": [
        "${file}",
        "--runInBand",
        "--detectOpenHandles"
      ],
      "cwd": "${workspaceFolder}",
      "outputCapture": "std",
      "restart": true,
      "console": "integratedTerminal",
      "internalConsoleOptions": "neverOpen"
    },
    {
      "name": "Launch web storybook",
      "type": "chrome",
      "request": "launch",
      "url": "http://localhost:3011",
      "pathMapping": {
        "/": "${workspaceFolder}/src",
      },
      "webRoot": "${workspaceFolder}/src",
      "sourceMaps": true,
      "userDataDir": "${workspaceFolder}/.vscode/chrome",
      "internalConsoleOptions": "neverOpen",
    }
  ]
}