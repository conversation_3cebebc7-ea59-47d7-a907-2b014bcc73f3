{
  "extends": "./tsconfig.json",
  "compilerOptions": {
    "declaration": true,
    "declarationMap": false,
    "outDir": "./dist",
    "rootDir": "./src"
  },
  "exclude": ["src/**/*.test.tsx", "src/**/*.test.ts", "src/**/*.stories.tsx", "src/**/*.stories.ts", "src/**/*.stories.mdx", "src/**/*.Style.ts", "dist", "node_modules"],
  "include": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.d.ts", "src/**/*.css"],
}