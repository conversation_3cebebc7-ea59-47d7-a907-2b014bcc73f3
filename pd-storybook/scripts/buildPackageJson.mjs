import fs from 'fs';
import path from 'path';

// Ruta del archivo package.json
const packageJsonPath = path.resolve('package.json');

// Leer el archivo package.json como un string
let packageJsonContent = fs.readFileSync(packageJsonPath, 'utf8');

packageJsonContent = packageJsonContent
  .replace(/src\//g, '')
  .replace(/dist\//g, '')
  .replace(/\.ts/g, '.js');

fs.writeFileSync(`${path.dirname(packageJsonPath)}/dist/package.json`, packageJsonContent, 'utf8');
