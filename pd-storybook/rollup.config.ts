/* eslint-disable import/no-extraneous-dependencies */

import commonjs from '@rollup/plugin-commonjs';
import nodeResolve from '@rollup/plugin-node-resolve';
import typescript from '@rollup/plugin-typescript';
import postcssImport from 'postcss-import';
import { InputPluginOption } from 'rollup';
import PeerDepsExternalPlugin from 'rollup-plugin-peer-deps-external';
import postcss from 'rollup-plugin-postcss';

const rollupOptions = [{
  input: 'src/index.ts',
  output:
      {
        dir: 'dist',
        format: 'es',
        sourcemap: true,
      },
  watch: false,
  plugins: [
      PeerDepsExternalPlugin() as InputPluginOption,
      typescript({ tsconfig: './tsconfig.build.json', sourceMap: true }),
      commonjs(),
      nodeResolve(),
  ],
  external: ['react', 'react-dom'],
}, {
  input: 'src/configurations/Theme.Configuration.ts',
  output: [{ file: 'dist/configurations/Theme.Configuration.js', format: 'es' }],
  plugins: [
    typescript({
      tsconfig: './tsconfig.build.json', sourceMap: true, include: ['Theme.Configuration.ts'], outDir: 'dist/configurations', rootDir: 'src/configurations',
    }),
  ],
  watch: false,
}, {
  input: 'src/styles/Global.Style.css',
  output: [{ file: 'dist/styles/Global.css' }],
  plugins: [
    postcss({
      extract: true,
      modules: false,
      inject: false,
      minimize: true,
      plugins: [postcssImport()],
    }),
  ],
}];

export default rollupOptions;
