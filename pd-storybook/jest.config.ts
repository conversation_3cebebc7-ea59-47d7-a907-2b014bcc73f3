import type { Config } from 'jest';

const config: Config = {
  testEnvironment: 'jest-environment-jsdom',
  setupFilesAfterEnv: ['<rootDir>/jest.setup.ts'],
  moduleFileExtensions: ['js', 'jsx', 'ts', 'tsx', 'json'],
  modulePathIgnorePatterns: ['<rootDir>/dist/'],
  rootDir: './',
  testRegex: ['.*\\.test\\.ts$', '.*\\.test\\.tsx$'],
  transform: {
    '^.+\\.(ts|tsx)$': 'ts-jest',
  },
  moduleNameMapper: {
    '\\.(css|less|scss|sass)$': 'identity-obj-proxy',
  },
  coveragePathIgnorePatterns: [
    '<rootDir>/dist/',
    '<rootDir>/.*\\.d\\.ts',
    '<rootDir>/\\.eslintrc\\.js',
    '<rootDir>/jest\\.config\\.ts',
    '<rootDir>/coverage/',
  ],
  fakeTimers: {
    now: (new Date('2024-08-21T00:00:00Z')).getTime(),
    enableGlobally: true,
  },
  coverageThreshold: {
    global: {
      branches: 94,
      functions: 95,
      lines: 95,
      statements: 95,
    },
  },
  snapshotSerializers: ['@emotion/jest/serializer'],
};

export default config;
