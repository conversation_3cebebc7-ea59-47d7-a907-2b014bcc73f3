version: 2.1
orbs:
  slack: circleci/slack@5.1.1

commands:
  send-slack-message:
    parameters:
      title:
        type: string
      event: 
        type: enum
        enum:
          - always
          - pass
          - fail
      workflow_name:
        type: string
    steps:
      - slack/notify:
          custom: |
            {
              "blocks": [
                {
                  "type": "header",
                  "text": {
                    "type": "plain_text",
                    "text": "@pitsdepot/storybook: << parameters.title >>",
                    "emoji": true
                  }
                },
                {
                  "type": "section",
                  "fields": [
                    {
                      "type": "mrkdwn",
                      "text": "*Tag:*\n${CIRCLE_TAG}"
                    },
                    {
                      "type": "mrkdwn",
                      "text": "*Workflow name/Build Id:*\n<< parameters.workflow_name >>/${CIRCLE_WORKFLOW_ID}"
                    },
                    {
                      "type": "mrkdwn",
                      "text": "*Triggered by:*\n${CIRCLE_USERNAME:-None}"
                    }
                  ]
                },
                {
                  "type": "divider"
                },
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": ":link: *Links*"
                  }
                },
                {
                  "type": "section",
                  "fields": [
                    {
                      "type": "mrkdwn",
                      "text": "<${CIRCLE_BUILD_URL}|View Build Details>"
                    },
                    {
                      "type": "mrkdwn",
                      "text": "<${REPO_URL}/commits/${CIRCLE_SHA1}|See commit>"
                    }
                  ]
                },
                {
                  "type": "context",
                  "elements": [
                    {
                      "type": "mrkdwn",
                      "text": "This notification was generated by CircleCI for ${CIRCLE_PROJECT_REPONAME}."
                    }
                  ]
                }
              ]
            }
          event: << parameters.event >>
          channel: "#ci_cd"

jobs:
  build-and-publish:
    docker:
      - image: cimg/node:20.18.1
    environment:
      REPO_URL: << pipeline.project.git_url >>
    steps:
      - send-slack-message:
          title: ":hourglass_flowing_sand: Building and publishing in progress"
          event: always
          workflow_name: "Build and Publish"
      - checkout
      - run:
          name: Write GOOGLE_APPLICATION_CREDENTIALS to file
          command: |
            echo "$DEPLOY_CREDENTIALS" > /tmp/google-credentials.json
            echo "export GOOGLE_APPLICATION_CREDENTIALS=/tmp/google-credentials.json" >> $BASH_ENV
      - restore_cache:
          name: Restore pnpm Package Cache
          keys:
            - pnpm-packages-{{ checksum "pnpm-lock.yaml" }}
      - run:
          name: Install pnpm package manager
          command: |
            npm i -g corepack@latest
            corepack prepare pnpm@latest-10 --activate
            pnpm config set store-dir .pnpm-store
            pnpm config set global-bin-dir .pnpm-store/.bin
            echo "export PATH=$PATH:$(pwd)/.pnpm-store/.bin" >> $BASH_ENV
      - run:
          name: Authenticate with private registry
          command: |
            echo "$DEPLOY_CREDENTIALS" > /tmp/google-credentials.json
            export GOOGLE_APPLICATION_CREDENTIALS=/tmp/google-credentials.json
            pnpm artifactregistry-login
      - run:
          name: Install Dependencies
          command: pnpm install --frozen-lockfile --ignore-scripts
      - run:
          name: Build Project
          command: pnpm build
      - run:
          name: Publish to npm
          command: |
            cd dist
            pnpm publish --ignore-scripts --no-git-checks
      - send-slack-message:
          title: ":rocket: Published to registry"
          event: pass
          workflow_name: "Build and Publish"
      - send-slack-message:
          title: ":bangbang: Build and publish failed :speak_no_evil:"
          event: fail
          workflow_name: "Build and Publish"
  notify_start:
    docker:
      - image: cimg/node:20.18.1
    environment:
      REPO_URL: << pipeline.project.git_url >>
    steps:
      - send-slack-message:
          title: ":warning: Build and publish, approval required :warning: " 
          event: always
          workflow_name: "Build and Publish"
workflows:
  build-publish:
    jobs:
      - notify_start:
          context:
            - SLACK
          filters:
            tags:
              only: /[0-9]+\.[0-9]+\.[0-9]+/
            branches:
              ignore: /.*/
      - wait-for-approval:
          type: approval
          requires:
            - notify_start
          filters:
            tags:
              only: /[0-9]+\.[0-9]+\.[0-9]+/
            branches:
              ignore: /.*/
      - build-and-publish:
          context:
            - CREDENTIALS
            - SLACK
          filters:
            tags:
              only: /^[0-9]+\.[0-9]+\.[0-9]+/
            branches:
              ignore: /.*/
          requires:
            - wait-for-approval