import Theme from '../src/configurations/Theme.Configuration';
import '../src/styles/Global.Style.css';

import type { Preview } from "@storybook/react";

const preview: Preview = {
  parameters: {
    backgrounds: {
      values: [
        {
          name: 'dark',
          value: `${Theme.colors.dark[200]}`,
        },
        {
          name: 'light',
          value: `${Theme.colors.white
          }`,
        },
      ]
      },
    docs: {
      toc: true,
    },
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i,
      },
    },
  },
  tags: ['autodocs'],
};

export default preview;
