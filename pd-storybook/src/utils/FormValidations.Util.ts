export const isRequired = (value?: string | number) => (value === undefined || value === null || value.toString().trim() === '' ? 'Campo requerido' : undefined);

export const singleWord = (value?: string) => (value && value.trim().split(' ').length > 1 ? 'Ingresa solo una palabra' : undefined);

export const minLength = (min: number) => (value?: string) => (value && value.length < min ? `Debe contener al menos ${min} caracteres` : undefined);

export const maxLength = (max: number) => (value?: string) => (value && value.length > max ? `Debe contener máximo ${max} caracteres` : undefined);

export const isEmail = (value?: string) => (value && !/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,4}$/i.test(value) ? 'Debe ser una dirección de email válida' : undefined);

export const isNumeric = (value?: string) => (value && Number.isNaN(Number(value)) ? 'Debe ser un número' : undefined);

export const isPositiveNumer = (value?: string) => (value && Number(value) < 0 ? 'Debe ser positivo' : undefined);

export const isValidPassword = (value?: string) => {
  if (!value) return 'La constraseña es requerida';

  const checks = [
    { regex: /[a-z]/, message: 'al menos una letra minúscula' },
    { regex: /[A-Z]/, message: 'al menos una letra mayúscula' },
    { regex: /\d/, message: 'al menos un número' },
    { regex: /[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/, message: 'al menos un carácter especial. Ejemplo: !"#$%&\'()*+,-./:;<=>?@[\\]^_`{|}~' },
  ];

  const failedChecks = checks
    .filter((check) => !check.regex.test(value))
    .map((check) => check.message);

  return failedChecks.length > 0
    ? `La contraseña debe contener ${failedChecks.join(', ')}`
    : undefined;
};

export function combineValidators<T>(...validators: Array<(value?: T) => string | undefined>) {
  return (value?: T) => validators.map((validate) => validate(value)).find((error) => error !== undefined);
}
