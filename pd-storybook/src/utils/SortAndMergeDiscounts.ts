import { Discount } from '../components/discounts/DiscountItem.Component';

interface SortAndMergeDiscountsProps {
    catalogDiscounts: Discount[];
    storeDiscounts: Discount[];
}

export const SortAndMergeDiscounts = (discounts: SortAndMergeDiscountsProps) => {
  const { catalogDiscounts, storeDiscounts } = discounts;

  const sortedCatalogDiscounts = catalogDiscounts?.sort((a, b) => (a.requiredQuantity ?? 0) - (b.requiredQuantity ?? 0));

  const sortedStoreDiscounts = storeDiscounts?.sort((a, b) => (a.requiredAmount ?? 0) - (b.requiredAmount ?? 0));

  const finalApplicableDiscounts = [...sortedCatalogDiscounts, ...sortedStoreDiscounts];

  return finalApplicableDiscounts;
};
