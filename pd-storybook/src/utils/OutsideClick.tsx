import { useEffect, useRef } from 'react';

type OutsideClickProps = {
  children: React.ReactNode;
  onOutsideClick: (e: MouseEvent) => void;
};

const OutsideClick: React.FC<OutsideClickProps> = ({ children, onOutsideClick }) => {
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (ref.current && !ref.current.contains(event.target as Node)) {
        onOutsideClick(event);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [onOutsideClick]);

  return <div ref={ref}>{children}</div>;
};

export default OutsideClick;
