import { action } from '@storybook/addon-actions';
import type { Meta, StoryObj } from '@storybook/react';
import { useEffect, useState } from 'react';

import { ShoppingCartItem, ShoppingCartItemProps } from '../components/shoppingCart/ShoppingCartItem.Component';

const meta: Meta<typeof ShoppingCartItem> = {
  title: 'Components/ShoppingCart/ShoppingCartItem',
  component: ShoppingCartItem,
  args: {
    id: '001',
    name: 'Test Product with a very very long name',
    imageUrl: 'https://www.betaautoparts.com/cdn/shop/files/761941035A-761941773-vw-touareg-headlamp-led-left-hella-1EX-013-143-211.jpg?v=1723023814&width=360',
    unitPrice: 100,
    unitPriceAfterDiscount: 85,
    subtotal: 85,
    quantity: 1,
  },
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
};

export default meta;

type Story = StoryObj<typeof ShoppingCartItem>;

export const Default: Story = {
  render: (args) => {
    const [product, setProduct] = useState<ShoppingCartItemProps>(args);

    const handleIncrement = () => {
      if (product.unitPrice !== null) {
        setProduct({ ...product, quantity: product.quantity + 1, subtotal: product.unitPrice * (product.quantity + 1) });
      }
    };

    const handleDecrement = () => {
      if (product.unitPrice !== null) {
        setProduct({ ...product, quantity: product.quantity - 1, subtotal: product.unitPrice * (product.quantity - 1) });
      }
    };

    const handleRemove = () => {
      action('Remove item')(product.name);
    };

    const handleQuantityChange = (id: string, newQuantity: number) => {
      if (product.unitPrice !== null) {
        setProduct({ ...product, quantity: newQuantity, subtotal: product.unitPrice * newQuantity });
      }
    };

    return (
      <div className='pd-w-64'>
        <ShoppingCartItem
          {...product}
          onIncrement={handleIncrement}
          onDecrement={handleDecrement}
          onRemove={handleRemove}
          onQuantityChange={handleQuantityChange}
        />

        <div className='pd-mt-4 pd-text-sm pd-text-dark-600'>
          {JSON.stringify(product)}
        </div>
      </div>
    );
  },
};

const appliedDiscount = {
  id: 'bf8a2d7a-4faf-4105-8f00-ce39f9eea9a3',
  discountValue: 15,
  discountType: 'percentage',
  name: '15 percent seed company 1',
  requiredQuantity: 0,
  startDate: '2025-03-17T14:13:59.083Z',
  endDate: null,
  priceAfterDiscount: 47.52,
};

const applicableDiscounts = [
  {
    id: 'bf8a2d7a-4faf-4105-8f00-ce39f9eea9a3',
    discountValue: 15,
    discountType: 'percentage',
    name: '15 percent seed company 1',
    requiredQuantity: 0,
    startDate: '2025-03-17T14:13:59.083Z',
    endDate: null,
    priceAfterDiscount: 47.52,
  },
  {
    id: 'bf8a2d7a-4faf-4105-8f00-ce39f9eea9a7',
    discountValue: 50,
    discountType: 'percentage',
    name: '50 percent discount seed company 3',
    requiredQuantity: 50,
    startDate: '2025-03-16T14:13:59.083Z',
    endDate: null,
    priceAfterDiscount: 27.95,
  },
  {
    id: 'bf8a2d7a-4faf-4105-8f00-ce39f9eea9af',
    discountValue: 25,
    discountType: 'percentage',
    name: '25 percent seed company 1 if more than 10',
    requiredQuantity: 10,
    startDate: '2025-03-17T14:13:59.083Z',
    endDate: null,
    priceAfterDiscount: 41.92,
  },
];

export const WithLoading: Story = {
  render: (args) => {
    const [product, setProduct] = useState<ShoppingCartItemProps>({ ...args, unitPrice: 0 });
    const [isLoading, setIsLoading] = useState(true);

    useEffect(() => {
      setTimeout(() => {
        setProduct({ ...product, unitPrice: 100 });
        setIsLoading(false);
      }, 2000);
    }, []);

    const handleIncrement = () => {
      setIsLoading(true);
      setProduct({ ...product, quantity: product.quantity + 1, unitPrice: 0 });
      setTimeout(() => {
        if (product.unitPrice !== null) {
          setProduct({ ...product, quantity: product.quantity + 1, subtotal: product.unitPrice * (product.quantity + 1) });
          setIsLoading(false);
        }
      }, 2000);
    };

    const handleDecrement = () => {
      setProduct({ ...product, quantity: product.quantity - 1, unitPrice: 0 });
      setIsLoading(true);
      setTimeout(() => {
        if (product.unitPrice !== null) {
          setProduct({ ...product, quantity: product.quantity - 1, subtotal: product.unitPrice * (product.quantity - 1) });
          setIsLoading(false);
        }
      }, 2000);
    };

    const handleRemove = () => {
      action('Remove item')(product.name);
    };

    const handleQuantityChange = (id: string, newQuantity: number) => {
      setProduct({ ...product, quantity: newQuantity, unitPrice: 0 });
      setIsLoading(true);
      setTimeout(() => {
        if (product.unitPrice !== null) {
          setProduct({ ...product, quantity: newQuantity, subtotal: product.unitPrice * newQuantity });
          setIsLoading(false);
        }
      }, 2000);
    };

    return (
      <div className='pd-w-64'>
        <ShoppingCartItem
          {...product}
          onIncrement={handleIncrement}
          onDecrement={handleDecrement}
          onRemove={handleRemove}
          onQuantityChange={handleQuantityChange}
          appliedDiscount={appliedDiscount}
          applicableDiscounts={applicableDiscounts}
          isLoading={isLoading}
        />
        <ShoppingCartItem
          {...product}
          onIncrement={handleIncrement}
          onDecrement={handleDecrement}
          onRemove={handleRemove}
          onQuantityChange={handleQuantityChange}
          isLoading={isLoading}
        />

        <div className='pd-mt-4 pd-text-sm pd-text-dark-600'>
          {JSON.stringify(product)}
        </div>
      </div>
    );
  },
};
