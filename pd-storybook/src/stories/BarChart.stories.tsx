import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';

import { BarChartComponent } from '../components/chart/BarChart.Component';
import Theme from '../configurations/Theme.Configuration';

const chartData = [
  { month: 'January', desktop: 186 },
  { month: 'February', desktop: 305 },
  { month: 'March', desktop: 237 },
  { month: 'April', desktop: 73 },
  { month: 'May', desktop: 209 },
  { month: 'June', desktop: 214 },
];

const chartDataMultiple = [
  { month: 'January', desktop: 186, mobile: 80 },
  { month: 'February', desktop: 305, mobile: 200 },
  { month: 'March', desktop: 237, mobile: 120 },
  { month: 'April', desktop: 73, mobile: 190 },
  { month: 'May', desktop: 209, mobile: 130 },
  { month: 'June', desktop: 214, mobile: 140 },
];

const meta = {
  title: 'Components/Charts/BarChart',
  component: BarChartComponent,
  args: {
    data: chartData,
    series: [
      {
        key: 'desktop',
        label: 'Desktop',
        color: Theme.colors.primary,
      },
    ],
    xAxisKey: 'month',
    yAxisKey: 'desktop',
    tickLength: 3,
    marginLeft: 20,
    marginRight: 20,
    marginTop: 0,
    marginBottom: 0,
    isAnimationActive: false,
    tickStyles: {},
    tickWidth: 0,
    cartesianGrid: false,
    vertical: false,
    className: '',
  },
  parameters: {
    layout: 'centered',
    key: 'BarChart',
    docs: {
      disable: false,
    },
  },
  argTypes: {
    tickWidth: {
      control: {
        type: 'number',
      },
    },
    cartesianGrid: {
      control: {
        type: 'boolean',
      },
    },
    vertical: {
      control: {
        type: 'boolean',
      },
    },
  },
  decorators: [
    (Story) => (
      <div className='pd-flex pd-justify-center pd-align-middle pd-h-[250px] pd-w-full'>
        <Story />
      </div>
    ),
  ],
} as Meta<typeof BarChartComponent>;

type Story = StoryObj<typeof BarChartComponent>;

export const Default: Story = {};

export const Multiple: Story = {
  args: {
    data: chartDataMultiple,
    series: [
      {
        key: 'desktop',
        label: 'Desktop',
        color: Theme.colors.primary,
      },
      {
        key: 'mobile',
        label: 'Mobile',
        color: Theme.colors.secondary,
      },
    ],
    isAnimationActive: true,
  },
  parameters: {
    docs: {
      disable: true,
    },
  },
};

export const Vertical: Story = {
  args: {
    data: [
      { name: 'Client A', sales: 120 },
      { name: 'Client B', sales: 90 },
      { name: 'Client C', sales: 60 },
      { name: 'Client D', sales: 30 },
      { name: 'Client E', sales: 10 },
    ],
    series: [
      {
        key: 'sales',
        label: 'Sales',
        color: Theme.colors.primary,
      },
    ],
    vertical: true,
    yAxisKey: 'name',
    xAxisKey: 'sales',
    tickWidth: 60,
    tickStyles: { fontSize: '10px' },
    tickLength: 10,
    isAnimationActive: true,
  },
  parameters: {
    docs: {
      disable: true,
    },
  },
};

export const LongLabels: Story = {
  args: {
    data: [
      { month: 'Very long January', desktop: 100 },
      { month: 'Extremely long February', desktop: 200 },
      { month: 'March with long text', desktop: 150 },
    ],
    series: [
      {
        key: 'desktop',
        label: 'Desktop',
        color: Theme.colors.primary,
      },
    ],
    xAxisKey: 'month',
    yAxisKey: 'desktop',
    tickWidth: 90,
    tickStyles: { fontSize: '10px' },
    tickLength: 15,
    isAnimationActive: true,
  },
  parameters: {
    docs: {
      disable: true,
    },
  },
};

export const CustomTickStyles: Story = {
  args: {
    data: chartData,
    series: [
      {
        key: 'desktop',
        label: 'Desktop',
        color: Theme.colors.primary,
      },
    ],
    xAxisKey: 'month',
    yAxisKey: 'desktop',
    tickWidth: 80,
    tickStyles: { fontSize: '14px', fill: Theme.colors.secondary },
    tickLength: 6,
    isAnimationActive: true,
  },
  parameters: {
    docs: {
      disable: true,
    },
  },
};

export default meta;
