// eslint-disable-next-line import/no-extraneous-dependencies
import { action } from '@storybook/addon-actions';
import type { Meta, StoryObj } from '@storybook/react';
import { fn } from '@storybook/test';
import React from 'react';

import { ButtonProps } from '../components/button/Button.Component';
import { FormComponent, FormInput } from '../components/form/Form.Component';
import { FormInputType, InputComponent } from '../components/input/Input.Component';

const defaultInputs: FormInput[] = [
  {
    label: 'Username',
    component: InputComponent,
    props: {
      name: 'username',
      value: '',
      className: 'pd-mt-2',
      onChange: fn(),
      placeholder: 'Write your username',
    },
  },
  {
    label: 'Email',
    component: InputComponent,
    props: {
      inputType: FormInputType.Email,
      name: 'email',
      value: '',
      className: 'pd-mt-2',
      onChange: fn(),
      placeholder: 'Write your email',
    },
  },
  {
    label: 'Password',
    component: InputComponent,
    props: {
      inputType: FormInputType.Password,
      name: 'password',
      value: '',
      className: 'pd-mt-2',
      onChange: fn(),
      placeholder: 'Write your password',
    },
  },
];

const defaultBtn: ButtonProps = {
  children: 'Send',
  className: '!pd-w-full',
  onClick: (e) => { e.preventDefault(); },
};

const applyInputActions = (inputs: Array<FormInput>) => inputs.map((input) => ({
  ...input,
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => {
    e.persist();
    action(`onChange ${input.props.name}`)(e.target.value);
  },
}));

const meta = {
  title: 'Components/form/FormComponent',
  component: FormComponent,
  parameters: {
    layout: 'centered',
  },
  args: {
    inputs: applyInputActions(defaultInputs),
    btnSubmit: defaultBtn,
    className: '',
  },
  argTypes: {
    inputs: {
      control: 'object',
      description: 'Array of input configurations',
    },
    btnSubmit: { control: 'object' },
    className: { control: 'text' },
  },
} as Meta<typeof FormComponent>;

export default meta;

type Story = StoryObj<typeof FormComponent>;

export const Default: Story = {};

export const Horizontal: Story = {
  args: {
    orientation: 'horizontal',
  },
};

export const FourColumns: Story = {
  args: {
    ...Default.args,
    orientation: 'horizontal',
    columnsNumber: 4,
  },
};

export const ModifiedByClassName: Story = {
  args: {
    className: 'pd-bg-dark-100 pd-p-8 pd-rounded',
  },
};
