import type { Meta, StoryObj } from '@storybook/react';

import { InfoTips, InfoTipsItems } from '../components/infoTips/InfoTips.Component';

const defaultItems: InfoTipsItems[] = [
  {
    id: '1',
    subtitle: 'Soy el primer artículo',
    text: 'Esto es un texto dummie',
    icon: 'airplane',
  },
  {
    id: '2',
    subtitle: 'Soy el segundo artículo',
    text: 'Esto es un texto dummie, puede ser un poco más largo para ver como se comporta',
    icon: 'card',
  },
  {
    id: '3',
    subtitle: 'Soy el tercer artículo',
    text: 'Esto es un texto dummie',
    icon: 'armchair',
  },
  {
    id: '4',
    subtitle:
        'Soy el cuarto artículo un poco más largo para ver como se comporta',
    text: 'Esto es un texto dummie, puede ser corto. Este será más largo para ver como se ocupan dos o tres líneas en el ejemplo.',
    icon: 'video',
  },
];

const meta: Meta<typeof InfoTips> = {
  title: 'Components/InfoTips',
  component: InfoTips,
  parameters: {
    layout: 'centered',
  },
  args: {
    title: 'Tener el control total de tu inventario es Pensar Smart',
    items: defaultItems,
    className: '',
  },
  argTypes: {
    title: {
      control: 'text',
    },
    items: {
      control: 'object',
    },
    className: {
      control: 'text',
    },
  },
} as Meta <typeof InfoTips>;

export default meta;

type Story = StoryObj<typeof InfoTips>;

export const Default: Story = {};
