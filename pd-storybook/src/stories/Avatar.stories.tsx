import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { fn } from '@storybook/test';

import { Avatar } from '../components/avatar/Avatar.Component';

const meta = {
  title: 'Components/Avatar',
  component: Avatar,
  parameters: {
    layout: 'centered',
  },
  args: {
    // eslint-disable-next-line max-len
    src: 'https://imgs.search.brave.com/6m3xgQN3DlbdTGDHkHgGvbXk5QyaqyaVM6NfkR1Crj0/rs:fit:860:0:0:0/g:ce/aHR0cHM6Ly90NC5m/dGNkbi5uZXQvanBn/LzA3LzQxLzcxLzkz/LzM2MF9GXzc0MTcx/OTM5NF9DOUJQM1li/aVhTSjdXc3BTREx0/S2RZeFpLS1dsZjBK/ei5qcGc',
    name: '<PERSON>',
    tooltip: true,
    tooltipProps: {
      position: 'top',
    },
    size: 100,
    border: {
      color: 'gray',
      width: '3px',
    },
    onClick: fn(),
  },
  argTypes: {
    name: { control: 'text' },
    tooltip: { control: 'boolean' },
    tooltipProps: {
      control: 'object',
      required: false,
      mapping: {
        position: {
          control: {
            type: 'select',
            options: ['top', 'bottom', 'left', 'right'],
          },
        },
      },
    },
    size: { control: 'number' },
    border: {
      control: 'object',
      required: false,
      mapping: {
        color: {
          control: 'text',
          required: false,
        },
        width: {
          control: 'text',
          required: false,
        },
      },
    },
    src: { control: 'text' },
    onClick: fn(),
  },
} as Meta<typeof Avatar>;

type Story = StoryObj<typeof Avatar>;

export const Default: Story = {
  render: (args) => (
    <Avatar
      {...args}
      />
  ),
};

export const NoImageAvatar: Story = {
  render: (args) => (
    <Avatar
      {...args}
      src=''

      />
  ),
};

export const DiferentSizes:Story = {
  render: (args) => (
    <div className='pd-flex pd-gap-2 pd-items-center'>
      <Avatar
        {...args}
        size={100}

      />

      <Avatar
        {...args}
        size={80}

      />

      <Avatar
        {...args}
        size={50}

      />
    </div>
  ),
};

export const SquareAvatar: Story = {
  render: (args) => (
    <Avatar
      {...args}
      shape='square'
      />
  ),
};

export const DiferentSquareSizes:Story = {
  render: (args) => (
    <div className='pd-flex pd-gap-2 pd-items-center'>
      <Avatar
        {...args}
        size={100}
        shape='square'
      />

      <Avatar
        {...args}
        size={80}
        shape='square'
      />

      <Avatar
        {...args}
        size={50}
        shape='square'
      />
    </div>
  ),
};

export const Outline:Story = {
  render: (args) => (
    <div className='pd-flex pd-gap-2 pd-items-center'>
      <Avatar
        {...args}
        size={100}
        border={{
          color: 'blue',
          width: '3px',
        }}
      />

      <Avatar
        {...args}
        size={80}
        border={{
          color: 'gray',
          width: '2px',
        }}
      />

      <Avatar
        {...args}
        size={50}
        border={{
          color: 'red',
          width: '1px',
        }}
      />

    </div>
  ),
};

export default meta;
