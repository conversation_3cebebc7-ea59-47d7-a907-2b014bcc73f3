import { action } from '@storybook/addon-actions';
import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { fn } from '@storybook/test';
import { useState } from 'react';

import { SearchBox } from '../components/searchBox/SearchBox.Component';

const meta = {
  title: 'Components/Input/SearchBox',
  component: SearchBox,
  parameters: {
    layout: 'centered',
  },
  args: {
    placeholder: 'Search...',
    suggestions: [],
    onSearchChange: action('OnSearchChange'),
    isLoading: false,
    className: '',
  },
  argTypes: {
    placeholder: { control: 'text' },
    isLoading: { control: 'boolean' },
    className: { control: 'text' },
  },
} as Meta<typeof SearchBox>;

export default meta;
type Story = StoryObj<typeof SearchBox>;

const sampleData = [
  { id: 1, title: '<PERSON><PERSON><PERSON>' },
  { id: 2, title: '<PERSON><PERSON>' },
  { id: 3, title: '<PERSON><PERSON><PERSON>' },
  { id: 4, title: '<PERSON>asco' },
  { id: 5, title: '<PERSON>ambu<PERSON><PERSON>' },
  { id: 6, title: '<PERSON><PERSON>' },
  { id: 7, title: '<PERSON><PERSON>' },
  { id: 8, title: '<PERSON>' },
  { id: 9, title: '<PERSON><PERSON>' },
  { id: 10, title: 'Mango' },
  { id: 11, title: 'Naranja' },
  { id: 12, title: 'Papaya' },
  { id: 13, title: 'Pera' },
  { id: 14, title: 'Piña' },
  { id: 15, title: 'Sandía' },
];

const searchDatabase = (query: string) => new Promise<typeof sampleData>((resolve) => {
  setTimeout(() => {
    const results = sampleData.filter((item) => item.title.toLowerCase().includes(query.toLowerCase()));
    resolve(results);
  }, 1000);
});

export const Default: Story = {
  args: {
    placeholder: 'Search for fruits...',
  },
};

export const Basic: Story = {
  args: {
    placeholder: 'Simple search...',
    onValueChange: fn(),
  },
};

export const WithLiveSearch: Story = {
  render: (args) => {
    const [searchTerm, setSearchTerm] = useState('');

    return (
      <div className="pd-flex pd-flex-col pd-gap-4">
        <SearchBox
          {...args}
          placeholder="Type to search..."
          onValueChange={setSearchTerm}
        />
        <div>Current search term: {searchTerm}</div>
      </div>
    );
  },
};

export const Interactive: Story = {
  render: (args) => {
    const [suggestions, setSuggestions] = useState<{ id: number; title: string }[]>([]);
    const [isLoading, setIsLoading] = useState(false);
    const [currentValue, setCurrentValue] = useState('');
    const [selectedSuggestion, setSelectedSuggestion] = useState<string>('');

    const handleSearchChange = async (value: string) => {
      setCurrentValue(value);

      if (value.trim() === '') {
        setSuggestions([]);
        return;
      }

      setIsLoading(true);
      try {
        const results = await searchDatabase(value);
        setSuggestions(results);
      } catch (error) {
        fn()(error);
        setSuggestions([]);
      } finally {
        setIsLoading(false);
      }
    };

    return (
      <div className="pd-flex pd-flex-col pd-gap-4">
        <SearchBox
          {...args}
          placeholder="Search fruits..."
          suggestions={suggestions}
          isLoading={isLoading}
          onValueChange={handleSearchChange}
          onSearchChange={(suggestion) => {
            setSelectedSuggestion(suggestion ? suggestion.title : '');
            setCurrentValue(suggestion ? suggestion.title : '');
            setSuggestions([]);
          }}
        />
        <div className="pd-text-sm pd-text-gray-600">
          <p>Current search: {currentValue}</p>
          <p>Selected suggestion: {selectedSuggestion}</p>
        </div>
      </div>
    );
  },
};

const CustomResulst = ({ title, subTitle }: {title: string, subTitle: string}) => (
  <div className="pd-flex pd-items-center pd-gap-4">
    <div className='pd-h-8 pd-w-8 pd-bg-fadedRed pd-rounded-full pd-flex pd-items-center pd-justify-center pd-text-lg pd-text-primary'>
      {title[0]}
    </div>
    <div className='pd-flex pd-flex-col'>
      <div className='pd-text-xsm'>{title}</div>
      <div className='pd-text-xxsm pd-text-dark-500'>{subTitle}</div>
    </div>
  </div>
);

export const InteractiveWithCustomResults: Story = {
  render: (args) => {
    const [suggestions, setSuggestions] = useState<{ id: number; title: string }[]>([]);
    const [isLoading, setIsLoading] = useState(false);
    const [currentValue, setCurrentValue] = useState('');
    const [selectedSuggestion, setSelectedSuggestion] = useState<string>('');

    const handleSearchChange = async (value: string) => {
      setCurrentValue(value);

      if (value.trim() === '') {
        setSuggestions([]);
        return;
      }

      setIsLoading(true);
      try {
        const results = await searchDatabase(value);
        const customResults = results.map((item) => ({
          ...item,
          subTitle: 'This is a custom result',
          renderer: CustomResulst,
        }));
        setSuggestions(customResults);
      } catch (error) {
        fn()(error);
        setSuggestions([]);
      } finally {
        setIsLoading(false);
      }
    };

    return (
      <div className="pd-flex pd-flex-col pd-gap-4 pd-w-[700px]">
        <SearchBox
          {...args}
          loadingLabel='Buscando...'
          pressEnterLabel='Presiona Enter para buscar'
          placeholder="Search fruits..."
          suggestions={suggestions}
          isLoading={isLoading}
          onValueChange={handleSearchChange}
          onSearchChange={(suggestion) => {
            setSelectedSuggestion(suggestion ? suggestion.title : '');
            setCurrentValue(suggestion ? suggestion.title : '');
            setSuggestions([]);
          }}
        />
        <div className="pd-text-sm pd-text-gray-600">
          <p>Current search: {currentValue}</p>
          <p>Selected suggestion: {selectedSuggestion}</p>
        </div>
      </div>
    );
  },
};
