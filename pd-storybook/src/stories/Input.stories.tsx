import type { Meta, StoryObj } from '@storybook/react';
import { fn } from '@storybook/test';

import { FormInputType, InputComponent } from '../components/input/Input.Component';

const meta = {
  title: 'Components/form/Input',
  component: InputComponent,
  parameters: {
    layout: 'centered',
  },
  args: {
    onChange: fn(),
    inputType: FormInputType.Text,
    name: 'text',
    placeholder: 'Write here',
    error: false,
    inputClassName: '',
    textAlign: 'left',
  },
  argTypes: {
    inputType: { control: 'select' },
    name: { control: 'text' },
    placeholder: { control: 'text' },
    inputClassName: { control: 'text' },
    error: { control: 'boolean' },
    textAlign: {
      control: 'select',
      options: ['left', 'right', 'center'],
    },
  },
} as Meta<typeof InputComponent>;

type Story = StoryObj<typeof InputComponent>;

export const Text: Story = {
  args: {
    label: 'Text',
  },
};

export const Password: Story = {
  args: {
    ...Text,
    inputType: FormInputType.Password,
    placeholder: 'Write your password',
  },
};

export const Email: Story = {
  args: {
    ...Text,
    inputType: FormInputType.Email,
    placeholder: 'Write your email',
  },
};

export const TextRight: Story = {
  args: {
    ...Text,
    textAlign: 'right',
  },
};

export const WithCustomClassname : Story = {
  args: {
    ...Text,
    placeholder: '',
    inputClassName: '!pd-h-20 pd-bg-fadedGreen !pd-border-2 !pd-border-solid !pd-border-positive !pd-rounded-lg',
  },
};

export default meta;
