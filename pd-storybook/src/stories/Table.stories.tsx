import type { Meta, StoryObj } from '@storybook/react';
import { fn } from '@storybook/test';
import { useState } from 'react';
import { MemoryRouter } from 'react-router-dom';

import { Table } from '../components/table/Table.Component';

const meta = {
  title: 'Components/Table',
  component: Table,
  parameters: {
    layout: 'centered',
  },
  backgrounds: {
    default: 'dark',
  },
  args: {
    totalItems: 10,
    columns: [
      { header: 'Nombre', dataKey: 'name' },
      { header: 'Edad', dataKey: 'age' },
      { header: 'Ciudad', dataKey: 'city' },
    ],
    rows: [
      {
        id: 11,
        name: '<PERSON>',
        age: 28,
        city: 'Madrid',
      },
      {
        id: 12,
        name: '<PERSON>',
        age: 22,
        city: 'Barcelona',
      },
      {
        id: 13,
        name: '<PERSON>',
        age: 35,
        city: 'Valencia',
      },
    ],
    selectTable: true,
    actionsHeader: 'Actions',
    totalItemsHeader: 'Products',
    classname: '!pd-min-w-[800px]',
  },
  argTypes: {
    columns: { control: 'object' },
    rows: { control: 'object' },
    searchBox: {
      control: 'object',
      description: 'SearchBox component properties',
    },
    filter: {
      control: 'object',
      description: 'Filter component properties',
    },
    selectTable: {
      control: 'boolean',
    },
    actionsHeader: {
      control: 'text',
    },
    headerTitle: {
      control: 'text',
    },
  },
} as Meta<typeof Table>;

export default meta;

type Story = StoryObj<typeof Table>;

export const Default: Story = {};

export const WithMoreFields: Story = {
  args: {
    columns: [
      { header: 'Nombre', dataKey: 'name' },
      { header: 'Color', dataKey: 'color' },
      { header: 'SKU', dataKey: 'SKU' },
      { header: 'Precio', dataKey: 'price' },
      { header: 'Stock', dataKey: 'stock' },
      { header: 'created', dataKey: 'created' },
      { header: 'updated', dataKey: 'updated' },
    ],
    rows: [
      {
        id: 101,
        name: 'Producto 1',
        color: 'Rojo',
        SKU: 'SKU001',
        price: 100,
        stock: 10,
        created: '2023-01-01',
        updated: '2023-01-10',
      },
      {
        id: 102,
        name: 'Producto 2',
        color: 'Azul',
        SKU: 'SKU002',
        price: 200,
        stock: 20,
        created: '2023-01-02',
        updated: '2023-01-11',
      },
      {
        id: 103,
        name: 'Producto 3',
        color: 'Verde',
        SKU: 'SKU003',
        price: 300,
        stock: 30,
        created: '2023-01-03',
        updated: '2023-01-12',
      },
      {
        id: 104,
        name: 'Producto 4',
        color: 'Amarillo',
        SKU: 'SKU004',
        price: 400,
        stock: 40,
        created: '2023-01-04',
        updated: '2023-01-13',
      },
      {
        id: 105,
        name: 'Producto 5',
        color: 'Negro',
        SKU: 'SKU005',
        price: 500,
        stock: 50,
        created: '2023-01-05',
        updated: '2023-01-14',
      },
      {
        id: 106,
        name: 'Producto 6',
        color: 'Blanco',
        SKU: 'SKU006',
        price: 600,
        stock: 60,
        created: '2023-01-06',
        updated: '2023-01-15',
      },
      {
        id: 107,
        name: 'Producto 7',
        color: 'Naranja',
        SKU: 'SKU007',
        price: 700,
        stock: 70,
        created: '2023-01-07',
        updated: '2023-01-16',
      },
      {
        id: 1,
        name: 'Producto 8',
        color: 'Rosa',
        SKU: 'SKU008',
        price: 800,
        stock: 80,
        created: '2023-01-08',
        updated: '2023-01-17',
      },
      {
        id: 108,
        name: 'Producto 9',
        color: 'Morado',
        SKU: 'SKU009',
        price: 900,
        stock: 90,
        created: '2023-01-09',
        updated: '2023-01-18',
      },
      {
        id: 109,
        name: 'Producto 10',
        color: 'Gris',
        SKU: 'SKU010',
        price: 1000,
        stock: 100,
        created: '2023-01-10',
        updated: '2023-01-19',
      },
    ],
  },
};

export const WithoutSelectTable: Story = {
  args: {
    ...WithMoreFields.args,
    selectTable: false,
  },
};

const actions = {
  enableDisableProduct: fn(),
  editProduct: fn(),
  deleteProduct: fn(),
};

export const WithActions : Story = {

  render: (args) => (
    <MemoryRouter>
      <Table {...args} actions={actions} />
    </MemoryRouter>
  ),
};

export const WithRendererFunction: Story = {
  args: {
    columns: [
      {
        header: 'Nombre',
        dataKey: 'name',
      },
      {
        header: 'Disponible',
        dataKey: 'avaible',
        renderer: (value) => (
          <span>
            {value ? '🟢' : '🔴'}
          </span>
        ),
      },
    ],
    rows: [
      {
        id: 1,
        name: 'Alberto',
        avaible: true,
      },
      {
        id: 2,
        name: 'Andrés',
        avaible: false,
      },
    ],
  },
};

export const InteractiveSearch: Story = {
  render: (args) => {
    const [filteredRows, setFilteredRows] = useState(args.rows);

    const handleSearch = (searchTerm: string) => {
      const filtered = args.rows?.filter((row) => Object.values(row)
        .join(' ')
        .toLowerCase()
        .includes(searchTerm.toLowerCase()));
      setFilteredRows(filtered);
    };

    return (
      <Table
        {...args}
        rows={filteredRows}
        totalItems={filteredRows?.length || 0}
        searchBox={{
          placeholder: 'Search...',
          onValueChange: handleSearch,
        }}
      />
    );
  },
  args: {
    columns: [
      { header: 'Nombre', dataKey: 'name' },
      { header: 'Edad', dataKey: 'age' },
      { header: 'Ciudad', dataKey: 'city' },
    ],
    rows: [
      {
        id: 11,
        name: 'Juan',
        age: 28,
        city: 'Madrid',
      },
      {
        id: 12,
        name: 'Ana',
        age: 22,
        city: 'Barcelona',
      },
      {
        id: 13,
        name: 'Luis',
        age: 35,
        city: 'Valencia',
      },
    ],
  },
};

export const WithFilters: Story = {
  args: {
    ...WithMoreFields.args,
    filter: {
      filters: [
        {
          name: 'Color',
          key: 'color',
          options: [
            { id: 'red', label: 'Red' },
            { id: 'blue', label: 'Blue' },
            { id: 'green', label: 'Green' },
          ],
        },
        {
          name: 'Stock',
          key: 'stock',
          options: [
            { id: 'inStock', label: 'In Stock' },
            { id: 'lowStock', label: 'Low Stock' },
            { id: 'outOfStock', label: 'Out of Stock' },
          ],
        },
      ],
      buttonText: 'Filter Products',
      applyText: 'Apply Filters',
      onApply: fn(),
    },
  },
};

export const WithSearchAndFilters: Story = {
  args: {
    ...WithFilters.args,
    searchBox: {
      placeholder: 'Search products...',
      suggestions: [
        { id: 1, title: 'Product 1' },
        { id: 2, title: 'Product 2' },
        { id: 3, title: 'Product 3' },
      ],
    },
  },
};
