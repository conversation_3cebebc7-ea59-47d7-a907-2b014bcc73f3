import type { Meta, StoryObj } from '@storybook/react';
import { useState } from 'react';

import { Pagination } from '../components/pagination/Pagination.Component';

const meta: Meta<typeof Pagination> = {
  title: 'Components/Pagination',
  component: Pagination,
  parameters: {
    layout: 'centered',
  },
  args: {
    initialPage: 1,
    totalPages: 10,
    isLoading: false,
  },
  argTypes: {
    initialPage: { control: 'number' },
    totalPages: { control: 'number' },
    isLoading: { control: 'boolean' },
    onPageChange: { action: 'page changed' },
  },
};

export default meta;

type Story = StoryObj<typeof Pagination>;

export const Default: Story = {
  render: (args) => {
    const [currentPage, setCurrentPage] = useState(args.initialPage);

    const handlePageChange = (page: number) => {
      setCurrentPage(page);
      args.onPageChange(page);
    };

    return (
      <Pagination
        {...args}
        initialPage={currentPage}
        onPageChange={handlePageChange}
      />
    );
  },
};
