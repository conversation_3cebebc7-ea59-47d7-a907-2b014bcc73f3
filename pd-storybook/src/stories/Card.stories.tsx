import { action } from '@storybook/addon-actions';
import type { Meta, StoryObj } from '@storybook/react';
import { useState } from 'react';

import { Badge } from '../components/badge/Badge.Component';
import { Button } from '../components/button/Button.Component';
import { CardComponent } from '../components/card/Card.Component';
import { CardSkeleton } from '../components/card/CardSkeleton.Component';
import { CountdownTimer } from '../components/countdownTimer/CountdownTimer.Component';
import { IconImporter } from '../components/iconImporter/IconImporter.Component';
import Theme from '../configurations/Theme.Configuration';

const meta = {
  title: 'Components/Card',
  component: CardComponent,
  parameters: {
    layout: 'centered',
  },
  args: {
    title: 'Motor marca Acme para camión Ford F-350 super duty',
    price: 1000,
    imageUrl: 'https://www.betaautoparts.com/cdn/shop/files/1459839451BRABUS20BREAK20PAD20copy.jpg?v=**********&width=360',
  },
} as Meta<typeof CardComponent>;

type Story = StoryObj<typeof CardComponent>;

export const Default: Story = {
  args: {
    redirectTo: {
      to: '/',
    },
  },
  render: (props) => (
    <div className='pd-w-[400px] pd-p-8 pd-flex pd-justify-center'>
      <CardComponent {...props} />
    </div>
  ),
};

export const WithDiscount : Story = {
  args: {
    ...Default.args,
    originalPrice: 1990,
    price: 990,
  },
};

const providerChildren = <span className='pd-text-dark-500 pd-italic pd-text-xxsm'>Proveedor marca Acme</span>;

export const WithTopPriceChildren: Story = {
  args: {
    ...WithDiscount.args,
    topPriceChild: providerChildren,
  },
};

const badges = <div className='pd-flex pd-flex-col pd-gap-2'>
  {providerChildren}
  <div className='pd-flex pd-gap-2'>
    <Badge className='pd-text-dark-500 '>Motos</Badge>
    <Badge className='pd-text-dark-500 ' color={Theme.colors.fadedGreen}>Ford</Badge>
    <Badge className='pd-text-dark-500 ' color={Theme.colors.fadedViolet}>F-350</Badge>
  </div>
</div>;

export const WithTopPriceMultipleChildren: Story = {
  args: {
    ...WithDiscount.args,
    topPriceChild: badges,
  },
};

const handleClick = (e: React.MouseEvent) => {
  e.preventDefault();
  action('button-clicked')('Button clicked');
};
const actionButton = <Button variant='outlined' onClick={handleClick}><IconImporter size={20} name='shoppingCart'/></Button>;

export const WithActionButton: Story = {
  args: {
    ...WithTopPriceMultipleChildren.args,
    actionButton,
  },
};

const bottomChild = <div className='pd-w-full pd-bg-primary pd-flex pd-justify-between pd-h-6 pd-items-center pd-text-white pd-rounded-md'>
  <div className='pd-px-2 pd-text-xxsm pd-h-full pd-flex pd-items-center'>50% de ahorro</div>
  <div className='pd-bg-white pd-text-secondary pd-text-xs pd-px-2 pd-h-full pd-flex pd-items-center pd-border pd-border-primary pd-rounded-r-md'>
    <CountdownTimer initialHours={10} initialMinutes={15} initialSeconds={59} />
  </div>
</div>;

export const WithBottomPriceChildren: Story = {
  args: {
    ...WithActionButton.args,
    bottomPriceChild: bottomChild,
  },
};

const multipleChildren = <div className='pd-flex pd-flex-col pd-gap-2'>
  {badges}
  {bottomChild}
</div>;

export const WithAnotherOrder: Story = {
  args: {
    ...WithDiscount.args,
    topPriceChild: multipleChildren,
    actionButton,
  },
};

export const WithSkeletonLoading: Story = {
  args: {
    ...Default.args,
  },
  render: (props) => {
    const [isLoading, setIsLoading] = useState(true);

    setTimeout(() => {
      setIsLoading(false);
    }, 2000);

    return (
      <div className='pd-h-[350px]'>
        {isLoading ? <CardSkeleton /> : <CardComponent {...props} />}
      </div>
    );
  },
};

export const WithCustomClass: Story = {
  args: {
    ...WithSkeletonLoading.args,
    className: '!pd-bg-cyan-200 !pd-h-[350px]',
  },
};

export default meta;
