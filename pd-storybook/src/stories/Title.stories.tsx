import type { Meta, StoryObj } from '@storybook/react';

import {
  Title,
} from '../components/title/Title.Component';

const meta: Meta<typeof Title> = {
  title: 'Components/Title',
  component: Title,

  args: {
    as: 'h1',
    children: 'Default Title',
    size: 'xxlg',
    weight: 'medium',
    align: 'left',
    className: '',
  },
  argTypes: {
    children: {
      control: 'text',
    },
    as: {
      control: 'select',
    },
    size: {
      control: 'select',
    },
    weight: {
      control: 'select',
    },
    align: {
      control: 'select',
    },
    className: {
      control: 'text',
    },
  },
} as Meta<typeof Title>;

type Story = StoryObj<typeof Title>;

export const DefaultTitle: Story = {};

export const Sizes: Story = {
  args: {
    ...DefaultTitle,
    children: 'Diferent title sizes example',
  },
  render: (args) => (
    <div className="pd-flex pd-flex-col pd-gap-4">
      <Title as={args.as} size="xxsm">
        {args.children}
      </Title>
      <Title as={args.as} size="xsm">
        {args.children}
      </Title>
      <Title as={args.as} size="sm">
        {args.children}
      </Title>
      <Title as={args.as} size="base">
        {args.children}
      </Title>
      <Title as={args.as} size="mdPlus">
        {args.children}
      </Title>
      <Title as={args.as} size="lg">
        {args.children}
      </Title>
      <Title as={args.as} size="lgPlus">
        {args.children}
      </Title>
      <Title as={args.as} size="xlg">
        {args.children}
      </Title>
      <Title as={args.as} size="xxlg">
        {args.children}
      </Title>
    </div>
  ),
};

export const FontColor: Story = {
  args: {
    ...DefaultTitle,
    color: 'blue',
  },
};

export const ModifiedByClassName: Story = {
  args: {
    children: 'Modified by className',
    className: 'pd-underline pd-underline-offset-8 pd-bg-primary pd-rounded pd-p-4 !pd-text-white',
  },
};

export default meta;
