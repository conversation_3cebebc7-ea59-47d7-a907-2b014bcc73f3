import { action } from '@storybook/addon-actions';
import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { useCallback, useState } from 'react';

import { DropdownWithSearch, OptionsDropdownProps } from '../components/dropdown/DropdownWithSearch.Component';

const sampleOptions: OptionsDropdownProps[] = [
  {
    id: '1', name: '<PERSON>', src: 'https://placehold.co/36x36/E6E6FA/333333?text=AW', shape: 'circle', size: 36,
  },
  {
    id: '2', name: '<PERSON> Builder', src: 'https://placehold.co/36x36/F0E68C/333333?text=BB', shape: 'circle', size: 36,
  },
  {
    id: '3', name: '<PERSON>', src: 'https://placehold.co/36x36/ADD8E6/333333?text=CB', shape: 'circle', size: 36,
  },
  {
    id: '4', name: '<PERSON>', src: 'https://placehold.co/36x36/FFB6C1/333333?text=DP', shape: 'square', size: 36,
  },
  {
    id: '5', name: '<PERSON>', src: 'https://placehold.co/36x36/90EE90/333333?text=ES', shape: 'circle', size: 36,
  },
  {
    id: '6', name: 'Fiona Gallagher', src: 'https://placehold.co/36x36/DDA0DD/333333?text=FG', shape: 'square', size: 36,
  },
  {
    id: '7', name: 'George Costanza', src: 'https://placehold.co/36x36/FFA07A/333333?text=GC', shape: 'circle', size: 36,
  },
  {
    id: '8', name: 'Harry Potter', src: 'https://placehold.co/36x36/B0E0E6/333333?text=HP', shape: 'circle', size: 36,
  },
];

const CustomTriggerButton = ({ text = 'Open Dropdown' }: { text?: string }) => (
  <button
    type="button"
    className="pd-flex pd-items-center pd-justify-center pd-gap-2 pd-px-4 pd-py-2
      pd-bg-blue-900 pd-text-white pd-font-medium pd-rounded-lg pd-shadow-md
      hover:pd-opacity-80 focus:pd-outline-none focus:pd-ring-2 focus:pd-ring-blue-500
      focus:pd-ring-opacity-50 pd-mx-auto"
  >

    <svg xmlns="http://www.w3.org/2000/svg" className="pd-h-5 pd-w-5" viewBox="0 0 20 20" fill="currentColor">
      <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
    </svg>
    {text}
  </button>
);

const meta: Meta<typeof DropdownWithSearch> = {
  title: 'Components/DropdownWithSearch',
  component: DropdownWithSearch,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    options: { control: 'object' },
    children: { control: false },
    setSelectedOption: { action: 'setSelectedOption' },
    itemsSelected: { control: 'object' },
    showAvatar: { control: 'boolean' },
    disabled: { control: 'boolean' },
    onSearchChange: { action: 'onSearchChange' },
    loadMoreOptions: { action: 'loadMoreOptions' },
    onToggle: { action: 'onToggle' },
    position: {
      control: 'select',
      options: ['bottom', 'top', 'left', 'right', 'top-left', 'top-right', 'bottom-left', 'bottom-right'],
    },
    dropdownMinWidth: { control: 'text' },
    dropdownMaxWidth: { control: 'text' },
    customSearchbox: { control: false },
  },
  args: {
    options: sampleOptions,
    setSelectedOption: action('setSelectedOption'),
    showAvatar: true,
    disabled: false,
    position: 'bottom',
    dropdownMinWidth: '250px',
  },
};

export default meta;

type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    children: <CustomTriggerButton text="Default Dropdown" />,
  },
};

export const NoAvatar: Story = {
  args: {
    ...Default.args,
    children: <CustomTriggerButton text="No Avatars" />,
    showAvatar: false,
  },
};

export const CustomRenderer: Story = {
  args: {
    ...Default.args,
    children: <CustomTriggerButton text="Custom Renderer" />,
    options: sampleOptions.map((opt) => ({
      ...opt,
      renderer: (option: OptionsDropdownProps) => (
        <div className={`pd-p-2.5 pd-border-b pd-border-gray-200 ${option.id === '3' ? 'pd-bg-cyan-50' : 'pd-bg-transparent'}`}>
          <strong className="pd-text-blue-500">{option.name}</strong> (ID: {option.id})
          {option.id === '3' && <span className="pd-ml-2.5 pd-text-xs pd-text-green-500">✨ Special</span>}
        </div>
      ),
    })),
  },
};

export const ItemsAlreadySelected: Story = {
  args: {
    ...Default.args,
    children: <CustomTriggerButton text="Pre-selected Items" />,
    itemsSelected: [sampleOptions[1], sampleOptions[3]],
  },
};

const storyWithVerticalSpace = (storyArgs: Story['args'] = {}, minHeightClass: string = 'pd-min-h-[300px]') => (
  <div className={`pd-flex pd-justify-center pd-items-center pd-w-full pd-p-5 ${minHeightClass}`}>
    <DropdownWithSearch
      {...storyArgs}
      options={storyArgs?.options || []}
      setSelectedOption={storyArgs?.setSelectedOption || (() => {})}
    >
      {storyArgs?.children}
    </DropdownWithSearch>
  </div>
);

export const PositionTop: Story = {
  render: (args) => storyWithVerticalSpace(args, 'pd-min-h-[350px]'),
  args: {
    ...Default.args,
    children: <CustomTriggerButton text="Position Top" />,
    position: 'top',
  },
};

export const PositionLeft: Story = {
  render: (args) => storyWithVerticalSpace(args),
  args: {
    ...Default.args,
    children: <CustomTriggerButton text="Position Left" />,
    position: 'left',
    dropdownMinWidth: '280px',
  },
};

export const PositionRight: Story = {
  render: (args) => storyWithVerticalSpace(args),
  args: {
    ...Default.args,
    children: <CustomTriggerButton text="Position Right" />,
    position: 'right',
    dropdownMinWidth: '280px',
  },
};

export const PositionTopLeft: Story = {
  render: (args) => storyWithVerticalSpace(args, 'pd-min-h-[350px]'),
  args: {
    ...Default.args,
    children: <CustomTriggerButton text="Position Top-Left" />,
    position: 'top-left',
  },
};

export const PositionTopRight: Story = {
  render: (args) => storyWithVerticalSpace(args, 'pd-min-h-[350px]'),
  args: {
    ...Default.args,
    children: <CustomTriggerButton text="Position Top-Right" />,
    position: 'top-right',
  },
};

export const PositionBottomLeft: Story = {
  render: (args) => storyWithVerticalSpace(args),
  args: {
    ...Default.args,
    children: <CustomTriggerButton text="Position Bottom-Left" />,
    position: 'bottom-left',
  },
};

export const PositionBottomRight: Story = {
  render: (args) => storyWithVerticalSpace(args),
  args: {
    ...Default.args,
    children: <CustomTriggerButton text="Position Bottom-Right" />,
    position: 'bottom-right',
  },
};

export const Disabled: Story = {
  args: {
    ...Default.args,
    children: <CustomTriggerButton text="Disabled Dropdown" />,
    disabled: true,
  },
};

const DropdownWithSearchAndState = (args: Story['args'] = {}) => {
  const [loadedOptions, setLoadedOptions] = useState<OptionsDropdownProps[]>(
    Array.isArray(args?.options) ? args.options : sampleOptions.slice(0, 3),
  );
  const [isLoading, setIsLoading] = useState(false);

  const handleSearch = useCallback((searchValue: string) => {
    action('onSearchChange')(searchValue);

    const filtered = sampleOptions.filter((opt) => opt.name?.toLowerCase().includes(searchValue.toLowerCase()));
    setLoadedOptions(filtered);
  }, []);

  const handleLoadMore = useCallback(() => {
    action('loadMoreOptions')();
    if (isLoading || loadedOptions.length >= sampleOptions.length) return;

    setIsLoading(true);
    setTimeout(() => {
      const currentLength = loadedOptions.length;
      const nextBatch = sampleOptions.slice(currentLength, currentLength + 3);
      setLoadedOptions((prev) => [...prev, ...nextBatch]);
      setIsLoading(false);
    }, 1000);
  }, [isLoading, loadedOptions.length]);

  return (
    <DropdownWithSearch
      {...args}
      options={loadedOptions}
      onSearchChange={handleSearch}
      loadMoreOptions={args?.loadMoreOptions ? handleLoadMore : undefined}
      setSelectedOption={args?.setSelectedOption || (() => {})}
    >
      {args?.children}
    </DropdownWithSearch>
  );
};

export const WithSearchCallback: Story = {
  render: (args) => <DropdownWithSearchAndState {...args} />,
  args: {
    ...Default.args,
    children: <CustomTriggerButton text="Search Callback" />,
    options: sampleOptions.slice(0, 3),
  },
};

export const WithLoadMore: Story = {
  render: (args) => <DropdownWithSearchAndState {...args} loadMoreOptions={action('loadMoreOptions')} />,
  args: {
    ...Default.args,
    children: <CustomTriggerButton text="Load More Options" />,
    options: sampleOptions.slice(0, 3),
  },
};

export const WithCustomSearchBox: Story = {
  args: {
    ...Default.args,
    children: <CustomTriggerButton text="Custom SearchBox" />,
    customSearchbox: (
      <input
        type="text"
        placeholder="Soy un buscador personalizado!"
        onChange={(e) => action('customSearchChange')(e.target.value)}
        className="pd-w-[calc(100%-1rem)] pd-p-2.5 pd-m-2 pd-border pd-border-blue-500 pd-rounded pd-box-border"
      />
    ),
  },
};

export const FewOptions: Story = {
  args: {
    ...Default.args,
    children: <CustomTriggerButton text="Few Options" />,
    options: sampleOptions.slice(0, 2),
  },
};

export const NoOptions: Story = {
  args: {
    ...Default.args,
    children: <CustomTriggerButton text="No Options" />,
    options: [],
  },
};

export const DisplaySelectedOption: Story = {
  render: (args) => {
    const [selected, setSelected] = useState<OptionsDropdownProps | null>(null);

    const handleSelectOption = (option: OptionsDropdownProps) => {
      setSelected(option);
      action('setSelectedOption')(option);
    };

    return (
      <div className="pd-flex pd-items-start pd-gap-5 pd-w-full pd-justify-center">
        <div className="pd-flex-shrink-0"> {/* Evita que el dropdown se encoja */}
          <DropdownWithSearch
            {...args}
            options={args?.options || []}
            setSelectedOption={handleSelectOption}
          >
            {args?.children}
          </DropdownWithSearch>
        </div>
        {selected && (
          <div className="pd-mt-0 pd-flex-grow pd-max-w-[400px]"> {/* Permite que el JSON crezca pero con un máximo */}
            <h4 className="pd-mt-0 pd-mb-1">Opción Seleccionada (JSON):</h4>
            <pre className="pd-bg-gray-50 pd-p-2.5 pd-rounded pd-border pd-border-gray-300 pd-whitespace-pre-wrap pd-break-all pd-max-h-[300px] pd-overflow-y-auto">
              {JSON.stringify(selected, null, 2)}
            </pre>
          </div>
        )}
      </div>
    );
  },
  args: {
    ...Default.args,
    children: <CustomTriggerButton text="Seleccionar Opción" />,
  },
  parameters: {
    layout: 'padded',
  },
};

export const AllPositions: Story = {
  render: () => (
    <div className="pd-grid pd-grid-cols-2 pd-gap-10 pd-p-8 pd-mb-20 pd-w-full pd-max-w-3xl">
      <h2 className="pd-col-span-2 pd-text-xl pd-font-bold pd-text-center pd-mb-6">Todas las posiciones del Dropdown</h2>

      <div className="pd-border pd-border-gray-200 pd-rounded-lg pd-p-4 pd-flex pd-flex-col pd-items-center">
        <h3 className="pd-mb-4 pd-font-medium">bottom</h3>
        <div className="pd-w-full pd-h-40 pd-flex pd-justify-center pd-items-start">
          <DropdownWithSearch
            options={sampleOptions.slice(0, 3)}
            setSelectedOption={action('setSelectedOption')}
            position="bottom"
          >
            <CustomTriggerButton text="Bottom" />
          </DropdownWithSearch>
        </div>
      </div>

      <div className="pd-border pd-border-gray-200 pd-rounded-lg pd-p-4 pd-flex pd-flex-col pd-items-center">
        <h3 className="pd-mb-4 pd-font-medium">bottom-left</h3>
        <div className="pd-w-full pd-h-40 pd-flex pd-justify-center pd-items-start">
          <DropdownWithSearch
            options={sampleOptions.slice(0, 3)}
            setSelectedOption={action('setSelectedOption')}
            position="bottom-left"
          >
            <CustomTriggerButton text="Bottom-Left" />
          </DropdownWithSearch>
        </div>
      </div>

      <div className="pd-border pd-border-gray-200 pd-rounded-lg pd-p-4 pd-flex pd-flex-col pd-items-center">
        <h3 className="pd-mb-4 pd-font-medium">bottom-right</h3>
        <div className="pd-w-full pd-h-40 pd-flex pd-justify-center pd-items-start">
          <DropdownWithSearch
            options={sampleOptions.slice(0, 3)}
            setSelectedOption={action('setSelectedOption')}
            position="bottom-right"
          >
            <CustomTriggerButton text="Bottom-Right" />
          </DropdownWithSearch>
        </div>
      </div>

      <div className="pd-border pd-border-gray-200 pd-rounded-lg pd-p-4 pd-flex pd-flex-col pd-items-center">
        <h3 className="pd-mb-4 pd-font-medium">top</h3>
        <div className="pd-w-full pd-h-40 pd-flex pd-justify-center pd-items-end">
          <DropdownWithSearch
            options={sampleOptions.slice(0, 3)}
            setSelectedOption={action('setSelectedOption')}
            position="top"
          >
            <CustomTriggerButton text="Top" />
          </DropdownWithSearch>
        </div>
      </div>

      <div className="pd-border pd-border-gray-200 pd-rounded-lg pd-p-4 pd-flex pd-flex-col pd-items-center">
        <h3 className="pd-mb-4 pd-font-medium">top-left</h3>
        <div className="pd-w-full pd-h-40 pd-flex pd-justify-center pd-items-end">
          <DropdownWithSearch
            options={sampleOptions.slice(0, 3)}
            setSelectedOption={action('setSelectedOption')}
            position="top-left"
          >
            <CustomTriggerButton text="Top-Left" />
          </DropdownWithSearch>
        </div>
      </div>

      <div className="pd-border pd-border-gray-200 pd-rounded-lg pd-p-4 pd-flex pd-flex-col pd-items-center">
        <h3 className="pd-mb-4 pd-font-medium">top-right</h3>
        <div className="pd-w-full pd-h-40 pd-flex pd-justify-center pd-items-end">
          <DropdownWithSearch
            options={sampleOptions.slice(0, 3)}
            setSelectedOption={action('setSelectedOption')}
            position="top-right"
          >
            <CustomTriggerButton text="Top-Right" />
          </DropdownWithSearch>
        </div>
      </div>

      <div className="pd-border pd-border-gray-200 pd-rounded-lg pd-p-4 pd-flex pd-flex-col pd-items-center">
        <h3 className="pd-mb-4 pd-font-medium">left</h3>
        <div className="pd-w-full pd-h-40 pd-flex pd-justify-end pd-items-center">
          <DropdownWithSearch
            options={sampleOptions.slice(0, 3)}
            setSelectedOption={action('setSelectedOption')}
            position="left"
            dropdownMinWidth="200px"
          >
            <CustomTriggerButton text="Left" />
          </DropdownWithSearch>
        </div>
      </div>

      <div className="pd-border pd-border-gray-200 pd-rounded-lg pd-p-4 pd-flex pd-flex-col pd-items-center">
        <h3 className="pd-mb-4 pd-font-medium">right</h3>
        <div className="pd-w-full pd-h-40 pd-flex pd-justify-start pd-items-center">
          <DropdownWithSearch
            options={sampleOptions.slice(0, 3)}
            setSelectedOption={action('setSelectedOption')}
            position="right"
            dropdownMinWidth="200px"
          >
            <CustomTriggerButton text="Right" />
          </DropdownWithSearch>
        </div>
      </div>
    </div>
  ),
  parameters: {
    layout: 'fullscreen',
  },
};
