import type { <PERSON>a, StoryObj } from '@storybook/react';

import { RouteLink } from '../components/routeLink/RouteLink.Component';

const meta: Meta<typeof RouteLink> = {
  title: 'Components/RouteLink',
  component: RouteLink,

  args: {
    to: '/',
    children: 'Default RouteLink',
    weight: 'medium',
    className: '',
    underline: false,
    color: '',
    target: '_self',
  },
  argTypes: {
    children: {
      control: 'text',
    },
    size: {
      control: 'select',
    },
    weight: {
      control: 'select',
    },
    className: {
      control: 'text',
    },
    color: {
      control: 'text',
    },
    underline: {
      control: 'boolean',
    },
    target: {
      control: 'text',
    },
  },
} as Meta<typeof RouteLink>;

type Story = StoryObj<typeof RouteLink>;

export const DefaultRoutelink: Story = {};

export const Underline: Story = {
  args: {
    ...DefaultRoutelink,
    underline: true,
  },
};

export const FontColor: Story = {
  args: {
    ...DefaultRoutelink,
    color: 'blue',
  },
};

export const External: Story = {
  args: {
    ...DefaultRoutelink,
    target: '_blank',
  },
};

export const Sizes: Story = {
  args: {
    ...DefaultRoutelink,
    children: 'Diferent routelink sizes example',
  },
  render: (args) => (
    <div className="pd-flex pd-flex-col pd-gap-4">
      <RouteLink size="xxsm" {...args}>
        {args.children}
      </RouteLink>
      <RouteLink size="xsm" {...args}>
        {args.children}
      </RouteLink>
      <RouteLink size="sm" {...args}>
        {args.children}
      </RouteLink>
      <RouteLink size="base" {...args}>
        {args.children}
      </RouteLink>
      <RouteLink size="mdPlus" {...args}>
        {args.children}
      </RouteLink>
      <RouteLink size="lg" {...args}>
        {args.children}
      </RouteLink>
      <RouteLink size="lgPlus" {...args}>
        {args.children}
      </RouteLink>
      <RouteLink size="xlg" {...args}>
        {args.children}
      </RouteLink>
      <RouteLink size="xxlg" {...args}>
        {args.children}
      </RouteLink>
    </div>
  ),
};

export const ModifiedByClassName: Story = {
  args: {
    ...DefaultRoutelink,
    children: 'Modified by className',
    className: 'pd-bg-primary pd-rounded pd-p-4 !pd-text-white',
  },
};

export default meta;
