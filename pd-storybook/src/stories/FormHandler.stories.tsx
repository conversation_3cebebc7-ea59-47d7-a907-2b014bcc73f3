import type { Meta, StoryObj } from '@storybook/react';
import { fn } from '@storybook/test';
import { useState } from 'react';

import { Button } from '../components/button/Button.Component';
import { FileInput } from '../components/fileInput/FileInput.Component';
import { FormHandlerComponent } from '../components/form/FormHandler.Component';
import { FormInputType, InputComponent, OptionProps } from '../components/input/Input.Component';
import { SelectInput } from '../components/selectInput/SelectInput.Component';
import { TextAreaInput } from '../components/textAreaInput/TextAreaInput.Component';

const meta: Meta<typeof FormHandlerComponent> = {
  title: 'Components/form/FormHandler',
  component: FormHandlerComponent,
  parameters: {
    layout: 'centered',
  },
};

export default meta;
type Story = StoryObj<typeof FormHandlerComponent>;

// Options for the category select input
const categoryOptions: OptionProps[] = [
  { value: '', placeholder: 'Select a category' },
  { value: 'general', placeholder: 'General' },
  { value: 'support', placeholder: 'Support' },
  { value: 'feedback', placeholder: 'Feedback' },
];

export const SimpleForm: Story = {
  args: {
    onSubmit: () => { /* Handle submit data */ },
    onChange: () => { /* Handle form data changes */ },
  },
  render: (args) => {
    const [formState, setFormState] = useState<Record<string, unknown>>({});

    return (
      <div className="pd-flex pd-gap-4">
        <div className="pd-w-[450px]" style={{ whiteSpace: 'pre-wrap' }}>
          {JSON.stringify(formState, null, 2)}
        </div>

        <div className="pd-w-[450px]">
          <FormHandlerComponent
            {...args}
            onChange={(data) => {
              setFormState(data);
              if (args.onChange) args.onChange(data);
            }}
          >
            <div className="flex flex-col gap-4 w-full max-w-md p-6 bg-white rounded-lg shadow-md">
              <h2 className="text-xl font-bold mb-4">Form with Custom Components</h2>

              <div className="mb-4">
                <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                  Name
                </label>
                <InputComponent
                  inputType={FormInputType.Text}
                  name="name"
                  placeholder="Your name"
                  inputClassName="w-full"
                />
              </div>

              <div className="mb-4">
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                  Email
                </label>
                <InputComponent
                  inputType={FormInputType.Email}
                  name="email"
                  placeholder="<EMAIL>"
                  inputClassName="w-full"
                />
              </div>

              <div className="mb-4">
                <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-1">
                  Message
                </label>
                <TextAreaInput
                  name="message"
                  placeholder="Write your message here"
                  inputClassName="w-full"
                  rows={4}
                />
              </div>

              <div className="mb-4">
                <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-1">
                  Category
                </label>
                <SelectInput
                  name="category"
                  inputClassName="w-full"
                  options={categoryOptions}
                />
              </div>

              <Button
                btnType="submit"
                className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              >
                Submit
              </Button>
            </div>
          </FormHandlerComponent>
        </div>
      </div>
    );
  },
};

export const NestedInputs: Story = {
  args: {
    onSubmit: () => { /* Handle submit data */ },
    onChange: () => { /* Handle form data changes */ },
  },
  render: (args) => {
    const [formState, setFormState] = useState<Record<string, unknown>>({});

    return (
      <div className="pd-flex pd-gap-4">
        <div className="pd-w-[450px]" style={{ whiteSpace: 'pre-wrap' }}>
          {JSON.stringify(formState, null, 2)}
        </div>

        <div className="pd-w-[450px]">
          <FormHandlerComponent
            {...args}
            onChange={(data) => {
              setFormState(data);
              if (args.onChange) args.onChange(data);
            }}
          >
            <div className="flex flex-col gap-4 w-full max-w-md p-6 bg-white rounded-lg shadow-md">
              <h2 className="text-xl font-bold mb-4">Form with Nested Inputs</h2>

              <div className="mb-4">
                <fieldset className="border rounded-md p-4">
                  <legend className="text-sm font-medium text-gray-700 px-2">Personal Information</legend>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label htmlFor="firstName" className="block text-sm font-medium text-gray-700 mb-1">
                        First Name
                      </label>
                      <InputComponent
                        inputType={FormInputType.Text}
                        name="firstName"
                        inputClassName="w-full"
                      />
                    </div>
                    <div>
                      <label htmlFor="lastName" className="block text-sm font-medium text-gray-700 mb-1">
                        Last Name
                      </label>
                      <InputComponent
                        inputType={FormInputType.Text}
                        name="lastName"
                        inputClassName="w-full"
                      />
                    </div>
                  </div>
                </fieldset>
              </div>

              <div className="mb-4">
                <fieldset className="border rounded-md p-4">
                  <legend className="text-sm font-medium text-gray-700 px-2">Contact</legend>
                  <div className="space-y-4">
                    <div>
                      <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                        Email
                      </label>
                      <InputComponent
                        inputType={FormInputType.Email}
                        name="email"
                        inputClassName="w-full"
                      />
                    </div>
                    <div>
                      <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
                        Phone
                      </label>
                      <InputComponent
                        inputType={FormInputType.Text}
                        name="phone"
                        inputClassName="w-full"
                      />
                    </div>
                  </div>
                </fieldset>
              </div>

              <Button
                btnType="submit"
                className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              >
                Submit
              </Button>
            </div>
          </FormHandlerComponent>
        </div>
      </div>
    );
  },
};

export const FileAndCheckbox: Story = {
  args: {
    onSubmit: () => { /* Handle submit data */ },
    onChange: () => { /* Handle form data changes */ },
  },
  render: (args) => {
    const [formState, setFormState] = useState<Record<string, unknown>>({});

    return (
      <div className="pd-flex pd-gap-4">
        <div className="pd-w-[450px]" style={{ whiteSpace: 'pre-wrap' }}>
          {JSON.stringify(formState, null, 2)}
        </div>

        <div className="pd-w-[450px]">
          <FormHandlerComponent
            {...args}
            onChange={(data) => {
              setFormState(data);
              if (args.onChange) args.onChange(data);
            }}
          >
            <div className="flex flex-col gap-4 w-full max-w-md p-6 bg-white rounded-lg shadow-md">
              <h2 className="text-xl font-bold mb-4">Form with Files and Checkboxes</h2>

              <div className="mb-4">
                <label htmlFor="fullName" className="block text-sm font-medium text-gray-700 mb-1">
                  Full Name
                </label>
                <InputComponent
                  inputType={FormInputType.Text}
                  name="fullName"
                  placeholder="Enter your full name"
                  inputClassName="w-full"
                />
              </div>

              <div className="mb-4">
                <label htmlFor="profilePicture" className="block text-sm font-medium text-gray-700 mb-1">
                  Profile Picture
                </label>
                <FileInput
                  name="profilePicture"
                  placeholder="Select an image"
                  inputClassName="w-full"
                  fileInputButtonLabel="Browse"
                  className="w-full"
                />
                <p className="text-xs text-gray-500 mt-1">Allowed formats: JPG, PNG, GIF (max. 5MB)</p>
              </div>

              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">Additional Documents</label>
                <FileInput
                  name="documents"
                  placeholder="Select documents"
                  inputClassName="w-full"
                  fileInputButtonLabel="Browse"
                  className="w-full"
                />
                <p className="text-xs text-gray-500 mt-1">You can upload multiple documents (max. 10MB total)</p>
              </div>

              <div className="mb-4 space-y-2">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="newsletter"
                    name="newsletter"
                    className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                    onChange={fn()}
                  />
                  <label htmlFor="newsletter" className="ml-2 block text-sm text-gray-700">
                    Subscribe to newsletter
                  </label>
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="termsAccepted"
                    name="termsAccepted"
                    className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                    required
                    onChange={fn()}
                  />
                  <label htmlFor="termsAccepted" className="ml-2 block text-sm text-gray-700">
                    I accept the terms and conditions
                  </label>
                </div>
              </div>

              <Button
                btnType="submit"
                className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              >
                Submit Form
              </Button>
            </div>
          </FormHandlerComponent>
        </div>
      </div>
    );
  },
};

export const WithInitialValues: Story = {
  args: {
    onSubmit: () => { /* Handle submit data */ },
    onChange: () => { /* Handle form data changes */ },
  },
  render: (args) => {
    const [formState, setFormState] = useState<Record<string, unknown>>({
      // Predefined initial values
      name: 'John Smith',
      email: '<EMAIL>',
      message: 'This is a test message with initial values.',
      category: 'support',
      age: 30,
      profilePicture: 'profile_photo.jpg',
      documents: 'document.pdf',
      newsletter: true,
      termsAccepted: true,
      phone: '************',
      priority: 'high',
    });

    return (
      <div className="pd-flex pd-gap-4">
        <div className="pd-w-[450px]" style={{ whiteSpace: 'pre-wrap' }}>
          {JSON.stringify(formState, null, 2)}
        </div>

        <div className="pd-w-[450px]">
          <FormHandlerComponent
            {...args}
            onChange={(data) => {
              setFormState(data);
              if (args.onChange) args.onChange(data);
            }}
          >
            <div className="flex flex-col gap-4 w-full max-w-md p-6 bg-white rounded-lg shadow-md">
              <h2 className="text-xl font-bold mb-4">Form with Initial Values</h2>

              <div className="mb-4">
                <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                  Name
                </label>
                <InputComponent
                  inputType={FormInputType.Text}
                  name="name"
                  value={formState.name as string}
                  placeholder="Your name"
                  inputClassName="w-full"
                />
              </div>

              <div className="mb-4">
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                  Email
                </label>
                <InputComponent
                  inputType={FormInputType.Email}
                  name="email"
                  value={formState.email as string}
                  placeholder="<EMAIL>"
                  inputClassName="w-full"
                />
              </div>

              <div className="mb-4">
                <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-1">
                  Message
                </label>
                <TextAreaInput
                  name="message"
                  value={formState.message as string}
                  placeholder="Write your message here"
                  inputClassName="w-full"
                  rows={3}
                />
              </div>

              <div className="mb-4">
                <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-1">
                  Category
                </label>
                <SelectInput
                  name="category"
                  value={formState.category as string}
                  inputClassName="w-full"
                  options={categoryOptions}
                />
              </div>

              <div className="mb-4">
                <label htmlFor="age" className="block text-sm font-medium text-gray-700 mb-1">
                  Age
                </label>
                <InputComponent
                  inputType={FormInputType.Number}
                  name="age"
                  value={formState.age as number}
                  inputClassName="w-full"
                  min={0}
                  max={120}
                />
              </div>

              <div className="mb-4">
                <label htmlFor="profilePicture" className="block text-sm font-medium text-gray-700 mb-1">
                  Profile Picture
                </label>
                <FileInput
                  name="profilePicture"
                  value={formState.profilePicture as string}
                  placeholder="Select an image"
                  inputClassName="w-full"
                  fileInputButtonLabel="Browse"
                  className="w-full"
                />
              </div>

              <div className="mb-4">
                <label htmlFor="documents" className="block text-sm font-medium text-gray-700 mb-1">
                  Documents
                </label>
                <FileInput
                  name="documents"
                  value={formState.documents as string}
                  placeholder="Select documents"
                  inputClassName="w-full"
                  fileInputButtonLabel="Browse"
                  className="w-full"
                />
              </div>

              <div className="mb-4">
                <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
                  Phone
                </label>
                <InputComponent
                  inputType={FormInputType.Text}
                  name="phone"
                  value={formState.phone as string}
                  placeholder="Your phone number"
                  inputClassName="w-full"
                />
              </div>

              <div className="mb-4">
                <label htmlFor="priority" className="block text-sm font-medium text-gray-700 mb-1">
                  Priority
                </label>
                <SelectInput
                  name="priority"
                  value={formState.priority as string}
                  inputClassName="w-full"
                  options={[
                    { value: '', placeholder: 'Select a priority' },
                    { value: 'low', placeholder: 'Low' },
                    { value: 'medium', placeholder: 'Medium' },
                    { value: 'high', placeholder: 'High' },
                  ]}
                />
              </div>

              <div className="mb-4 space-y-2">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="newsletter"
                    name="newsletter"
                    checked={formState.newsletter as boolean}
                    className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                    onChange={fn()}
                  />
                  <label htmlFor="newsletter" className="ml-2 block text-sm text-gray-700">
                    Subscribe to newsletter
                  </label>
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="termsAccepted"
                    name="termsAccepted"
                    checked={formState.termsAccepted as boolean}
                    className="h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                    required
                    onChange={fn()}
                  />
                  <label htmlFor="termsAccepted" className="ml-2 block text-sm text-gray-700">
                    I accept the terms and conditions
                  </label>
                </div>
              </div>

              <Button
                btnType="submit"
                className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              >
                Save Changes
              </Button>
            </div>
          </FormHandlerComponent>
        </div>
      </div>
    );
  },
};
