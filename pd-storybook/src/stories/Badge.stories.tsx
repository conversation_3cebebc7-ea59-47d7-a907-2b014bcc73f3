import type { <PERSON>a, StoryObj } from '@storybook/react';

import { Badge, BadgeType } from '../components/badge/Badge.Component';
import Theme from '../configurations/Theme.Configuration';

const meta = {
  title: 'Components/Badge',
  component: Badge,
  parameters: {
    layout: 'centered',
  },
  args: {
    children: '',
    badgeType: BadgeType.Default,
    className: '',
  },
  argTypes: {
    children: { control: 'text' },
    badgeType: { control: 'select' },
    color: { control: 'color' },
    className: { control: 'text' },
    hasChildren: { control: 'boolean' },
  },
} as Meta<typeof Badge>;

type Story = StoryObj<typeof Badge>;

export const Default: Story = {
  render: (args) => (
    <div className='pd-flex pd-gap-4'>
      <Badge {...args}>
        Default Badge
      </Badge>
      <Badge {...args} color={`${Theme.colors.fadedPeach}`}>
        Default Badge
      </Badge>
      <Badge {...args} color={`${Theme.colors.fadedRed}`}>
        Default Badge
      </Badge>
      <Badge {...args} color={`${Theme.colors.fadedViolet}`}>
        Default Badge
      </Badge>
    </div>
  ),
};

export const Outlined: Story = {
  render: (args) => (
    <div className='pd-flex pd-gap-4'>
      <Badge {...args} badgeType={BadgeType.Outline}>
        Outlined Badge
      </Badge>
      <Badge {...args} badgeType={BadgeType.Outline} color={`${Theme.colors.negative}`}>
        Outlined Badge
      </Badge>
      <Badge {...args} badgeType={BadgeType.Outline} color={`${Theme.colors.positive}`}>
        Outlined Badge
      </Badge>
      <Badge {...args} badgeType={BadgeType.Outline} color={`${Theme.colors.secondaryBlue}`}>
        Outlined Badge
      </Badge>
    </div>
  ),
};

export const Warning: Story = {
  render: (args) => (
    <div className='pd-flex pd-gap-4 pd-items-center'>
      <Badge {...args} badgeType={BadgeType.Warning}></Badge>
      <Badge {...args} badgeType={BadgeType.Warning}>
        Warning Badge
      </Badge>
    </div>
  ),
};

export const Danger: Story = {
  render: (args) => (
    <div className='pd-flex pd-gap-4 pd-items-center'>
      <Badge {...args} badgeType={BadgeType.Danger}></Badge>
      <Badge {...args} badgeType={BadgeType.Danger}>
        Danger Badge
      </Badge>
    </div>
  ),
};

export const ActiveDisabled: Story = {
  render: (args) => (
    <div className='pd-flex pd-gap-4 pd-items-center'>
      <Badge {...args} badgeType={BadgeType.Active}></Badge>
      <Badge {...args} badgeType={BadgeType.Disabled}></Badge>
    </div>
  ),
};

export default meta;
