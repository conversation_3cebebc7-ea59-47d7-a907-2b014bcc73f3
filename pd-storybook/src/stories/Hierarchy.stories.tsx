// eslint-disable-next-line import/no-extraneous-dependencies
import type { Meta, StoryObj } from '@storybook/react';

import { Hierarchy, HierarchyComponent } from '../components/hierarchy/Hierarchy.Component';
import { hierarchyConstants } from '../components/hierarchy/Hierarchy.Constants';
import Theme from '../configurations/Theme.Configuration';

const defaultHierarchies: Hierarchy[] = [
  {
    name: 'Category1',
    hierarchies: [
      {
        name: 'SubCategory1',
        hierarchies: [
          { name: 'value1' },
        ],
      },
      {
        name: 'SubCategory2',
        hierarchies: [
          {
            name: 'subCategory3',
            hierarchies: [
              {
                name: 'subcategory4',
                hierarchies: [
                  { name: 'custom color', backgroundColor: Theme.colors.fadedYellow },
                  { name: 'custom color 2', backgroundColor: Theme.colors.fadedYellow },
                ],
              },
            ],
          },
          {
            name: 'subCategory5',
            hierarchies: [
              {
                name: 'subcategory6',
                hierarchies: [
                  { name: 'default color' },
                  { name: 'default color 2' },
                ],
              },
            ],
          },
        ],
      },
    ],
  },
  {
    name: 'Category2',
    hierarchies: [
      { name: 'Value1' },
      { name: 'Value2' },
    ],
  },
];

const meta = {
  title: 'Components/Hierarchy/HierarchyComponent',
  component: HierarchyComponent,
  parameters: {
    layout: 'centered',
  },
  args: {
    hierarchies: [],
    labelProps: {
      title: hierarchyConstants.TITLE,
      tooltip: {
        content: 'This is a tooltip to help the user with some information',
        position: 'bottom',
      },
    },
  },
  argTypes: {
    initialCategories: { control: 'object' },
    onSave: { action: 'onSave' },
    showNotification: { control: false },
  },
} as Meta<typeof HierarchyComponent>;

export default meta;

type Story = StoryObj<typeof HierarchyComponent>;

export const Empty: Story = {
  render: (args) => (
    <div className='pd-w-[450px]'>
      <HierarchyComponent
        {...args}
        hierarchies={[]}
      />
    </div>
  ),
};

export const WithHierarchies: Story = {
  render: (args) => (
    <div className='pd-w-[450px]'>
      <HierarchyComponent
        {...args}
        hierarchies={defaultHierarchies}
      />
    </div>
  ),
};
