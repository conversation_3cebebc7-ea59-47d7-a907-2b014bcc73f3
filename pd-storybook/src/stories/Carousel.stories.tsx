import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import React, { useEffect, useRef, useState } from 'react';

import type { ImageData } from '../components/carousel/Carousel.Component';

const MockIconImporter = ({ name, className }: { name: string; className?: string }) => {
  if (name === 'caretLeft') {
    return (
      <svg className={className} viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
        <path d="M15 18L9 12L15 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
      </svg>
    );
  }

  if (name === 'caretRight') {
    return (
      <svg className={className} viewBox="0 0 24 24" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
        <path d="M9 6L15 12L9 18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
      </svg>
    );
  }

  if (name === 'x') {
    return (
      <svg className={className} viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
      </svg>
    );
  }

  return <span className={className} style={{ fontSize: '10px', color: '#555' }}>[{name}]</span>;
};

const MockCircleLoader = ({ className }: { className?: string }) => (
  <div className={className} style={{
    padding: '8px', border: '2px solid #ccc', borderTopColor: '#333', borderRadius: '50%', animation: 'spin 1s linear infinite',
  }}>
    <style>{'@keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }'}</style>
  </div>
);

const mockCn = (...args: (string | undefined)[]) => args.filter(Boolean).join(' ');

const CarouselComponent = ({
  images = [],
  className,
  onRemoveImage,
  onAddImages,
  canScrollLeft = false,
  canScrollRight = false,
  onScrollLeft,
  onScrollRight,
  addImageEnabled = true,
}: {
  images: ImageData[];
  className?: string;
  onRemoveImage?: (id: string) => void;
  onAddImages?: () => void;
  canScrollLeft?: boolean;
  canScrollRight?: boolean;
  onScrollLeft?: () => void;
  onScrollRight?: () => void;
  addImageEnabled?: boolean;
  containerRef?: React.RefObject<HTMLDivElement>;
}) => (
  <div className={mockCn('pd-flex pd-flex-col pd-gap-4 pd-flex-1 pd-w-full', className)}>
    <div className="pd-flex pd-items-center pd-gap-2 pd-w-full">
      {addImageEnabled && (
      <div
        className="pd-w-24 pd-h-24 pd-border-2 pd-border-dashed pd-border-[#DFE3E8] pd-rounded-lg
                      pd-flex pd-items-center pd-justify-center pd-text-xs pd-cursor-pointer
                      pd-flex-shrink-0 hover:pd-text-[#0077B6] hover:pd-border-[#0077B6]"
        onClick={onAddImages}
          >
        <span>Seleccionar</span>
      </div>
      )}

      <div className="pd-flex-1 pd-relative pd-overflow-hidden">
        {canScrollLeft && (
        <button
          type="button"
          onClick={onScrollLeft}
          className="pd-absolute pd-left-0 pd-top-1/2 pd--translate-y-1/2 pd-z-10
                        pd-bg-white pd-bg-opacity-75 pd-p-1 pd-rounded-full pd-shadow-lg
                        pd-cursor-pointer pd-border pd-border-[#DFE3E8]"
          aria-label="Scroll left"
            >
          <MockIconImporter name="caretLeft" className="pd-w-5 pd-h-5 pd-text-[#323D52]" />
        </button>
        )}
        <div
          className="pd-flex pd-gap-2 pd-pb-0.5 pd-overflow-x-auto pd-hide-scrollbar"
          >
          {images.map((img, index) => (
            <div key={img.id} className="pd-relative pd-w-24 pd-h-24 pd-rounded-lg pd-overflow-hidden pd-flex-shrink-0">
              {img.processing ? (
                <div className='pd-bg-white pd-w-full pd-h-full pd-flex pd-flex-col pd-gap-2 pd-items-center pd-justify-center'>
                  <MockCircleLoader className='!pd-w-8 !pd-h-8'/>
                  <div className='pd-text-[10px] pd-text-[#505D75]'>Procesando...</div>
                </div>
              ) : (
                <>
                  <img src={img.url} alt={img.name} className="pd-w-full pd-h-full pd-object-cover" />
                  {onRemoveImage && (
                  <button
                    onClick={() => onRemoveImage(img.id)}
                    className="pd-absolute pd-top-0.5 pd-right-0.5 pd-bg-black pd-bg-opacity-60
                                  pd-text-white pd-border-none pd-rounded-full pd-w-5 pd-h-5 pd-text-sm
                                  pd-leading-none pd-cursor-pointer pd-flex pd-items-center pd-justify-center"
                      >
                    <MockIconImporter name='x' className="pd-w-3 pd-h-3" />
                  </button>
                  )}
                  {index === 0 && (
                  <div className='pd-bg-[#0077B6] pd-opacity-90 pd-text-white pd-text-xs pd-px-3 pd-py-1
                        pd-rounded-xl pd-absolute pd-bottom-1 pd-left-1/2 pd-transform pd--translate-x-1/2'>
                    Portada
                  </div>
                  )}
                </>
              )}
            </div>
          ))}
        </div>
        {canScrollRight && (
        <button
          type="button"
          onClick={onScrollRight}
          className="pd-absolute pd-right-0 pd-top-1/2 pd--translate-y-1/2 pd-z-10
                        pd-bg-white pd-bg-opacity-75 pd-p-1 pd-rounded-full pd-shadow-lg
                        pd-cursor-pointer pd-border pd-border-[#DFE3E8]"
          aria-label="Scroll right"
            >
          <MockIconImporter name="caretRight" className="pd-w-5 pd-h-5 pd-text-[#323D52]" />
        </button>
        )}
      </div>
    </div>
  </div>
);

const InteractiveCarousel = ({ images }: { images: ImageData[] }) => {
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(true);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  const updateScrollButtonVisibility = () => {
    if (scrollContainerRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = scrollContainerRef.current;
      setCanScrollLeft(scrollLeft > 0);
      setCanScrollRight(scrollLeft + clientWidth < scrollWidth - 10);
    }
  };

  const handleScrollLeft = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: -128, behavior: 'smooth' });
      setTimeout(updateScrollButtonVisibility, 300);
    }
  };

  const handleScrollRight = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: 128, behavior: 'smooth' });
      setTimeout(updateScrollButtonVisibility, 300);
    }
  };

  useEffect(() => {
    updateScrollButtonVisibility();
    window.addEventListener('resize', updateScrollButtonVisibility);
    return () => window.removeEventListener('resize', updateScrollButtonVisibility);
  }, []);

  return (
    <div className="pd-flex pd-flex-col pd-gap-4 pd-flex-1 pd-w-full pd-max-w-md">
      <div className="pd-flex pd-items-center pd-gap-2 pd-w-full">
        <div className="pd-flex-1 pd-relative pd-overflow-hidden">
          {canScrollLeft && (
            <button
              type="button"
              onClick={handleScrollLeft}
              className="pd-absolute pd-left-0 pd-top-1/2 pd--translate-y-1/2 pd-z-10
                          pd-bg-white pd-bg-opacity-75 pd-p-1 pd-rounded-full pd-shadow-lg
                          pd-cursor-pointer pd-border pd-border-[#DFE3E8]"
              aria-label="Scroll left"
            >
              <MockIconImporter name="caretLeft" className="pd-w-5 pd-h-5 pd-text-[#323D52]" />
            </button>
          )}
          <div
            ref={scrollContainerRef}
            className="pd-flex pd-gap-2 pd-pb-0.5 pd-overflow-x-auto pd-hide-scrollbar"
            onScroll={updateScrollButtonVisibility}
            style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
          >
            {images.map((img, index) => (
              <div key={img.id} className="pd-relative pd-w-24 pd-h-24 pd-rounded-lg pd-overflow-hidden pd-flex-shrink-0">
                <img src={img.url} alt={img.name} className="pd-w-full pd-h-full pd-object-cover" />
                {index === 0 && (
                  <div className='pd-bg-[#0077B6] pd-opacity-90 pd-text-white pd-text-xs pd-px-3 pd-py-1
                        pd-rounded-xl pd-absolute pd-bottom-1 pd-left-1/2 pd-transform pd--translate-x-1/2'>
                    Portada
                  </div>
                )}
              </div>
            ))}
          </div>
          {canScrollRight && (
            <button
              type="button"
              onClick={handleScrollRight}
              className="pd-absolute pd-right-0 pd-top-1/2 pd--translate-y-1/2 pd-z-10
                          pd-bg-white pd-bg-opacity-75 pd-p-1 pd-rounded-full pd-shadow-lg
                          pd-cursor-pointer pd-border pd-border-[#DFE3E8]"
              aria-label="Scroll right"
            >
              <MockIconImporter name="caretRight" className="pd-w-5 pd-h-5 pd-text-[#323D52]" />
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

const mockImages: ImageData[] = [
  {
    id: '1',
    url: 'https://img.freepik.com/psd-gratis/amortiguador-automoviles-renderizado-3d-fondo-transparente_363450-6625.jpg',
    name: 'portada.jpg',
  },
  {
    id: '2',
    url: 'https://img.freepik.com/fotos-premium/kit-correa-distribucion-rodillos-pared-blanca_168171-317.jpg',
    name: 'imagen2.png',
  },
  {
    id: '3',
    url: 'https://img.freepik.com/foto-gratis/composicion-diferentes-accesorios-coche_23-2149030390.jpg',
    name: 'imagen3.gif',
  },
  {
    id: '4',
    url: 'https://img.freepik.com/foto-gratis/composicion-diferentes-accesorios-coche_23-2149030439.jpg',
    name: 'imagen4.webp',
  },
];

const processingImages: ImageData[] = [
  ...mockImages,
  {
    id: '5', url: '', name: 'subiendo.jpg', processing: true,
  },
];

const tenImages: ImageData[] = [
  {
    id: '1',
    url: 'https://img.freepik.com/psd-gratis/amortiguador-automoviles-renderizado-3d-fondo-transparente_363450-6625.jpg',
    name: 'portada.jpg',
  },
  {
    id: '2',
    url: 'https://img.freepik.com/fotos-premium/kit-correa-distribucion-rodillos-pared-blanca_168171-317.jpg',
    name: 'imagen2.png',
  },
  {
    id: '3',
    url: 'https://img.freepik.com/foto-gratis/composicion-diferentes-accesorios-coche_23-2149030390.jpg',
    name: 'imagen3.gif',
  },
  {
    id: '4',
    url: 'https://img.freepik.com/foto-gratis/composicion-diferentes-accesorios-coche_23-2149030439.jpg',
    name: 'imagen4.webp',
  },
  {
    id: '5',
    url: 'https://img.freepik.com/foto-gratis/composicion-diferentes-accesorios-coche_23-2149030387.jpg',
    name: 'img5.jpg',
  },
  {
    id: '6',
    url: 'https://img.freepik.com/vector-gratis/amortiguador-realista_1284-10875.jpg',
    name: 'img6.jpg',
  },
  {
    id: '7',
    url: 'https://img.freepik.com/psd-gratis/amortiguador-automoviles-renderizado-3d-fondo-transparente_363450-6609.jpg',
    name: 'img7.jpg',
  },
  {
    id: '8',
    url: 'https://img.freepik.com/vector-gratis/composicion-accesorios-centro-servicio-automoviles_98292-7431.jpg',
    name: 'img8.jpg',
  },
  {
    id: '9',
    url: 'https://img.freepik.com/vector-gratis/servicio-reparacion-automoviles-composicion-isometrica-'
    + 'con-vista-taller-automoviles-trabajadores-proceso-reparacion-texto_1284-31879.jpg',
    name: 'img9.jpg',
  },
  {
    id: '10',
    url: 'https://img.freepik.com/vector-gratis/servicio-reparacion-automoviles-composicion-realista-'
    + 'con-set-imagenes-aisladas-herramientas-reparacion-piezas-automoviles-texto-editable_1284-29633.jpg',
    name: 'img10.jpg',
  },
];

const manyImages: ImageData[] = [
  ...mockImages,
  {
    id: '5',
    url: 'https://img.freepik.com/foto-gratis/composicion-diferentes-accesorios-coche_23-2149030387.jpg',
    name: 'img5.jpg',
  },
  {
    id: '6',
    url: 'https://img.freepik.com/vector-gratis/amortiguador-realista_1284-10875.jpg',
    name: 'img6.jpg',
  },
  {
    id: '7',
    url: 'https://img.freepik.com/psd-gratis/amortiguador-automoviles-renderizado-3d-fondo-transparente_363450-6609.jpg',
    name: 'img7.jpg',
  },
  {
    id: '8',
    url: 'https://img.freepik.com/vector-gratis/composicion-accesorios-centro-servicio-automoviles_98292-7431.jpg',
    name: 'img8.jpg',
  },
];

const meta: Meta<typeof CarouselComponent> = {
  title: 'Components/Carousel',
  component: CarouselComponent,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    images: {
      control: 'object',
      description: 'Array de objetos de imagen para mostrar en el carrusel.',
    },
    className: { control: 'text' },
    addImageEnabled: { control: 'boolean' },
    canScrollLeft: { control: 'boolean' },
    canScrollRight: { control: 'boolean' },
    onRemoveImage: { action: 'onRemoveImage' },
    onAddImages: { action: 'onAddImages' },
    onScrollLeft: { action: 'onScrollLeft' },
    onScrollRight: { action: 'onScrollRight' },
    containerRef: {
      control: false,
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  name: 'Estado por Defecto',
  args: {
    images: mockImages,
    addImageEnabled: true,
  },
};

export const WithScrolling: Story = {
  name: 'Con Botones de Scroll',
  args: {
    images: manyImages,
    addImageEnabled: true,
    canScrollLeft: true,
    canScrollRight: true,
  },
  parameters: {
    docs: {
      description: {
        story: 'Muestra el carrusel con los botones de navegación. En una implementación real, '
              + 'la visibilidad de estos botones sería controlada por el estado de scroll del contenedor.',
      },
    },
  },
};

export const WithProcessingImages: Story = {
  name: 'Con Imágenes en Procesamiento',
  args: {
    images: processingImages,
    addImageEnabled: true,
  },
};

export const EmptyState: Story = {
  name: 'Estado Vacío',
  args: {
    images: [],
    addImageEnabled: true,
  },
};

export const AddImageDisabled: Story = {
  name: 'Añadir Imagen Deshabilitado',
  args: {
    images: mockImages,
    addImageEnabled: false,
  },
};

export const LeftNavigationOnly: Story = {
  name: 'Solo Navegación Izquierda',
  args: {
    images: manyImages,
    addImageEnabled: true,
    canScrollLeft: true,
    canScrollRight: false,
  },
  parameters: {
    docs: {
      description: {
        story: 'Muestra el carrusel con solo el botón de navegación izquierdo visible, simulando que '
              + 'el usuario ha desplazado el carrusel hacia la derecha y se encuentra al final del contenido.',
      },
    },
  },
};

export const RightNavigationOnly: Story = {
  name: 'Solo Navegación Derecha',
  args: {
    images: manyImages,
    addImageEnabled: true,
    canScrollLeft: false,
    canScrollRight: true,
  },
  parameters: {
    docs: {
      description: {
        story: 'Muestra el carrusel con solo el botón de navegación derecho visible, simulando '
              + 'el estado inicial del carrusel cuando hay más imágenes a la derecha por mostrar.',
      },
    },
  },
};

export const InteractiveNavigationDemo: Story = {
  name: 'Demo Interactiva de Navegación',
  args: {
    images: manyImages,
    addImageEnabled: true,
    canScrollLeft: true,
    canScrollRight: true,
  },
  parameters: {
    docs: {
      description: {
        story: 'Muestra ambos botones de navegación y permite interactuar con ellos. Observe '
              + 'en el panel de Acciones cómo se disparan los eventos onScrollLeft y onScrollRight '
              + 'al hacer clic en los botones correspondientes.',
      },
    },
  },
};

export const TenImagesWithNavigation: Story = {
  name: 'Diez Imágenes con Navegación',
  render: () => <InteractiveCarousel images={tenImages} />,
  parameters: {
    docs: {
      description: {
        story: 'Muestra un carrusel con 10 imágenes donde solo son visibles 5 inicialmente. '
              + 'Las flechas de navegación aparecen y desaparecen automáticamente según la '
              + 'posición de desplazamiento. Al hacer clic en las flechas se desplazan las '
              + 'imágenes con una animación suave.',
      },
    },
  },
};
