import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';

import { NotificationComponent } from '../components/notification/Notification.Component';

const meta = {
  title: 'Components/Notification',
  component: NotificationComponent,
  parameters: {
    layout: 'centered',
    backgrounds: {
      default: 'dark',
    },
  },
  argTypes: {
    notifications: {
      control: 'object',
    },
  },
} as Meta<typeof NotificationComponent>;

export default meta;

type Story = StoryObj<typeof NotificationComponent>;

export const Default: Story = {
  args: {
    notifications: [
      { id: '1', message: 'Notification 1', to: '#notification/1' },
      { id: '2', message: 'Notification 2', to: '#notification/2' },
      { id: '3', message: 'Notification Very long, lets see how to handle messages with a lot of words', to: '#notification/3' },
    ],
  },
};
