import type { <PERSON>a, StoryObj } from '@storybook/react';
import { fn } from '@storybook/test';

import { Button } from '../components/button/Button.Component';

const meta = {
  title: 'Components/Button',
  component: Button,
  parameters: {
    layout: 'centered',
  },
  args: {
    children: 'Default But<PERSON>',
    variant: 'primary',
    size: 'small',
    disabled: false,
    btnType: 'button',
    className: '',
    onClick: fn(),
  },
  argTypes: {
    variant: { control: 'select' },
    size: { control: 'select' },
    children: { control: 'text' },
    disabled: { control: 'boolean' },
    btnType: { control: 'select' },
    className: { control: 'text' },
  },
} as Meta<typeof Button>;

type Story = StoryObj<typeof Button>;

export const Primary: Story = {
  args: {
    children: 'Primary Button',
  },
};

export const Outlined: Story = {
  args: {
    ...Primary,
    children: 'Outlined Button',
    variant: 'outlined',
  },
};

export const Sizes: Story = {
  render: (args) => (
    <>
      <Button {...args} className='pd-mr-4' size="small">
        Small Button
      </Button>
      <Button {...args} className='pd-mr-4' size="medium">
        Medium Button
      </Button>
      <Button {...args} className='pd-mr-4' size="large">
        Large Button
      </Button>
    </>
  ),
};

export const Disabled: Story = {
  args: {
    ...Primary,
    children: 'Disabled Button',
    disabled: true,
  },
};

export const ModifiedByClassName: Story = {
  args: {
    ...Primary,
    children: 'Modified by className',
    className: 'pd-shadow-lg !pd-bg-dark-700',
  },
};

export default meta;
