import type { Meta, StoryObj } from '@storybook/react';
import { useState } from 'react';

import { DateInputComponent } from '../components/dateInput/DateInput.Component';

const meta = {
  title: 'Components/Input/DateInput',
  component: DateInputComponent,
  parameters: {
    layout: 'centered',
  },
  args: {
    name: 'date',
    disabled: false,
    value: '',
  },
  argTypes: {
    disabled: {
      control: {
        type: 'boolean',
      },
    },
  },
} as Meta<typeof DateInputComponent>;

type Story = StoryObj<typeof DateInputComponent>;

export const Default: Story = {
  args: {
    ...meta.args,
  },
};

export const WithInitialValue: Story = {
  args: {
    ...Default.args,
    value: '2025-02-10 11:37:02.293-0500',

  },
  render: (args) => {
    const [date, setDate] = useState(args.value);
    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      setDate(e.target.value);
    };

    return (
      <div className='pd-w-[300px] pd-h-ful'>
        <DateInputComponent
          onChange={handleChange}
          value={date}
          {...args}
        />
        {date && <div className='pd-mt-4'>Selected date: {date}</div>}
      </div>
    );
  },
};

export const Disabled: Story = {
  args: {
    ...Default.args,
    disabled: true,
  },
  render: (args) => <DateInputComponent {...args} />,
};

export default meta;
