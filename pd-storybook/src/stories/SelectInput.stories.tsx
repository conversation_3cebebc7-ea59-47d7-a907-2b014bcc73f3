import type { Meta, StoryObj } from '@storybook/react';
import { fn } from '@storybook/test';

import { SelectInput } from '../components/selectInput/SelectInput.Component';

const meta = {
  title: 'Components/form/SelectInput',
  component: SelectInput,
  parameters: {
    layout: 'centered',
  },
  args: {
    name: 'select',
    inputClassName: '',
    error: false,
    options: [
      { value: '1', placeholder: 'Option 1' },
      { value: '2', placeholder: 'Option 2' },
      { value: '3', placeholder: 'Option 3' },
    ],
    onChange: fn(),
  },
  argTypes: {
    name: { control: 'text' },
    inputClassName: { control: 'text' },
    error: { control: 'boolean' },
    options: { control: 'object' },
  },
} as Meta<typeof SelectInput>;

type Story = StoryObj<typeof SelectInput>;

export const Select: Story = {};

export default meta;
