// eslint-disable-next-line import/no-extraneous-dependencies
import type { Meta, StoryObj } from '@storybook/react';
import { useCallback, useMemo, useState } from 'react';

import { EditableHierarchy, EditableHierarchyComponent } from '../components/hierarchy/EditableHierarchy.Component';
import { HierarchyComponent } from '../components/hierarchy/Hierarchy.Component';
import { hierarchyConstants } from '../components/hierarchy/Hierarchy.Constants';
import Theme from '../configurations/Theme.Configuration';
import { generateId } from '../utils/GenerateID.Util';

const defaultEditableHierarchies: EditableHierarchy[] = [
  {
    id: generateId(),
    name: 'Category1',
    title: 'CUSTOM TITLE',
    hierarchies: [
      {
        id: generateId(),
        name: 'SubCategory1',
        hierarchies: [
          { id: generateId(), name: 'value1' },
        ],
      },
      {
        id: generateId(),
        title: '',
        name: 'SubCategory2',
        hierarchies: [
          {
            id: generateId(),
            name: ' NotDeletable',
            disableDelete: true,
            hierarchies: [
              {
                id: generateId(),
                name: 'subcategory4',
                hierarchies: [
                  { id: generateId(), name: 'custom color', backgroundColor: Theme.colors.fadedYellow },
                  { id: generateId(), name: 'custom color 2', backgroundColor: Theme.colors.fadedYellow },
                ],
              },
            ],
          },
          {
            id: generateId(),
            name: 'subCategory5',
            hierarchies: [
              {
                id: generateId(),
                name: 'subcategory6',
                hierarchies: [
                  { id: generateId(), name: 'default color' },
                  { id: generateId(), name: 'default color 2' },
                ],
              },
            ],
          },
        ],
      },
    ],
  },
  {
    id: generateId(),
    name: 'Category2',
    hierarchies: [
      { id: generateId(), name: 'Value1' },
      { id: generateId(), name: 'Value2' },
    ],
  },
];

const validatedHierarchies: EditableHierarchy[] = [
  {
    id: generateId(),
    name: 'Category1',
    hierarchies: [
      {
        id: generateId(),
        name: 'SubCategory1',
        validator: (value: string) => {
          if (value.length > 5) return 'The value is too long max 5 characters';

          return undefined;
        },
        hierarchies: [
          {
            id: generateId(),
            name: 'value1',
          },
        ],
      },
      {
        id: generateId(),
        name: 'SubCategory2',
        hierarchies: [
          {
            id: generateId(),
            name: '',
            validator: (value: string) => {
              if (!value) return 'Cannot be empty';

              return undefined;
            },
          },
        ],
      },
    ],
  },
];

const meta = {
  title: 'Components/Hierarchy/EditableHierarchyComponent',
  component: EditableHierarchyComponent,
  parameters: {
    layout: 'centered',
  },
  args: {
    hierarchies: [],
    labelProps: {
      title: hierarchyConstants.TITLE,
      tooltip: {
        content: 'This is a tooltip to help the user with some information',
        position: 'bottom',
      },
    },
  },
  argTypes: {
    initialCategories: { control: 'object' },
    onSave: { action: 'onSave' },
    showNotification: { control: false },
  },
} as Meta<typeof EditableHierarchyComponent>;

export default meta;

type Story = StoryObj<typeof EditableHierarchyComponent>;

export const Empty: Story = {
  render: (args) => (
    <div className='pd-w-[450px]'>
      <EditableHierarchyComponent
        {...args}
        hierarchies={[]}
        labelProps={{ ...args.labelProps, title: hierarchyConstants.TITLE_EDIT }}
        />
    </div>
  ),
};

export const WithHierarchies: Story = {
  render: (args) => {
    const [draftHierarchies, setDraftHierarchies] = useState<EditableHierarchy[]>(defaultEditableHierarchies);

    const onChange = ((hierarchies: EditableHierarchy[]) => {
      setDraftHierarchies(hierarchies);
    });

    return (
      <div className='pd-flex pd-gap-4'>

        <HierarchyComponent
          {...args}
          hierarchies={draftHierarchies}
        />
        <div className='pd-w-[450px]'>
          <EditableHierarchyComponent
            {...args}
            hierarchies={draftHierarchies}
            labelProps={{ ...args.labelProps, title: hierarchyConstants.TITLE_EDIT }}
            onChange={onChange}
          />
        </div>
      </div>

    );
  },
};

export const WithValidations: Story = {
  render: (args) => {
    const [draftHierarchies, setDraftHierarchies] = useState<EditableHierarchy[]>(validatedHierarchies);
    const onChange = ((hierarchies: EditableHierarchy[]) => {
      setDraftHierarchies(hierarchies);
    });

    return (
      <div className='pd-flex pd-gap-4'>
        <div className='pd-w-[450px]' style={{ whiteSpace: 'pre-wrap' }}>{JSON.stringify(draftHierarchies, undefined, 2)}</div>
        <div className='pd-w-[450px]'>
          <EditableHierarchyComponent
            {...args}
            hierarchies={validatedHierarchies}
            labelProps={{ ...args.labelProps, title: hierarchyConstants.TITLE_EDIT }}
            onChange={onChange}
          />
        </div>
      </div>);
  },
};

export const withCustomAdd: Story = {
  render: (args) => {
    const [hierarchies, setHierarchies] = useState<EditableHierarchy[]>(defaultEditableHierarchies);

    const findHierarchy = useCallback((hierarchiesToSearch: EditableHierarchy[], hierarchyToFind: EditableHierarchy): EditableHierarchy | undefined => {
      let foundHierarchy: EditableHierarchy | undefined;

      for (let i = 0; i < hierarchiesToSearch.length; i += 1) {
        const hierarchy = hierarchiesToSearch[i];

        if (hierarchy.id === hierarchyToFind.id) {
          return hierarchy;
        }

        if (hierarchy.hierarchies) {
          foundHierarchy = findHierarchy(hierarchy.hierarchies, hierarchyToFind);

          if (foundHierarchy) {
            return foundHierarchy;
          }
        }
      }

      return foundHierarchy;
    }, []);

    const ChoseType = ({ show, hierarchy }: {show: boolean, hierarchy: EditableHierarchy}) => {
      const handleSelection = useCallback((type: 'type1' | 'type2') => {
        const newHierarchy = findHierarchy(hierarchies, hierarchy) as EditableHierarchy;

        newHierarchy.properties = {
          ...newHierarchy.properties,
          show: false,
        };

        newHierarchy.hierarchies = newHierarchy.hierarchies || [];

        newHierarchy.hierarchies.unshift({
          id: generateId(),
          name: `new ${type}`,
          properties: {
            type,
          },
        });

        setHierarchies([...hierarchies]);
      }, []);

      return (
        <>
          {show && <div className="pd-flex pd-flex-col pd-border pd-p-4 pd-rounded-lg pd-gap-4 pd-w-full">
            <div
              data-testid="add-type-1"
              className="pd-cursor-pointer hover:pd-text-dark-500"
              onClick={() => handleSelection('type1')}
              >
              {hierarchyConstants.TYPE_1_LABEL}
            </div>
            <div
              data-testid="add-values"
              className="pd-cursor-pointer hover:pd-text-dark-500"
              onClick={() => handleSelection('type2')}
            >
              {hierarchyConstants.TYPE_2_LABEL}
            </div>
          </div>}
        </>
      );
    };

    const onChange = ((changedHierarchies: EditableHierarchy[]) => {
      setHierarchies(changedHierarchies);
    });

    const onAdd = useCallback((hierarchy: EditableHierarchy) => {
      const foundHierarchy = findHierarchy(hierarchies, hierarchy) as EditableHierarchy;

      foundHierarchy.properties = {
        ...(foundHierarchy.properties || {}),
        show: true,
      };

      setHierarchies([...hierarchies]);
    }, [hierarchies]);

    const onRootAdd = (() => {
      const newHierarchy: EditableHierarchy = {
        name: '',
        id: generateId(),
        properties: {
          type: 'type1',
        },
      };

      hierarchies.unshift(newHierarchy);

      setHierarchies([...hierarchies]);
    });

    const mappedHierarchies = useMemo(() => {
      const mapHierarchies = (newHierarchies: EditableHierarchy[]): EditableHierarchy[] => newHierarchies.map((h) => ({
        ...h,
        onAdd,
        child: ChoseType as React.FC,
        hierarchies: h.hierarchies ? mapHierarchies(h.hierarchies) : undefined,
      }));

      return mapHierarchies(hierarchies);
    }, [hierarchies]);

    return (
      <div className='pd-flex pd-gap-4'>
        <div className='pd-w-[450px]' style={{ whiteSpace: 'pre-wrap' }}>{JSON.stringify(hierarchies, undefined, 2)}</div>
        <div className='pd-w-[450px]'>
          <EditableHierarchyComponent
            {...args}
            hierarchies={mappedHierarchies}
            labelProps={{ ...args.labelProps, title: hierarchyConstants.TITLE_EDIT }}
            onChange={onChange}
            onRootAdd={onRootAdd}
          />
        </div>
      </div>
    );
  },
};
