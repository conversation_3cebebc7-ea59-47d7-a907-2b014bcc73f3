import { action } from '@storybook/addon-actions';
import type { Meta, StoryObj } from '@storybook/react';

import { ShoppingCartSummary } from '../components/shoppingCart/ShoppingCartSummary.Component';

const meta: Meta<typeof ShoppingCartSummary> = {
  title: 'Components/ShoppingCart/ShoppingCartSummary',
  component: ShoppingCartSummary,
  args: {
    subTotal: 100,
    total: 125,
    taxesValue: 10,
    discountValue: 5,
    shippingCost: 20,
    checkoutButton: {
      children: 'Hacer pedido',
      onClick: action('checkout'),
    },
  },
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
};

export default meta;

type Story = StoryObj<typeof ShoppingCartSummary>;

export const Default: Story = {
  render: (args) => (
    <div className='pd-w-52'>
      <ShoppingCartSummary {...args} />
    </div>
  ),
};
