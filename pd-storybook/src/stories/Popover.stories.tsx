import type { <PERSON>a, StoryObj } from '@storybook/react';

import { Button } from '../components/button/Button.Component';
import { CustomPopoverContent, Popover } from '../components/popover/Popover.Component';
import { Title } from '../components/title/Title.Component';
import Theme from '../configurations/Theme.Configuration';

const meta = {
  title: 'Components/Popover',
  component: Popover,
  parameters: {
    layout: 'centered',
  },
  args: {
    content: 'This is a popover content',
    position: 'bottom',
    className: '',
    fontColor: '',
    bgColor: '',
    triggerMode: 'click',
    width: 'auto',
    maxHeight: 'none',
    hasScroll: false,
  },
  argTypes: {
    content: { control: 'text' },
    position: { control: 'select', options: ['top', 'right', 'bottom', 'left'] },
    className: { control: 'text' },
    fontColor: { control: 'text' },
    bgColor: { control: 'text' },
    triggerMode: { control: 'select', options: ['click', 'hover'] },
    width: { control: 'text' },
    maxHeight: { control: 'text' },
    hasScroll: { control: 'boolean' },
  },
} as Meta<typeof Popover>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    children: <Button>Click me</Button>,
  },
};

export const WithHoverTrigger: Story = {
  args: {
    children: <Button>Hover me</Button>,
    triggerMode: 'hover',
  },
};

export const WithCustomRenderer: Story = {
  render: () => {
    const customContent: CustomPopoverContent = {
      title: 'Custom Rendered Content',
      description: 'This content is rendered using a custom renderer function',
      items: ['Item 1', 'Item 2', 'Item 3'],
    };

    return (
      <Popover
        content={customContent}
        renderer={(content) => {
          const typedContent = content as CustomPopoverContent;
          return (
            <div className="pd-p-2">
              <Title as="h3" size="lg" weight="semiBold">{typedContent.title}</Title>
              <p className="pd-my-2">{typedContent.description}</p>
              <ul className="pd-pl-5">
                {typedContent.items.map((item: string, index: number) => (
                  <li key={index}>{item}</li>
                ))}
              </ul>
            </div>
          );
        }}
        width="300px"
      >
        <Button>Custom Renderer</Button>
      </Popover>
    );
  },
};

export const WithDifferentPositions: Story = {
  render: (args) => (
    <div className="pd-flex pd-gap-5 pd-flex-wrap">
      <Popover {...args} position="top" content="Top position">
        <Button>Top</Button>
      </Popover>
      <Popover {...args} position="right" content="Right position">
        <Button>Right</Button>
      </Popover>
      <Popover {...args} position="bottom" content="Bottom position">
        <Button>Bottom</Button>
      </Popover>
      <Popover {...args} position="left" content="Left position">
        <Button>Left</Button>
      </Popover>
    </div>
  ),
};

export const WithCustomStyles: Story = {
  args: {
    children: <Button>Custom Styles</Button>,
    content: 'This popover has custom styles',
    bgColor: Theme.colors.fadedBlue,
    fontColor: Theme.colors.darkBlue,
    width: '250px',
  },
};

export const WithScrollableContent: Story = {
  render: () => {
    const longContent = (
      <div className="pd-space-y-2">
        <Title as="h3" size="lg" weight="semiBold">Popover con Scroll</Title>
        <p>Este popover tiene una altura máxima y muestra una barra de desplazamiento cuando el contenido es demasiado largo.</p>
        <ul className="pd-space-y-2">
          {Array.from({ length: 20 }, (_, i) => (
            <li key={i} className="pd-my-2.5">
              Elemento de lista {i + 1} - Este es un elemento de ejemplo para mostrar el scroll
            </li>
          ))}
        </ul>
      </div>
    );

    return (
      <Popover
        content={longContent}
        position="bottom"
        maxHeight="300px"
        hasScroll={true}
        width="350px"
      >
        <Button>Abrir Popover con Scroll</Button>
      </Popover>
    );
  },
};

export const WithMaxWidth: Story = {
  render: () => {
    const longTextContent = (
      <div>
        <Title as="h3" size="lg" weight="semiBold">Popover con MaxWidth</Title>
        <p className="pd-my-2">Este popover tiene un ancho máximo definido. El texto se ajustará automáticamente dentro del contenedor sin exceder este ancho máximo.</p>
        <p className="pd-my-2">
          Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam euismod, nisl eget aliquam ultricies, nunc nisl aliquet nunc, quis aliquam nisl nunc quis nisl.
        </p>
      </div>
    );

    const shortContent = 'Texto corto';
    const mediumContent = 'Este es un texto de longitud media.';

    return (
      <div className="pd-flex pd-flex-col pd-gap-8">
        <div className="pd-flex pd-gap-4">
          <Popover
            content={shortContent}
            position="bottom"
          >
            <Button>Texto Corto (Sin MaxWidth)</Button>
          </Popover>

          <Popover
            content={mediumContent}
            position="bottom"
          >
            <Button>Texto Medio (Sin MaxWidth)</Button>
          </Popover>
        </div>

        <div className="pd-flex pd-gap-4">
          <Popover
            content={shortContent}
            position="bottom"
            maxWidth="100px"
          >
            <Button>Texto Corto (MaxWidth: 100px)</Button>
          </Popover>

          <Popover
            content={mediumContent}
            position="bottom"
            maxWidth="200px"
          >
            <Button>Texto Medio (MaxWidth: 200px)</Button>
          </Popover>
        </div>

        <div className="pd-flex pd-gap-4">
          <Popover
            content={longTextContent}
            position="bottom"
            maxWidth="200px"
          >
            <Button>Contenido Largo (MaxWidth: 200px)</Button>
          </Popover>

          <Popover
            content={longTextContent}
            position="bottom"
            maxWidth="300px"
          >
            <Button>Contenido Largo (MaxWidth: 300px)</Button>
          </Popover>

          <Popover
            content={longTextContent}
            position="bottom"
          >
            <Button>Contenido Largo (Sin MaxWidth)</Button>
          </Popover>
        </div>
      </div>
    );
  },
};
