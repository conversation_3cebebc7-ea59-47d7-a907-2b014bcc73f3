import type { Meta, StoryObj } from '@storybook/react';
import { useState } from 'react';

import { Button } from '../components/button/Button.Component';
import { HiddenMenu } from '../components/hiddenMenu/HiddenMenu.Component';
import { FormInputType, InputComponent } from '../components/input/Input.Component';
import { TextAreaInput } from '../components/textAreaInput/TextAreaInput.Component';

const meta: Meta<typeof HiddenMenu> = {
  title: 'Components/HiddenMenu',
  component: HiddenMenu,
  parameters: {
    layout: 'fullscreen',
  },
  tags: ['autodocs'],
};

export default meta;

type Story = StoryObj<typeof HiddenMenu>;

const basicTopChildren = <p className='pd-text-primary pd-font-semibold pd-text-xl'>I am a Basic Title</p>;

const basicBottomChildren = <ul className="pd-space-y-4">
  {[...Array(5)].map((_, index) => (
    <li key={index} className="pd-p-2 pd-bg-gray-100 pd-rounded">
      Elemento {index + 1}
    </li>
  ))}
</ul>;

const childrenTemplate = ({
  topChildren,
  bottomChildren,
} : {topChildren: React.ReactNode, bottomChildren: React.ReactNode}) => <div className="pd-flex pd-flex-col pd-h-full pd-bg-white pd-shadow-lg">
  <div className="pd-sticky pd-top-0 pd-p-4 pd-z-10">
    {topChildren}
  </div>

  <div className='pd-flex-1 pd-overflow-y-auto'>
    <div className="pd-p-4">
      {bottomChildren}
    </div>
  </div>
</div>;

export const Basic: Story = {
  args: {
    isOpen: false,
    children: childrenTemplate({ topChildren: basicTopChildren, bottomChildren: basicBottomChildren }),
  },
  render: (args) => {
    const [isOpen, setIsOpen] = useState(args.isOpen);
    return (
      <div className="pd-flex pd-flex-col pd-items-center pd-gap-4 pd-h-screen pd-justify-center">
        <Button
          onClick={() => setIsOpen((prev) => !prev)}
          variant="primary"
          className='pd-min-w-40'
              >
          {isOpen ? 'Cerrar menú' : 'Abrir menú'}
        </Button>
        <HiddenMenu
          {...args}
          isOpen={isOpen}
              />
      </div>
    );
  },
};

export const ContactForm: Story = {
  render: () => {
    const [isOpen, setIsOpen] = useState(false);

    return (
      <div className="pd-relative pd-min-h-screen pd-bg-gray-100">
        <div
          className={`pd-min-h-screen pd-transition-all pd-duration-300 pd-ease-in-out ${isOpen ? 'pd-mr-64' : 'pd-mr-0'}`}
        >
          <nav className="pd-bg-white pd-shadow-md">
            <div className="pd-container pd-mx-auto pd-px-4 pd-py-4 pd-flex pd-justify-between pd-items-center">
              <h1 className="pd-text-xl pd-font-bold">Mi Sitio Web</h1>
              <Button
                onClick={() => setIsOpen(!isOpen)}
                className="pd-px-4 pd-py-2 pd-bg-green-500 pd-text-white pd-rounded hover:pd-bg-green-600 pd-transition-colors"
              >
                {isOpen ? 'Cerrar' : 'Contacto'}
              </Button>
            </div>
          </nav>

          <main className="pd-p-6">
            <h2 className="pd-text-3xl pd-font-bold pd-mb-4">Bienvenido a Mi Sitio Web</h2>
            <p className="pd-mb-4">Este es un ejemplo que muestra cómo usar el componente HiddenMenu para un formulario de contacto.</p>
            <p>Haz clic en el botón Contacto para abrir el formulario lateral.</p>
          </main>
        </div>

        <HiddenMenu
          isOpen={isOpen}
          className='!pd-border-l !pd-border-gray-200'>
          <form className="pd-space-y-4 pd-bg-white pd-p-4" onSubmit={(e) => e.preventDefault()}>
            <div>
              <label className="pd-block pd-text-sm pd-font-medium pd-mb-1">Nombre</label>
              <InputComponent
                name='name'
                className="pd-w-full pd-px-3 pd-py-2 pd-border pd-rounded pd-focus:outline-none pd-focus:ring-2 pd-focus:ring-green-500"
                placeholder="Tu nombre"
              />
            </div>
            <div>
              <label className="pd-block pd-text-sm pd-font-medium pd-mb-1">Email</label>
              <InputComponent
                name='email'
                inputType={FormInputType.Email}
                className="pd-w-full pd-px-3 pd-py-2 pd-border pd-rounded pd-focus:outline-none pd-focus:ring-2 pd-focus:ring-green-500"
                placeholder="<EMAIL>"
              />
            </div>
            <div>
              <label className="pd-block pd-text-sm pd-font-medium pd-mb-1">Asunto</label>
              <InputComponent
                name='subject'
                className="pd-w-full pd-px-3 pd-py-2 pd-border pd-rounded pd-focus:outline-none pd-focus:ring-2 pd-focus:ring-green-500"
                placeholder="Asunto del mensaje"
              />
            </div>
            <div>
              <label className="pd-block pd-text-sm pd-font-medium pd-mb-1">Mensaje</label>
              <TextAreaInput
                name='message'
                className="pd-w-full pd-px-3 pd-py-2 pd-border pd-rounded pd-focus:outline-none pd-focus:ring-2 pd-focus:ring-green-500 pd-min-h-32"
                placeholder="Escribe tu mensaje aquí..."
              />
            </div>
            <Button variant='outlined'
              className='!pd-w-full'>
              Enviar mensaje
            </Button>
          </form>
        </HiddenMenu>
      </div>
    );
  },
};
