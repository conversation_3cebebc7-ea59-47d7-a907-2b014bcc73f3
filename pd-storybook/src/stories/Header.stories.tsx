import type { Meta, StoryObj } from '@storybook/react';
import { fn } from '@storybook/test';
import { MemoryRouter } from 'react-router-dom';

import logo from '../assets/logo.png';
import { Header } from '../components/header/Header.Component';

const meta = {
  title: 'Components/Header',
  component: Header,
  parameters: {
    layout: 'centered',
  },
  args: {
    logo,
    profileUserInfo: {
      userName: 'Cristian',
    },
    logoutButton: {
      children: 'Logout',
      onClick: fn(),
    },
    notifications: [],
    className: 'pd-min-w-[800px]',
  },
  argTypes: {
    logo: {
      control: { type: 'text' },
    },
    profileUserInfo: {
      control: { type: 'object' },
    },
    logoutButton: {
      control: { type: 'object' },
    },
    notifications: {
      control: { type: 'object' },
    },
    profileUserSubmenu: {
      control: { type: 'object' },
    },
    className: {
      control: { type: 'text' },
    },
  },
} as Meta<typeof Header>;

type Story = StoryObj<typeof Header>;

export const Default: Story = {};

export const WithProfileUserInfo: Story = {
  args: {
    ...Default,
    profileUserInfo: {
      // eslint-disable-next-line max-len
      userProfilePhoto: 'https://plus.unsplash.com/premium_photo-1683121366070-5ceb7e007a97?fm=jpg&q=60&w=3000&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NXx8dXNlcnxlbnwwfHwwfHx8MA%3D%3D',
      userName: 'Cristian',
      userRol: 'Admin',
    },
  },
};

const notifications = [
  { id: '1', message: 'Notification 1', to: '#notification/1' },
  { id: '2', message: 'Notification 2', to: '#notification/2' },
  { id: '3', message: 'Notification Very long, lets see how to handle messages with a lot of words', to: '#notification/3' },
];

export const WithNotifications: Story = {
  render: (args) => (
    <MemoryRouter>
      <Header {...args} notifications={notifications} />
    </MemoryRouter>
  ),
};

const withProfileUserSubmenuArgs = [
  {
    id: '1', to: '#button', children: 'Button', external: false,
  },
  {
    id: '2', to: '#input', children: 'Input', external: false,
  },
  {
    id: '3', to: '#title', children: 'Title', external: false,
  },
];

export const WithProfileUserSubmenus: Story = {
  render: (args) => (
    <MemoryRouter>
      <Header {...args} profileUserSubmenu={withProfileUserSubmenuArgs} />
    </MemoryRouter>
  ),
};

export const WithSearchBox: Story = {
  args: {
    ...Default.args,
    searchBox: {
      placeholder: 'Search...',
      suggestions: [],
      onValueChange: fn(),
      onSearchChange: fn(),
      isLoading: false,
      className: 'pd-bg-transparent',
    },
  },
};

export default meta;
