/* eslint-disable max-len */
import { action } from '@storybook/addon-actions';
import type { Meta, StoryObj } from '@storybook/react';
import { useState } from 'react';

import { Button } from '../components/button/Button.Component';
import { CardComponent } from '../components/card/Card.Component';
import { IconImporter } from '../components/iconImporter/IconImporter.Component';
import { ShoppingCart } from '../components/shoppingCart/ShoppingCart.Component';
import { ShoppingCartItemProps } from '../components/shoppingCart/ShoppingCartItem.Component';

const meta = {
  title: 'Components/ShoppingCart/ShoppingCart',
  component: ShoppingCart,
  parameters: {
    layout: 'fullscreen',
  },
  args: {
    isOpen: false,
    items: [],
    className: '',
  },
  argTypes: {
    isOpen: {
      control: 'boolean',
      description: 'Controla la visibilidad del carrito',
    },
    items: {
      control: 'object',
      description: 'Lista de productos en el carrito',
    },
    className: {
      control: 'text',
      description: 'Clases CSS adicionales',
    },
  },
} as Meta<typeof ShoppingCart>;

export default meta;
type Story = StoryObj<typeof ShoppingCart>;

const sampleItems: ShoppingCartItemProps[] = [
  {
    id: '1',
    name: 'Camiseta de algodón',
    unitPrice: 24.99,
    quantity: 2,
    imageUrl: 'https://www.betaautoparts.com/cdn/shop/files/A4-FOG-LAMP-1NE010832081-RH.jpg?v=1723056663&width=360',
  },
  {
    id: '2',
    name: 'Pantalón vaquero',
    unitPrice: 49.99,
    quantity: 1,
    imageUrl: 'https://www.betaautoparts.com/cdn/shop/files/A4-RADIATOR-8K0121251Q.jpg?v=1723056635&width=360',
  },
  {
    id: '3',
    name: 'Zapatillas deportivas',
    unitPrice: 79.99,
    quantity: 1,
    imageUrl: 'https://www.betaautoparts.com/cdn/shop/files/06h121026ba-audi-volkswagen-skoda-seat-water-pump.jpg?v=1738753409&width=360',
  },
];

export const Default: Story = {
  args: {
    items: sampleItems,
    cartSummary: {
      checkoutButton: {
        children: 'Proceder al pago',
        onClick: action('checkout'),
      },
      subTotal: 0,
      total: 0,
    },
  },
  render: (args) => {
    const [isOpen, setIsOpen] = useState(args.isOpen);
    const [products, setProducts] = useState(args.items);

    const handleRemoveItem = (id: string) => {
      setProducts((prevItems) => prevItems.filter((item) => item.id !== id));
    };

    const handleIncrement = (id: string) => {
      setProducts((prevItems) => prevItems.map((item) => (item.id === id ? { ...item, quantity: item.quantity + 1 } : item)));
    };

    const handleDecrement = (id: string) => {
      setProducts((prevItems) => prevItems.map((item) => (item.id === id && item.quantity > 1 ? { ...item, quantity: item.quantity - 1 } : item)));
    };

    const items = products.map((item) => ({
      ...item,
      onRemove: handleRemoveItem,
      onIncrement: handleIncrement,
      onDecrement: handleDecrement,
    }));

    return (
      <div className="pd-flex pd-flex-col pd-items-center pd-gap-4 pd-h-screen pd-justify-center">
        <Button
          onClick={() => setIsOpen((prev) => !prev)}
          variant="primary"
          className='pd-min-w-40'
        >
          {isOpen ? 'Cerrar carrito' : 'Abrir carrito'}
        </Button>
        <ShoppingCart
          {...args}
          items={items}
          isOpen={isOpen}
        />
      </div>
    );
  },
};

export const EmptyCart: Story = {
  render: (args) => {
    const [isOpen, setIsOpen] = useState(args.isOpen);

    return (
      <div className="pd-flex pd-flex-col pd-items-center pd-gap-4 pd-h-screen pd-justify-center">
        <Button
          onClick={() => setIsOpen((prev) => !prev)}
          variant="primary"
          className='pd-min-w-40'
        >
          {isOpen ? 'Cerrar carrito' : 'Abrir carrito'}
        </Button>
        <ShoppingCart
          {...args}
          items={[]}
          isOpen={isOpen}
        />
      </div>
    );
  },
};

const storeSampleItems = [
  {
    id: '001',
    name: 'Sensor de oxígeno',
    price: 24.99,
    originalPrice: 29.99,
    imageUrl: 'https://www.betaautoparts.com/cdn/shop/files/1431869955THIRD20BLINKER20LAMP20copy.jpg?v=1723035762&width=360',
  },
  {
    id: '002',
    name: 'Filtro de aire',
    price: 49.99,
    imageUrl: 'https://www.betaautoparts.com/cdn/shop/files/7l6820803d-8fk351001871-audi-a6-q7-volkswagen-touareg-compressor.jpg?v=1723053629&width=360',
  },
  {
    id: '003',
    name: 'Bomba de agua',
    price: 79.99,
    originalPrice: 89.99,
    imageUrl: 'https://www.betaautoparts.com/cdn/shop/files/0131545602-mercedes-W463-W164-W216-W219-W251-W230-W212-W211-W221-W207-W204-W209-alternator-180A-valeo-parts-1.jpg?v=1723015189&width=360',
  },
  {
    id: '004',
    name: 'Bujía de encendido',
    price: 9.99,
    originalPrice: 14.99,
    imageUrl: 'https://www.betaautoparts.com/cdn/shop/files/1421478766ALTERNATOR2061020copy.jpg?v=1723032715&width=360',
  },
  {
    id: '005',
    name: 'Filtro de aceite',
    price: 19.99,
    originalPrice: 24.99,
    imageUrl: 'https://www.betaautoparts.com/cdn/shop/files/1459839451BRABUS20BREAK20PAD20copy.jpg?v=1723026979&width=360',
  },
  {
    id: '006',
    name: 'Correa de distribución',
    price: 29.99,
    imageUrl: 'https://www.betaautoparts.com/cdn/shop/files/8V0941699C-audi-a3-fog-lamp-left-china-parts-1.jpg?v=1723056599&width=360',
  },
  {
    id: '007',
    name: 'Filtro de combustible',
    price: 14.99,
    originalPrice: 19.99,
    imageUrl: 'https://www.betaautoparts.com/cdn/shop/files/A4-FOG-LAMP-LH-8T0941699-FROM-VALEO-08-15.jpg?v=1723056626&width=360',
  },
  {
    id: '008',
    name: 'Pastillas de freno',
    price: 39.99,
    originalPrice: 49.99,
    imageUrl: 'https://www.betaautoparts.com/cdn/shop/files/06h121026ba-audi-volkswagen-skoda-seat-water-pump.jpg?v=1738753409&width=360',
  },
  {
    id: '009',
    name: 'Amortiguadores',
    price: 99.99,
    imageUrl: 'https://www.betaautoparts.com/cdn/shop/files/A4-RADIATOR-8K0121251Q.jpg?v=1723056635&width=360',
  },
  {
    id: '0010',
    name: 'Batería de coche',
    price: 69.99,
    originalPrice: 79.99,
    imageUrl: 'https://www.betaautoparts.com/cdn/shop/files/A4-FOG-LAMP-1NE010832081-RH.jpg?v=1723056663&width=360',
  },
];

export const WithItemsToAdd: Story = {
  render: (args) => {
    const [isOpen, setIsOpen] = useState(args.isOpen);
    const [toCheckoutItems, setToCheckoutItems] = useState<ShoppingCartItemProps[]>(sampleItems);

    const handleRemoveItem = (id: string) => {
      setToCheckoutItems((prevItems) => prevItems.filter((item) => item.id !== id));
    };

    const handleIncrement = (id: string) => {
      setToCheckoutItems((prevItems) => prevItems.map((item) => (item.id === id ? { ...item, quantity: item.quantity + 1 } : item)));
    };

    const handleDecrement = (id: string) => {
      setToCheckoutItems((prevItems) => prevItems.map((item) => (item.id === id && item.quantity > 1 ? { ...item, quantity: item.quantity - 1 } : item)));
    };

    const toCheckoutItemsMapped = toCheckoutItems.map((item) => ({
      ...item,
      onRemove: handleRemoveItem,
      onIncrement: handleIncrement,
      onDecrement: handleDecrement,
    }));

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const onAddItem = (item: any) => {
      const existingItem = toCheckoutItems.find((i) => i.id === item.id);

      if (existingItem) {
        handleIncrement(item.id);
      } else {
        setToCheckoutItems([...toCheckoutItems, { ...item, quantity: 1, price: item.price }]);
      }
      action('addToCart')(item);
    };

    const totalItems = toCheckoutItems.reduce((total, item) => total + item.quantity, 0);

    return (

      <div className="pd-relative pd-min-h-screen pd-bg-gray-100">
        <div className={`pd-min-h-screen pd-transition-all pd-duration-300 pd-ease-in-out ${isOpen ? 'pd-mr-64' : 'pd-mr-0'}`}>
          <nav className="pd-bg-white pd-shadow-md">
            <div className="pd-container pd-px-4 pd-py-4 pd-flex pd-justify-between pd-items-center">
              <h1 className="pd-text-xl pd-font-bold">Mi Tienda</h1>
              <button
                className="pd-relative pd-text-gray-600 pd-hover:text-gray-900"
                onClick={() => setIsOpen((prev) => !prev)}
              >
                <IconImporter name="shoppingCart" size={20} />
                {totalItems > 0 && (
                <span className="pd-absolute pd-top-0 pd-right-0 pd-transform pd-translate-x-1/2 pd-translate-y-[-50%] pd-bg-primary pd-text-white pd-rounded-full pd-w-5 pd-h-5 pd-flex pd-items-center pd-justify-center pd-text-xs">
                  {totalItems}
                </span>
                )}
              </button>
            </div>
          </nav>

          <main>
            <section className="pd-p-6">
              <h2 className="pd-text-2xl pd-font-bold pd-mb-4">Productos Destacados</h2>
              <div className="pd-flex pd-flex-wrap pd-gap-4">
                {
                  storeSampleItems.map((item) => (
                    <div key={item.id}>
                      <CardComponent
                        id={item.id}
                        title={item.name}
                        imageUrl={item.imageUrl}
                        price={item.price}
                        originalPrice={item.originalPrice}
                        redirectTo={{ to: '/' }}
                        actionButton={(
                          <Button
                            variant='outlined'
                            onClick={(e) => {
                              e.preventDefault();
                              onAddItem(item);
                            }}>
                            <IconImporter size={20} name='shoppingCart' />
                          </Button>
                      )}
                        className="pd-border pd-rounded-lg pd-overflow-hidden pd-bg-white"
                      />
                    </div>
                  ))
                }
              </div>
            </section>
          </main>
        </div>
        <ShoppingCart
          {...args}
          items={toCheckoutItemsMapped}
          isOpen={isOpen}
          />
      </div>
    );
  },
};
