import type { Meta, StoryObj } from '@storybook/react';
import { fn } from '@storybook/test';
import { useState } from 'react';

import { AvatarGroup } from '../components/avatarGroup/AvatarGroup.Component';
import { OptionsDropdownProps } from '../components/dropdown/DropdownWithSearch.Component';

const meta = {
  title: 'Components/AvatarGroup/AvatarGroupFilled',
  component: AvatarGroup,
  parameters: {
    layout: 'centered',
  },
  args: {
    maxToShow: 3,
    itemsSelected: [{
      id: '1',
      name: '<PERSON>',
    }],
    readOnly: false,
    avatarProps: {
      size: 50,
      onclick: fn(),
      tooltip: true,
      tooltipProps: {
        position: 'top',
      },
    },
    dropdownProps: {
      options: [],
    },
  },
  argTypes: {
    avatarProps: { control: 'object' },
    maxToShow: { control: 'number' },
  },
} as Meta<typeof AvatarGroup>;

type Story = StoryObj<typeof AvatarGroup>;

export const Default: Story = {
  render: (args) => (
    <AvatarGroup {...args}/>
  ),
};

export const AvatarSelectedNoAdd: Story = {
  render: (args) => (
    <AvatarGroup {...args} readOnly={true}/>
  ),
};

export const AvatarSelectedAddAvatar: Story = {
  render: (args) => {
    const [itemsSelected, setItemsSelected] = useState<OptionsDropdownProps[]>([
      {
        id: '1',
        name: 'usuario 1',
        // eslint-disable-next-line max-len
        src: 'https://imgs.search.brave.com/w2HND3H2u2ORdvuYc96qSZUj_BxcJM2mKSEhQ2E_04I/rs:fit:500:0:0:0/g:ce/aHR0cHM6Ly90NC5m/dGNkbi5uZXQvanBn/LzA3LzQxLzcxLzkz/LzM2MF9GXzc0MTcx/OTM5NF9DOUJQM1li/aVhTSjdXc3BTREx0/S2RZeFpLS1dsZjBK/ei5qcGc',
      },
      {
        id: '2',
        name: 'usuario 2',
      },
    ]);

    const dropdownOptions = [
      {
        id: '1',
        name: 'usuario 1',
      },
      {
        id: '2',
        name: 'usuario 2',
      },
      {
        id: '3',
        name: 'usuario 3',
      },
      {
        id: '4',
        name: 'usuario 4',
      },
    ];

    const onSelectOption = (option: OptionsDropdownProps) => {
      const newItems = [...itemsSelected, option];
      setItemsSelected(newItems);
    };

    const dropdownProps = {
      options: dropdownOptions,
      setSelectedOption: onSelectOption,
    };

    return (
      <AvatarGroup {...args} itemsSelected={itemsSelected} dropdownProps={dropdownProps} direction='vertical' />
    );
  },
};

export const AvatarGroupWithStroke: Story = {
  render: (args) => {
    const [itemsSelected, setItemsSelected] = useState<OptionsDropdownProps[]>([
      {
        id: '1',
        name: 'usuario 1',
      },
      {
        id: '2',
        name: 'usuario 2',
      },
    ]);

    const dropdownOptions = [
      {
        id: '1',
        name: 'usuario 1',
      },
      {
        id: '2',
        name: 'usuario 2',
      },
      {
        id: '3',
        name: 'usuario 3',
      },
      {
        id: '4',
        name: 'usuario 4',
      },
    ];

    const onSelectOption = (option: OptionsDropdownProps) => {
      const newItems = [...itemsSelected, option];
      setItemsSelected(newItems);
    };

    const dropdownProps = {
      options: dropdownOptions,
      setSelectedOption: onSelectOption,
    };

    return (
      <AvatarGroup
        {...args}
        itemsSelected={itemsSelected}
        dropdownProps={dropdownProps}
        avatarProps={{
          ...args.avatarProps,
          border: {
            width: '2px',
            color: 'red',
          },
        }}
      />
    );
  },
};

export const AvatarGroupDiferentSize: Story = {
  render: (args) => (
    <AvatarGroup {...args} avatarProps={{ ...args.avatarProps, size: 35 }}/>
  ),
};

export const AvatarGroupSquareAvatars: Story = {
  render: (args) => (
    <AvatarGroup {...args} avatarProps={{ ...args.avatarProps, shape: 'square' }}/>
  ),
};

export default meta;
