import type { Meta, StoryObj } from '@storybook/react';
import { fn } from '@storybook/test';

import { TextAreaInput } from '../components/textAreaInput/TextAreaInput.Component';

const meta = {
  title: 'Components/form/TextAreaInput',
  component: TextAreaInput,
  parameters: {
    layout: 'centered',
  },
  args: {
    name: 'textArea',
    placeholder: 'Write here',
    inputClassName: '',
    isRequired: false,
    error: false,
    onChange: fn(),
  },
  argTypes: {
    name: { control: 'text' },
    placeholder: { control: 'text' },
    inputClassName: { control: 'text' },
    isRequired: { control: 'boolean' },
    error: { control: 'boolean' },
  },
} as Meta<typeof TextAreaInput>;

type Story = StoryObj<typeof TextAreaInput>;

export const TextArea: Story = {};

export default meta;
