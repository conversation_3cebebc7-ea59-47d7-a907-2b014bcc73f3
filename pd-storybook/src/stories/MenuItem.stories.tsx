import type { <PERSON>a, StoryObj } from '@storybook/react';
import { MemoryRouter } from 'react-router-dom';

import { MenuItem, MenuItemProps } from '../components/menuItem/MenuItem.Component';

const defaultItems: MenuItemProps = {
  id: '1',
  title: 'Menu Item',
  icon: 'airplane',
  items: [
    {
      id: '1',
      title: 'Sub Menu Item',
      to: '/',
      active: false,
    },
    {
      id: '2',
      title: 'Sub Menu Item 2',
      to: '/',
      active: false,
    },
  ],
  to: '/',
  active: false,
};

const meta = {
  title: 'Components/MenuItem',
  component: MenuItem,
  parameters: {
    layout: 'centered',
    backgrounds: {
      default: 'dark',
    },
  },
  args: {
    ...defaultItems,
  },
  argTypes: {
    items: {
      control: 'object',
    },
    active: {
      control: 'boolean',
    },
  },
} as Meta<typeof MenuItem>;

const muchitems = [
  {
    id: '1',
    title: 'Sub Menu Item',
    to: '/',
  },
  {
    id: '2',
    title: 'Sub Menu Item 2',
    to: '/',
  },
  {
    id: '3',
    title: 'Sub Menu Item 3',
    to: '/',
  },
  {
    id: '4',
    title: 'Sub Menu Item 4',
    to: '/',
  },
  {
    id: '5',
    title: 'Sub Menu Item 5',
    to: '/',
  },
  {
    id: '6',
    title: 'Sub Menu Item 6',
    to: '/',
  },
  {
    id: '7',
    title: 'Sub Menu Item 7',
    to: '/',
  },
  {
    id: '8',
    title: 'Sub Menu Item 8',
    to: '/',
  },
  {
    id: '9',
    title: 'Sub Menu Item 9',
    to: '/',
  },
  {
    id: '10',
    title: 'Sub Menu Item 10',
    to: '/',
  },
  {
    id: '11',
    title: 'Sub Menu Item 11',
    to: '/',
  },
  {
    id: '12',
    title: 'Sub Menu Item 12',
    to: '/',
  },
  {
    id: '13',
    title: 'Sub Menu Item 13',
    to: '/',
  },
  {
    id: '14',
    title: 'Sub Menu Item 14',
    to: '/',
  },
];

export default meta;

type Story = StoryObj<typeof MenuItem>;

export const WithSubItems: Story = {
  render: (args) => (
    <MemoryRouter>
      <MenuItem {...args} />
    </MemoryRouter>
  ),
};

export const WithoutSubItems: Story = {
  render: (args) => (
    <MemoryRouter>
      <MenuItem {...args} items={[]} />
    </MemoryRouter>
  ),
};

export const MultipleSubItems: Story = {
  render: (args) => (
    <MemoryRouter>
      <MenuItem {...args} items={muchitems} />
    </MemoryRouter>
  ),
};
