import { action } from '@storybook/addon-actions';
import type { <PERSON>a, StoryObj } from '@storybook/react';
import { useEffect, useState } from 'react';

import { ComponentModes, RelatedItem, RelationsComponent } from '../components/relations/Relations.Component';
import { SearchBox, SearchSuggestion } from '../components/searchBox/SearchBox.Component';

const meta = {
  title: 'Components/Relation',
  component: RelationsComponent,
  parameters: {
    layout: 'centered',
  },
  args: {
    header: {
      title: 'Relations Component',
      tooltip: {
        content: 'Please search an item to add to a new relation',
      },
      icon: {
        name: 'info',
      },
    },
    relatedItems: [],
    noItemsLabel: '',
    searchBox: {},
    subTitle: 'Current related items',
    mode: 'showMode',
    togleModeButton: () => {},
  },
  argTypes: {
    header: {
      control: {
        type: 'object',
      },
    },
    subTitle: {
      control: {
        type: 'text',
      },
    },
    disabled: {
      control: {
        type: 'boolean',
      },
    },
    relatedItems: {
      control: {
        type: 'object',
      },
    },
    noItemsLabel: {
      control: {
        type: 'text',
      },
    },
    searchBox: {
      control: {
        type: 'object',
      },
    },
    onSave: {
      action: 'onSave',
    },
  },
} as Meta<typeof RelationsComponent>;

const sampleData = [
  { id: '1', title: 'Manzana' },
  { id: '2', title: 'Banana' },
  { id: '3', title: 'Cereza' },
  { id: '4', title: 'Damasco' },
  { id: '5', title: 'Frambuesa' },
  { id: '6', title: 'Uva' },
  { id: '7', title: 'Kiwi' },
  { id: '8', title: 'Lima' },
  { id: '9', title: 'Limón' },
  { id: '10', title: 'Mango' },
  { id: '11', title: 'Naranja' },
  { id: '12', title: 'Papaya' },
  { id: '13', title: 'Pera' },
  { id: '14', title: 'Piña' },
  { id: '15', title: 'Sandía' },
  { id: '21', title: 'Related Item 1' },
];

const initialItems = [
  { id: '21', title: 'Related Item 1' },
  { id: '22', title: 'Related Item 2' },
  { id: '23', title: 'Related Item 3' },
  { id: '24', title: 'Related Item 4' },
  { id: '25', title: 'Related Item 5' },
];

const searchDatabase = (query: string) => new Promise<typeof sampleData>((resolve) => {
  setTimeout(() => {
    const results = sampleData.filter((item) => item.title.toLowerCase().includes(query.toLowerCase()));
    resolve(results);
  }, 1000);
});

type Story = StoryObj<typeof RelationsComponent>

export const Default: Story = {
  render: (args) => {
    const [inputValue, setInputValue] = useState('');
    return (
      <div className='pd-w-[400px]'>
        <RelationsComponent
          {...args}
      >
          <input
            type="text"
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            className='pd-border pd-border-dark-300 pd-rounded-lg pd-p-1 pd-text-sm pd-px-2'
            placeholder='Write here...'
        />
        </RelationsComponent>
      </div>
    );
  },
};

export const Disabled: Story = {
  args: {
    mode: 'disabledMode',
  },
  render: (args) => (
    <div className='pd-w-[400px]'>
      <RelationsComponent
        {...args}
      >
        <input
          type="text"
          className='pd-border pd-border-dark-300 pd-rounded-lg pd-p-1 pd-text-sm pd-px-2'
          placeholder='Write here...'
        />
      </RelationsComponent>
    </div>
  ),
};

export const DisabledWithContent: Story = {
  args: {
    mode: 'disabledMode',
    relatedItems: initialItems,
  },
  render: (args) => (
    <div className='pd-w-[400px]'>
      <RelationsComponent
        {...args}
      >
        <input
          type="text"
          className='pd-border pd-border-dark-300 pd-rounded-lg pd-p-1 pd-text-sm pd-px-2'
          placeholder='Write here...'
          />
      </RelationsComponent>
    </div>
  ),
};

export const DefaultItemsAndLiveSearch: Story = {
  args: {
    ...Default.args,
    relatedItems: initialItems,
  },
  render: (args) => {
    const [seledtedItems, setSelectedItems] = useState(args.relatedItems || []);
    const [suggestions, setSuggestions] = useState<{ id: string; title: string }[]>([]);
    const [modeState, setModeState] = useState<ComponentModes>('showMode');
    const [isLoading, setIsLoading] = useState(false);

    const handleAdd = (itemToAdd: RelatedItem) => {
      const exist = seledtedItems?.some((item) => item.id === itemToAdd?.id);
      if (exist) return;
      setSelectedItems([itemToAdd, ...seledtedItems]);
    };

    const handleDelete = (id: string) => {
      const newItems = seledtedItems?.filter((item) => item.id !== id);
      setSelectedItems(newItems);
    };

    const items = seledtedItems?.map((item) => ({
      ...item,
      onRemoveItem: () => handleDelete(item.id),
    }));

    const handleSearchChange = async (value: string) => {
      if (value.trim() === '') {
        setSuggestions([]);
        return;
      }

      setIsLoading(true);
      try {
        const results = await searchDatabase(value);
        const filteredResults = results.filter((result) => !seledtedItems.some((selected) => selected.id === result.id));
        setSuggestions(filteredResults);
      } catch (error) {
        action('error')(error);
        setSuggestions([]);
      } finally {
        setIsLoading(false);
      }
    };

    const handleToogleMode = () => {
      if (modeState === 'editMode') {
        action('Success')(seledtedItems);
      }
      setModeState((prev) => (prev === 'editMode' ? 'showMode' : 'editMode'));
    };

    const onSearchChange = (selectedSuggestion?: SearchSuggestion) => {
      handleAdd(selectedSuggestion as RelatedItem);
      setSuggestions([]);
    };

    const search = {
      suggestions,
      isLoading,
      onValueChange: handleSearchChange,
      onSearchChange,
      disabled: modeState === 'showMode',
    };

    return (
      <div className='pd-w-[400px]'>
        <RelationsComponent
          {...args}
          relatedItems={items}
          mode={modeState}
          togleModeButton={handleToogleMode}
        >
          <SearchBox {...search} />
        </RelationsComponent>
      </div>
    );
  },
};

const CustomResulst = ({ title, subTitle }: { title: string, subTitle: string }) => (
  <div className="pd-flex pd-items-center pd-gap-4">
    <div className='pd-h-8 pd-w-8 pd-bg-fadedRed pd-rounded-full pd-flex pd-items-center pd-justify-center pd-text-lg pd-text-primary'>
      {title[0]}
    </div>
    <div className='pd-flex pd-flex-col'>
      <div className='pd-text-xsm'>{title}</div>
      <div className='pd-text-xxsm pd-text-dark-500'>{subTitle}</div>
    </div>
  </div>
);

export const CustomItemsAndSearchWithEnter: Story = {
  render: (args) => {
    const [seledtedItems, setSelectedItems] = useState(args.relatedItems || []);
    const [suggestions, setSuggestions] = useState<{ id: string; title: string }[]>([]);
    const [modeState, setModeState] = useState<ComponentModes>('showMode');
    const [isLoading, setIsLoading] = useState(false);
    const [searchInput, setSearchInput] = useState<string>('');
    const [searchValue, setSearchValue] = useState<string>('');

    const handleSearchChange = async (value: string) => {
      if (value.trim() === '') {
        setSuggestions([]);
        return;
      }

      setIsLoading(true);
      try {
        const results = await searchDatabase(value);
        const filteredResults = results.filter((result) => !seledtedItems.some((selected) => selected.id === result.id));
        const customResults = filteredResults.map((item) => ({
          ...item,
          subTitle: 'This is a custom subtitle',
          renderer: CustomResulst,
        }));
        setSuggestions(customResults);
      } catch (error) {
        action('error')(error);
        setSuggestions([]);
      } finally {
        setIsLoading(false);
      }
    };

    useEffect(() => {
      handleSearchChange(searchValue);
    }, [searchValue]);

    const handleAdd = (itemToAdd: RelatedItem) => {
      const exist = seledtedItems?.some((item) => item.id === itemToAdd?.id);
      if (exist) return;
      setSelectedItems([itemToAdd, ...seledtedItems]);
    };

    const handleDelete = (id: string) => {
      const newItems = seledtedItems?.filter((item) => item.id !== id);
      setSelectedItems(newItems);
    };

    const items = seledtedItems?.map((item) => ({
      ...item,
      onRemoveItem: () => handleDelete(item.id),
    }));

    const handleInputChange = (value: string) => {
      setSearchInput(value);
      if (value.trim() === '') {
        setSearchValue('');
      }
    };

    const onSearchChange = (selectedSuggestion?: SearchSuggestion) => {
      handleAdd(selectedSuggestion as RelatedItem);
      setSuggestions([]);
      setSearchInput('');
    };

    const handleToogleMode = () => {
      if (modeState === 'editMode') {
        action('Success')(seledtedItems);
      }
      setModeState((prev) => (prev === 'editMode' ? 'showMode' : 'editMode'));
    };

    const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
      if (event.key === 'Enter') {
        const trimmedInput = searchInput.trim();

        if (trimmedInput.length < 1) {
          action('error')('Please enter a valid search value');
          return;
        }
        setSearchValue(trimmedInput);
        handleSearchChange(trimmedInput);
      }
    };

    const onOutsideClick = () => {
      setSuggestions([]);
    };

    const search = {
      suggestions,
      isLoading,
      value: searchInput,
      onValueChange: handleInputChange,
      onKeyDown: handleKeyDown,
      onSearchChange,
      disabled: modeState === 'showMode',
      pressEnterLabel: 'Press Enter to search',
      onOutsideClick,
    };

    return (
      <div className='pd-w-[400px]'>
        <RelationsComponent
          {...args}
          relatedItems={items}
          mode={modeState}
          togleModeButton={handleToogleMode}
      >
          <SearchBox {...search} />
        </RelationsComponent>
      </div>
    );
  },
};

export default meta;
