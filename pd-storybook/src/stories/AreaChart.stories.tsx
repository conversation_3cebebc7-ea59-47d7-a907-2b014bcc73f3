import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';

import { AreaChartComponent } from '../components/chart/AreaChart.Component';
import Theme from '../configurations/Theme.Configuration';

const chartData = [
  { month: 'January', desktop: 186 },
  { month: 'February', desktop: 305 },
  { month: 'March', desktop: 237 },
  { month: 'April', desktop: 73 },
  { month: 'May', desktop: 209 },
  { month: 'June', desktop: 214 },
];

const chartDataMultiple = [
  { month: 'January', desktop: 186, mobile: 80 },
  { month: 'February', desktop: 305, mobile: 200 },
  { month: 'March', desktop: 237, mobile: 120 },
  { month: 'April', desktop: 73, mobile: 190 },
  { month: 'May', desktop: 209, mobile: 130 },
  { month: 'June', desktop: 214, mobile: 140 },
];

const meta = {
  title: 'Components/Charts/AreaChart',
  component: AreaChartComponent,
  args: {
    data: chartData,
    series: [
      {
        key: 'desktop',
        label: 'Desktop',
        color: Theme.colors.primary,
      },
    ],
    xAxisKey: 'month',
    yAxisKey: 'desktop',
    tickLength: 3,
    marginLeft: 20,
    marginRight: 20,
    marginTop: 0,
    marginBottom: 0,
    isAnimationActive: false,
    tickStyles: {},
    tickWidth: 0,
    cartesianGrid: false,
    showXAxis: true,
    showYAxis: true,
    className: '',
    showDot: false,
    dotLabel: false,
    dotLabelPosition: 'top',
    dotLabelOffset: 12,
    dotLabelFontSize: 12,
    areaType: 'monotone',
  },
  parameters: {
    layout: 'centered',
    key: 'AreaChart',
    docs: {
      disable: false,
    },
  },
  argTypes: {
    tickWidth: {
      control: {
        type: 'number',
      },
    },
    cartesianGrid: {
      control: {
        type: 'boolean',
      },
    },
    showXAxis: {
      control: {
        type: 'boolean',
      },
    },
    showYAxis: {
      control: {
        type: 'boolean',
      },
    },
    showDot: {
      control: {
        type: 'boolean',
      },
    },
    dotLabel: {
      control: {
        type: 'boolean',
      },
    },
  },
  decorators: [
    (Story) => (
      <div className='pd-flex pd-justify-center pd-align-middle pd-h-[250px] pd-w-full'>
        <Story />
      </div>
    ),
  ],
} as Meta<typeof AreaChartComponent>;

type Story = StoryObj<typeof AreaChartComponent>;

export const Default: Story = {};

export const MultipleSeries: Story = {
  args: {
    isAnimationActive: true,
    data: chartDataMultiple,
    series: [
      {
        key: 'desktop',
        label: 'Desktop',
        color: Theme.colors.primary,
        fillOpacity: 0.3,
      },
      {
        key: 'mobile',
        label: 'Mobile',
        color: Theme.colors.secondary,
        fillOpacity: 0.2,
      },
    ],
  },
  parameters: {
    docs: {
      disable: true,
    },
  },
};

export const LongLabels: Story = {
  args: {
    data: [
      { month: 'Long January', desktop: 100 },
      { month: 'Very February', desktop: 200 },
      { month: 'March ', desktop: 150 },
    ],
    series: [
      {
        key: 'desktop',
        label: 'Desktop',
        color: Theme.colors.primary,
      },
    ],
    isAnimationActive: true,
    xAxisKey: 'month',
    yAxisKey: 'desktop',
    tickWidth: 100,
    tickStyles: { fontSize: '10px' },
    tickLength: 16,
    marginTop: 20,
  },
  parameters: {
    docs: {
      disable: true,
    },
  },
};

export const CustomTickStyles: Story = {
  args: {
    data: chartData,
    isAnimationActive: true,
    series: [
      {
        key: 'desktop',
        label: 'Desktop',
        color: Theme.colors.primary,
        fillOpacity: 0.4,
      },
    ],
    xAxisKey: 'month',
    yAxisKey: 'desktop',
    tickWidth: 80,
    tickStyles: { fontSize: '14px', fill: '#0070f3' },
    tickLength: 6,
  },
  parameters: {
    docs: {
      disable: true,
    },
  },
};

export const WithoutAxis: Story = {
  args: {
    isAnimationActive: true,
    showXAxis: false,
    showYAxis: false,
  },
  parameters: {
    docs: {
      disable: true,
    },
  },
};

export const AreaTypeLinear: Story = {
  args: {
    isAnimationActive: true,
    areaType: 'linear',
  },
  parameters: {
    docs: {
      disable: true,
    },
  },
};

export const AreaTypeStep: Story = {
  args: {
    areaType: 'step',
    isAnimationActive: true,
  },
  parameters: {
    docs: {
      disable: true,
    },
  },
};

export default meta;
