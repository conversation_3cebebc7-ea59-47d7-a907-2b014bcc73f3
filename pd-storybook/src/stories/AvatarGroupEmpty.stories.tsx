import type { Meta, StoryObj } from '@storybook/react';
import { fn } from '@storybook/test';

import { AvatarGroup } from '../components/avatarGroup/AvatarGroup.Component';

const meta = {
  title: 'Components/AvatarGroup/AvatarGroupEmpty',
  component: AvatarGroup,
  parameters: {
    layout: 'centered',
  },
  args: {
    maxToShow: 3,
    itemsSelected: [],
    readOnly: false,
    avatarProps: {
      size: 50,
      onclick: fn(),
    },
    dropdownProps: {
      options: [],
    },
  },
  argTypes: {
    avatarProps: { control: 'object' },
    maxToShow: { control: 'number' },
  },
} as Meta<typeof AvatarGroup>;

type Story = StoryObj<typeof AvatarGroup>;

export const Default: Story = {
  render: (args) => (
    <AvatarGroup {...args} itemsSelected={[]}/>
  ),
};

export const EmptyState: Story = {
  render: (args) => (
    <AvatarGroup {...args} readOnly={true}/>
  ),
};

export const AvatarGroupWithStroke: Story = {
  render: (args) => (
    <AvatarGroup
      {...args}
      maxToShow={3}
      avatarProps={{
        ...args.avatarProps,
        border: {
          width: '2px',
          color: 'red',
        },
      }}
      />
  ),
};

export const AvatarGroupDiferentSize: Story = {
  render: (args) => (
    <AvatarGroup {...args} avatarProps={{ ...args.avatarProps, size: 35 }}/>
  ),
};

export const AvatarGroupSquareAvatars: Story = {
  render: (args) => (
    <AvatarGroup {...args} avatarProps={{ ...args.avatarProps, shape: 'square' }}/>
  ),
};

export default meta;
