import type { Meta, StoryObj } from '@storybook/react';

import { CircleLoader } from '../components/loaders/CircleLoader.Component';
import { ThreeDotsLoader } from '../components/loaders/ThreeDotsLoader.Component';

const meta = {
  title: 'Components/Loaders',
  parameters: {
    layout: 'centered',
  },

} as Meta;

type Story = StoryObj;

export const ThreeDots: Story = {
  render: () => (
    <ThreeDotsLoader />
  ),
};

export const Circle: Story = {
  render: () => (
    <CircleLoader />
  ),
};

export default meta;
