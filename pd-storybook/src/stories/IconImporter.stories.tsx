import React from 'react';
import type { Meta } from '@storybook/react';

import { IconImporter } from '../components/iconImporter/IconImporter.Component';
import { IconName, iconMap } from '../components/iconImporter/IconMap.Component';
import { Title } from '../components/title/Title.Component';

export default {
  title: 'Components/IconImporter',
  component: IconImporter,
} as Meta;

export const AllUsedIcons = () => {
  const iconNames = Object.keys(iconMap) as IconName[];

  return (
    <div className='pd-flex pd-flex-wrap pd-gap-10'>
      {iconNames.map((name) => (
        <div key={name} className='pd-flex pd-flex-col pd-gap-4 pd-items-center pd-border pd-p-4 pd-rounded-xl pd-shadow-lg'>
          <IconImporter name={name} size={24} />
          <Title as='h6' size='sm' weight='light'>{name}</Title>
        </div>
      ))}
    </div>
  );
};
