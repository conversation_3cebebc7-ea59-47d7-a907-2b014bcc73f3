import type { Meta, StoryObj } from '@storybook/react';
import { fn } from '@storybook/test';

import { FileInput } from '../components/fileInput/FileInput.Component';

const meta = {
  title: 'Components/form/FileInput',
  component: FileInput,
  parameters: {
    layout: 'centered',
  },
  args: {
    name: 'file',
    placeholder: 'Choose file',
    inputClassName: '',
    className: '',
    onChange: fn(),
    error: false,
  },
  argTypes: {
    name: { control: 'text' },
    placeholder: { control: 'text' },
    inputClassName: { control: 'text' },
    className: { control: 'text' },
    error: { control: 'boolean' },
  },
} as Meta<typeof FileInput>;

type Story = StoryObj<typeof FileInput>;

export const File: Story = {};

export default meta;
