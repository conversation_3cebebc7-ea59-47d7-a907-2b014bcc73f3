import type { Meta, StoryObj } from '@storybook/react';
import { fn } from '@storybook/test';

import { Filter } from '../components/filter/Filter.Component';

const meta = {
  title: 'Components/Filter',
  component: Filter,
  parameters: {
    layout: 'centered',
  },
  args: {
    filters: [
      {
        name: 'Category',
        key: 'category',
        options: [
          { id: 'electronics', label: 'Electronics' },
          { id: 'clothing', label: 'Clothing' },
          { id: 'books', label: 'Books' },
        ],
      },
      {
        name: 'Brand',
        key: 'brand',
        options: [
          { id: 'apple', label: 'Apple' },
          { id: 'samsung', label: 'Samsung' },
          { id: 'sony', label: 'Sony' },
        ],
      },
    ],
    onApply: fn(),
  },
  argTypes: {
    filters: { control: 'object' },
    buttonText: { control: 'text' },
    applyText: { control: 'text' },
    theme: { control: 'object' },
  },
} as Meta<typeof Filter>;

export default meta;
type Story = StoryObj<typeof Filter>;

export const Default: Story = {};

export const CustomLabels: Story = {
  args: {
    buttonText: 'Custom Filter Button',
    applyText: 'Custom Apply Text',
  },
};

export const ManyOptions: Story = {
  args: {
    filters: [
      {
        name: 'Colors',
        key: 'colors',
        options: Array.from({ length: 50 }, (_, i) => ({
          id: `color-${i}`,
          label: `Color ${i}`,
        })),
      },
    ],
  },
};
