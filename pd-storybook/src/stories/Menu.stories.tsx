import type { <PERSON><PERSON>, <PERSON>Obj } from '@storybook/react';
import { MemoryRouter } from 'react-router-dom';

import { Menu, MenuItemsProps } from '../components/menu/Menu.Component';

const defaultItems: MenuItemsProps = {
  menuItems: [
    {
      id: '1',
      title: 'Menu',
      icon: 'airplane',
      items: [
        {
          id: '1',
          title: 'Sub Menu Item',
          to: '/',
        },
        {
          id: '2',
          title: 'Sub Menu Item 2',
          to: '/',
        },
      ],
      to: '/',
    },
    {
      id: '2',
      title: 'Another Menu',
      icon: 'buildings',
      items: [
        {
          id: '1',
          title: 'Sub Menu Item',
          to: '/',
        },
        {
          id: '2',
          title: 'Sub Menu Item 2',
          to: '/',
        },
      ],
      to: '/',
    },
    {
      id: '3',
      title: 'Without submenus',
      icon: 'clock',
      to: '/',
    },
    {
      id: '4',
      title: 'With a lot of submenus',
      icon: 'video',
      items: [
        {
          id: '1',
          title: 'Sub Menu Item',
          to: '/',
        },
        {
          id: '2',
          title: 'Sub Menu Item 2',
          to: '/',
        },
        {
          id: '3',
          title: 'Sub Menu Item',
          to: '/',
        },
        {
          id: '4',
          title: 'Sub Menu Item 2',
          to: '/',
        },
        {
          id: '5',
          title: 'Sub Menu Item',
          to: '/',
        },
        {
          id: '6',
          title: 'Sub Menu Item 2',
          to: '/',
        },
      ],
      to: '/',
    },
  ],
};

const meta = {
  title: 'Components/Menu',
  component: Menu,
  parameters: {
    layout: 'centered',
    backgrounds: {
      default: 'dark',
    },
  },
  args: {
    ...defaultItems,
  },
  argTypes: {
    items: {
      control: 'object',
    },
  },
} as Meta<typeof Menu>;

export default meta;

type Story = StoryObj<typeof Menu>;

export const DefaultMenu: Story = {
  render: (args) => (
    <MemoryRouter>
      <Menu {...args} />
    </MemoryRouter>
  ),
};
