import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';

import { LineChartComponent } from '../components/chart/LineChart.Component';
import Theme from '../configurations/Theme.Configuration';

const chartData = [
  { month: 'January', desktop: 186 },
  { month: 'February', desktop: 305 },
  { month: 'March', desktop: 237 },
  { month: 'April', desktop: 73 },
  { month: 'May', desktop: 209 },
  { month: 'June', desktop: 214 },
];

const chartDataMultiple = [
  {
    month: 'January', desktop: 186, mobile: 80, tablet: 110,
  },
  {
    month: 'February', desktop: 305, mobile: 200, tablet: 130,
  },
  {
    month: 'March', desktop: 237, mobile: 120, tablet: 100,
  },
  {
    month: 'April', desktop: 73, mobile: 190, tablet: 60,
  },
  {
    month: 'May', desktop: 209, mobile: 130, tablet: 130,
  },
  {
    month: 'June', desktop: 214, mobile: 140, tablet: 110,
  },
];

const meta = {
  title: 'Components/Charts/LineChart',
  component: LineChartComponent,
  args: {
    data: chartData,
    series: [
      {
        key: 'desktop',
        label: 'Desktop',
        color: Theme.colors.primary,
      },
    ],
    xAxisKey: 'month',
    tickLength: 3,
    marginLeft: 20,
    marginRight: 20,
    marginTop: 0,
    marginBottom: 0,
    lineType: 'natural',
    lineDot: false,
    dotLabel: false,
    dotLabelPosition: 'top',
    dotLabelOffset: 12,
    dotLabelFontSize: 12,
    showXAxis: true,
    tickStyles: {},
    cartesianGrid: false,
    interval: undefined,
    isAnimationActive: false,
    className: '',
  },
  parameters: {
    layout: 'centered',
    key: 'LineChart',
    docs: {
      disable: false,
    },
  },
  argTypes: {
    lineType: {
      control: { type: 'select' },
      options: ['natural', 'linear', 'step', 'monotone'],
    },
    dotLabelPosition: {
      control: { type: 'select' },
      options: ['top', 'bottom', 'left', 'right'],
    },
    lineDot: {
      control: {
        type: 'boolean',
      },
    },
    dotLabel: {
      control: {
        type: 'boolean',
      },
    },
    showXAxis: {
      control: {
        type: 'boolean',
      },
    },
    cartesianGrid: {
      control: {
        type: 'boolean',
      },
    },
  },
  decorators: [
    (Story) => (
      <div className='pd-flex pd-justify-center pd-align-middle pd-h-[250px] pd-w-full'>
        <Story />
      </div>
    ),
  ],
} as Meta<typeof LineChartComponent>;

type Story = StoryObj<typeof LineChartComponent>;

export const Default: Story = {};

export const Size: Story = {
  args: {
    className: 'pd-h-[300px]',
    isAnimationActive: true,
  },
  parameters: {
    docs: {
      disable: true,
    },
  },
};

export const LinearType: Story = {
  args: {
    lineType: 'linear',
    isAnimationActive: true,
  },
  parameters: {
    docs: {
      disable: true,
    },
  },
};

export const StepType: Story = {
  args: {
    lineType: 'step',
    isAnimationActive: true,
  },
  parameters: {
    docs: {
      disable: true,
    },
  },
};

export const WithDot: Story = {
  args: {
    lineDot: true,
    isAnimationActive: true,
  },
  parameters: {
    docs: {
      disable: true,
    },
  },
};

export const WithDotLabel: Story = {
  args: {
    lineDot: true,
    dotLabel: true,
    dotLabelPosition: 'bottom',
    isAnimationActive: true,
  },
  parameters: {
    docs: {
      disable: true,
    },
  },
};

export const WithoutXAxis: Story = {
  args: {
    showXAxis: false,
    isAnimationActive: true,
  },
  parameters: {
    docs: {
      disable: true,
    },
  },
};

export const Multiple: Story = {
  args: {
    data: chartDataMultiple,
    series: [
      {
        key: 'desktop',
        label: 'Desktop',
        color: Theme.colors.primary,
      },
      {
        key: 'mobile',
        label: 'Mobile',
        color: Theme.colors.secondary,
      },
      {
        key: 'tablet',
        label: 'Tablet',
        color: Theme.colors.fadedBlue,
      },
    ],
    isAnimationActive: true,
  },
  parameters: {
    docs: {
      disable: true,
    },
  },
};

export default meta;
