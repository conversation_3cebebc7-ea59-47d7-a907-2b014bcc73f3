import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';

import { Button } from '../components/button/Button.Component';
import { DropdownSimple } from '../components/dropdown/DropdownSimple.Component';
import { IconImporter } from '../components/iconImporter/IconImporter.Component';

const meta = {
  title: 'Components/DropdownSimple',
  component: DropdownSimple,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'Un componente de dropdown simple que permite seleccionar una opción de una lista desplegable.',
      },
    },
    backgrounds: {
      default: 'light',
      values: [
        { name: 'light', value: '#FFFFFF' },
        { name: 'dark', value: '#333333' },
        { name: 'gray', value: '#F5F5F5' },
      ],
    },
    viewport: {
      defaultViewport: 'responsive',
    },
  },
  args: {
    options: [{
      id: '1',
      name: '<PERSON>',
    }, {
      id: '2',
      name: '<PERSON><PERSON>',
    }, {
      id: '3',
      name: '<PERSON>',
    }],
    showAvatar: false,
    disabled: false,
    placement: 'bottom-start',
  },
  argTypes: {
    showAvatar: {
      control: 'boolean',
      description: 'Muestra avatares junto a las opciones',
    },
    disabled: {
      control: 'boolean',
      description: 'Deshabilita el componente',
    },
    placement: {
      control: 'select',
      options: ['top', 'top-start', 'top-end', 'right', 'right-start', 'right-end', 'bottom', 'bottom-start', 'bottom-end', 'left', 'left-start', 'left-end'],
      description: 'Posición donde se mostrará el dropdown',
      table: {
        type: { summary: 'string' },
        defaultValue: { summary: 'bottom-start' },
        category: 'Layout',
      },
    },
    options: {
      control: 'object',
      mapping: {
        id: { control: 'text' },
        name: { control: 'text' },
      },
    },
    setSelectedOption: {
      action: 'option selected',
    },
    hildren: {
      description: 'Elemento que actúa como disparador del dropdown',
      control: false,
      table: {
        type: { summary: 'ReactNode' },
        category: 'Content',
      },
    },
  },
  decorators: [
    (Story) => (
      <div style={{ padding: '2rem', maxWidth: '400px' }}>
        <Story />
      </div>
    ),
  ],
} as Meta<typeof DropdownSimple>;

  type Story = StoryObj<typeof DropdownSimple>;

export const Default: Story = {
  render: (args) => (
    <DropdownSimple
      {...args}
      >
      <Button className='pd-mr-4' size="small">
        Select
      </Button>
    </DropdownSimple>
  ),
};

export const withRender: Story = {
  render: (args) => (
    <DropdownSimple
      {...args}
      options={args.options.map((option) => ({
        ...option,
        renderer: () => <div className='pd-flex pd-py-2'>
          <IconImporter
            name='airplane'
            className='pd-mr-2'
          />
          <p>{option.name}</p>
        </div>,
      }))}
      >
      <Button className='pd-mr-4' size="small">
        Select
      </Button>
    </DropdownSimple>
  ),
};

export const Disabled: Story = {
  args: {
    disabled: true,
  },
  render: (args) => (
    <DropdownSimple
      {...args}
      >
      <Button className='pd-mr-4' size="small" disabled={args.disabled}>
        Select (Disabled)
      </Button>
    </DropdownSimple>
  ),
};

export const WithAvatar: Story = {
  args: {
    showAvatar: true,
  },
  render: (args) => (
    <DropdownSimple
      {...args}
      >
      <Button className='pd-mr-4' size="small">
        Select with Avatar
      </Button>
    </DropdownSimple>
  ),
};

export const CustomTrigger: Story = {
  render: (args) => (
    <DropdownSimple
      {...args}
      >
      <div className="pd-flex pd-items-center pd-cursor-pointer pd-px-3 pd-py-2 pd-border pd-border-gray-300 pd-rounded-md">
        <IconImporter name="image" className="pd-mr-2" />
        <span>Custom Trigger</span>
        <IconImporter name="caretDown" className="pd-ml-2" />
      </div>
    </DropdownSimple>
  ),
};

export const WithCustomWidth: Story = {
  render: (args) => (
    <div className="pd-w-64">
      <DropdownSimple
        {...args}
      >
        <Button className='pd-w-full' size="small">
          Custom Width
        </Button>
      </DropdownSimple>
    </div>
  ),
};

export default meta;
