import type { <PERSON>a, StoryObj } from '@storybook/react';

import { Sidebar } from '../components/sidebar/Sidebar.Component';

const sidebarGroups: any = [
  {
    groupLabel: 'Plataforma',
    items: [
      {
        id: 'store',
        label: 'Tienda',
        icon: 'storefront',
        // to: store.base(),
      },
      {
        id: 'claudia',
        label: '<PERSON>',
        icon: 'brain',
        // to: claudia.base(),
      },
      {
        id: 'inventory',
        label: 'Inventario',
        icon: 'stack',
        // to: inventory.base(),
      },
      {
        id: 'catalog',
        label: 'Catálogo',
        icon: 'package',
        // to: catalog.base(),
      },
      {
        id: 'clients',
        label: 'Clientes',
        icon: 'addressBook',
        // to: clients.base(),
      },
      {
        id: 'providers',
        label: 'Proveedores',
        icon: 'factory',
        subItems: [
          {
            id: 'my-providers',
            label: 'Mis Proveedores',
            // to: '#',
          },
          {
            id: 'provider-catalog',
            label: 'Catálogo de Proveedores',
            // to: '#',
          },
        ],
      },
      {
        id: 'orders',
        label: 'Órdenes',
        icon: 'listChecks',
        subItems: [
          { id: 'purchase-orders', label: '<PERSON>rden<PERSON> de Compra', to: '/purchase-orders' },
          { id: 'sale-orders', label: 'Órdenes de Venta', to: '/sale-orders' },
        ],
      },
      {
        id: 'discounts',
        label: 'Descuentos',
        icon: 'tag',
        subItems: [
          { id: 'catalog-discounts', label: 'Descuentos de Catálogo', to: '#' },
          { id: 'store-discounts', label: 'Descuentos de Tienda', to: '#' },
        ],
      },
    ],
  },
  {
    groupLabel: 'Configuración',
    items: [
      {
        id: 'users',
        label: 'Usuarios',
        icon: 'users',
        to: '#',
      },
    ],
  },
];

const meta = {
  title: 'Components/Sidebar',
  component: Sidebar,
  parameters: {
    layout: 'centered',
    backgrounds: {
      default: 'dark',
    },
  },
  args: {
    sidebarGroups,
    path: '#',
    profileUserInfo: {
      userProfilePhoto: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80',
      userName: 'Juan Rodriguez',
      userRol: 'Admin',
      userEmail: '<EMAIL>',
    },
    headerSection: (
      <div className="pd-flex pd-items-center pd-gap-3 pd-px-2 pd-py-3 pd-mb-2 pd-rounded pd-bg-gray-50">
        <img src="https://img.freepik.com/free-psd/gradient-abstract-logo_23-2150689652.jpg?semt=ais_hybrid&w=740" alt="Suplifai Logo" className="pd-h-8 pd-w-8 pd-object-contain" />
        <div className="pd-flex pd-flex-col">
          <span className="pd-text-base pd-font-bold pd-text-gray-900 pd-leading-tight">Empresa</span>
        </div>
      </div>
    ),
    logoutButton: {
      onClick: () => alert('Logout'),
      children: 'Logout',
    },
    userMenuItems: [
      {
        id: '1', label: 'Ayuda', icon: 'question', onClick: () => console.log('Ayuda clicked!'),
      },
      {
        id: '2', label: 'Configuración', icon: 'settings', onClick: () => console.log('Configuración clicked!'),
      },
    ],
  },
  argTypes: {
    items: {
      control: 'object',
    },
  },
} as Meta<typeof Sidebar>;

export default meta;

type Story = StoryObj<typeof Sidebar>;

export const DefaultSidebar: Story = {
  render: (args) => <Sidebar {...args} />,
};
