import type { Meta, StoryObj } from '@storybook/react';

import { Button } from '../components/button/Button.Component';
import { Tooltip } from '../components/tooltip/Tooltip.Component';
import Theme from '../configurations/Theme.Configuration';

const meta = {
  title: 'Components/Tooltip',
  component: Tooltip,
  parameters: {
    layout: 'centered',
  },
  args: {
    content: 'Tooltip content',
    position: 'top',
    className: '',
    fontColor: '',
    bgColor: '',
  },
  argTypes: {
    content: { control: 'text' },
    position: { control: 'select', options: ['top', 'right', 'bottom', 'left'] },
    className: { control: 'text' },
    fontColor: { control: 'text' },
    bgColor: { control: 'text' },
  },
} as Meta<typeof Tooltip>;

type Story = StoryObj<typeof Tooltip>;

export const Top: Story = {
  args: {
    content: 'Tooltip on top',
    position: 'top',
  },
  render: (args) => (
    <Tooltip {...args}>
      <Button>Hover me</Button>
    </Tooltip>
  ),
};

export const Right: Story = {
  args: {
    content: 'Tooltip on right',
    position: 'right',
  },
  render: (args) => (
    <Tooltip {...args}>
      <Button>Hover me</Button>
    </Tooltip>
  ),
};

export const Bottom: Story = {
  args: {
    content: 'Tooltip on bottom',
    position: 'bottom',
  },
  render: (args) => (
    <Tooltip {...args}>
      <Button>Hover me</Button>
    </Tooltip>
  ),
};

export const Left: Story = {
  args: {
    content: 'Tooltip on left',
    position: 'left',
  },
  render: (args) => (
    <Tooltip {...args}>
      <Button>Hover me</Button>
    </Tooltip>
  ),
};

export const LongText: Story = {
  args: {
    // eslint-disable-next-line max-len
    content: 'Lorem ipsum dolor, sit amet consectetur adipisicing elit. Omnis repudiandae odio, numquam doloremque nemo libero! Fuga voluptatum culpa magni. Veritatis quaerat praesentium ex asperiores unde eum, accusamus corrupti nihil ab.',
    position: 'top',
  },
  render: (args) => (
    <Tooltip {...args}>
      <Button>Hover me</Button>
    </Tooltip>
  ),
};

export const CustomColors: Story = {
  args: {
    content: 'Custom styled tooltip',
    bgColor: Theme.colors.darkBlue,
    fontColor: 'yellow',
    position: 'top',
  },
  render: (args) => (
    <Tooltip {...args}>
      <Button>Hover me</Button>
    </Tooltip>
  ),
};

export const CustomClass: Story = {
  args: {
    content: 'Custom styled tooltip',
    className: '!pd-bg-fadedGreen !pd-text-positive !pd-rounded-xxl !pd-text-xxlg ',
    position: 'top',
  },
  render: (args) => (
    <Tooltip {...args}>
      <Button>Hover me</Button>
    </Tooltip>
  ),
};

export const DynamicContent: Story = {
  render: (args) => (
    <Tooltip {...args} content={`Dynamic content at ${new Date().toLocaleTimeString()}`}>
      <Button>Hover for dynamic content</Button>
    </Tooltip>
  ),
};

export default meta;
