import { action } from '@storybook/addon-actions';
import type { Meta, StoryObj } from '@storybook/react';
import { useEffect, useState } from 'react';

import { AutocompleteInput } from '../components/autoCompleteInput/AutocompleteInput.Component';
import { OptionsDropdownProps } from '../components/dropdown/DropdownWithSearch.Component';

const baseInputProps = {
  value: '',
  placeholder: 'Type what you wish...',
  disabled: false,
  inputClassName: '',
  onChange: () => {},
  onBlur: () => {},
};

const baseDropdownProps = {
  options: [],
  onSelect: () => {},
  children: null,
};

const meta: Meta<typeof AutocompleteInput> = {
  title: 'Components/Input/AutocompleteInput',
  component: AutocompleteInput,
  args: {
    dropdownSuggestionsProps: {
      ...baseDropdownProps,
    },
    inputProps: {
      ...baseInputProps,
      name: 'search-input',
    },
  },
  argTypes: {
    dropdownSuggestionsProps: {
      control: 'object',
    },
    inputProps: {
      control: 'object',
      expanded: true,
    },
  },
};

export default meta;

export const Default: StoryObj<typeof AutocompleteInput> = {
  render: (args) => {
    const allOptions = Array.from({ length: 50 }, (_, i) => ({
      id: `opt-${i}`,
      name: `Option ${i + 1}`,
      renderer: (value: unknown) => {
        const option = value as OptionsDropdownProps;
        return (
          <div className="pd-p-2 pd-gap-1 pd-text-xsm">{option.name}</div>
        );
      },
    }));

    const initialLoadCount = 10;
    const loadMoreCount = 10;

    const [value, setValue] = useState('');
    const [visibleOptions, setVisibleOptions] = useState(allOptions.slice(0, initialLoadCount));
    const [loading, setLoading] = useState(false);
    const [hasMore, setHasMore] = useState(true);
    const [selectedOption, setSelectedOption] = useState<OptionsDropdownProps | null>(null);
    const [isOpen, setIsOpen] = useState(false);

    useEffect(() => {
      if (!selectedOption && value.length > 0) {
        setIsOpen(true);
      } else {
        setIsOpen(false);
      }
    }, [value, selectedOption]);

    const loadMoreOptions = () => {
      if (loading || !hasMore) return;

      setLoading(true);
      setTimeout(() => {
        const currentLength = visibleOptions.length;
        const nextOptions = allOptions.slice(currentLength, currentLength + loadMoreCount);

        setVisibleOptions([...visibleOptions, ...nextOptions]);
        setLoading(false);

        if (visibleOptions.length + nextOptions.length >= allOptions.length) {
          setHasMore(false);
        }
      }, 500);
    };

    const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
      const newValue = event.target.value;
      setValue(newValue);
      action('onChange')(event);

      const filtered = allOptions.filter((option) => option.name.toLowerCase().includes(newValue.toLowerCase()));
      setVisibleOptions(filtered.slice(0, initialLoadCount));
      setHasMore(filtered.length > initialLoadCount);
      setSelectedOption(null);
    };

    const handleSelect = (option: OptionsDropdownProps) => {
      setSelectedOption(option);
      setValue(option.name || '');
      setIsOpen(false);
      action('onSelect')(option);
      setHasMore(false);
    };

    const handleBlur = (event: React.FocusEvent<HTMLInputElement>) => {
      setTimeout(() => {
        setIsOpen(false);
        action('onBlur')(event);
      }, 100);
    };

    const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
      const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;
      if (scrollTop + clientHeight >= scrollHeight - 20) {
        loadMoreOptions();
      }
    };

    return (
      <div className="pd-flex pd-gap-5 pd-w-full">
        <div className="pd-flex-1">
          <AutocompleteInput
            {...args}
            inputProps={{
              ...args.inputProps,
              value,
              onChange: handleInputChange,
              onBlur: handleBlur,
              placeholder: 'Advanced Configuration Input',
              inputClassName: 'pd-border-2 pd-border-purple-500 pd-p-2 pd-rounded pd-w-full',
            }}
            dropdownSuggestionsProps={{
              ...args.dropdownSuggestionsProps,
              options: visibleOptions,
              isOpen,
              onSelect: handleSelect,
              handleScroll,
              loading,
              hasMore,
            }}
          />
          {loading && <p>Loading more options...</p>}
        </div>
        <div className="pd-flex-1">
          <h4>Status:</h4>
          <p>Input Value: {value}</p>
          <p>Visible Options: {visibleOptions.length}</p>
          <p>Loading: {loading ? 'Yes' : 'No'}</p>
          <p>Has More Options: {hasMore ? 'Yes' : 'No'}</p>
          <p>Selected Option: {selectedOption ? selectedOption.name : 'None'}</p>
        </div>
      </div>
    );
  },
};

export const Disabled: StoryObj<typeof AutocompleteInput> = {
  render: (args) => <AutocompleteInput {...args} inputProps={{ ...args.inputProps, disabled: true }}/>,
};

export const WithAvatarOptions: StoryObj<typeof AutocompleteInput> = {
  render: (args) => <AutocompleteInput
    {...args}
    inputProps={{ ...args.inputProps, value: 'option' }}
    dropdownSuggestionsProps={{
      ...args.dropdownSuggestionsProps,
      isOpen: true,
      options: [
        {
          id: 'option-1',
          name: 'Option 1',
          renderer: (value: unknown) => {
            const option = value as OptionsDropdownProps;

            return (
              <div className={
                'pd-flex pd-items-center pd-gap-2 pd-p-2 pd-text-xsm hover:pd-bg-gray-100 pd-cursor-pointer pd-transition-colors pd-duration-200 pd-ease-in-out'
              }>
                <img
                  src="https://randomuser.me/api/portraits/men/1.jpg"
                  alt={option.name}
                  className='pd-w-10 pd-h-10 pd-rounded-full'
                />
                <span>{option.name}</span>
              </div>
            );
          },
        },
        {
          id: 'option-2',
          name: 'Option 2',
          renderer: (value: unknown) => {
            const option = value as OptionsDropdownProps;

            return (
              <div className={
                'pd-flex pd-items-center pd-gap-2 pd-p-2 pd-text-xsm hover:pd-bg-gray-100 pd-cursor-pointer pd-transition-colors pd-duration-200 pd-ease-in-out'
              }>
                <img
                  src="https://randomuser.me/api/portraits/men/1.jpg"
                  alt={option.name}
                  className='pd-w-10 pd-h-10 pd-rounded-full'
                />
                <span>{option.name}</span>
              </div>
            );
          },
        },
      ],
    }}
  />,
};

export const LoadingState: StoryObj<typeof AutocompleteInput> = {
  render: (args) => {
    const initialOptions = [
      { id: 'opt-a', name: 'Loaded Option A' },
      { id: 'opt-b', name: 'Loaded Option B' },
      { id: 'opt-c', name: 'Another Loaded Option C' },
      { id: 'opt-d', name: 'Different Loaded Option D' },
    ];
    const [value, setValue] = useState('');
    const [options, setOptions] = useState<OptionsDropdownProps[]>([]);
    const [isLoading, setIsLoading] = useState(true);
    const [selectedOption, setSelectedOption] = useState<OptionsDropdownProps | null>(null);
    const [isOpen, setIsOpen] = useState(false);
    useEffect(() => {
      if (!selectedOption && value.length > 0) {
        setIsOpen(true);
      } else {
        setIsOpen(false);
      }
    }, [value, selectedOption]);

    useEffect(() => {
      const timer = setTimeout(() => {
        setOptions(initialOptions);
        setIsLoading(false);
      }, 2000);

      return () => clearTimeout(timer);
    }, []);

    const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
      const newValue = event.target.value;
      setValue(newValue);
      action('onChange')(event);
      const newFilteredOptions = initialOptions.filter((option) => option.name.toLowerCase().includes(newValue.toLowerCase()));
      setOptions(newFilteredOptions);
      setSelectedOption(null);
    };

    const handleSelect = (option: OptionsDropdownProps) => {
      action('onSelect')(option);
      setValue(option.name || '');
      setOptions(initialOptions);
    };

    const handleBlur = (event: React.FocusEvent<HTMLInputElement>) => {
      setTimeout(() => {
        setIsOpen(false);
        action('onBlur')(event);
      }, 100);
    };

    return (
      <div className="pd-flex pd-gap-5 pd-w-full">
        <div className="pd-flex-1">
          <AutocompleteInput
            {...args}
            inputProps={{
              ...args.inputProps,
              onChange: handleInputChange,
              onBlur: handleBlur,
              value,
              disabled: isLoading,
            }}
            dropdownSuggestionsProps={{
              ...args.dropdownSuggestionsProps,
              options: isLoading ? [] : options,
              onSelect: handleSelect,
              isOpen,
            }}
          />
        </div>
        <div className="pd-flex-1">
          <h4>Status:</h4>
          <p>Input Value: {value}</p>
          <p>Loading: {isLoading ? 'Yes' : 'No'}</p>
          <p>Options: {JSON.stringify(options, null, 2)}</p>
        </div>
      </div>
    );
  },
};

export const WithCustomInputDisabled: StoryObj<typeof AutocompleteInput> = {
  render: (args) => <AutocompleteInput
    {...args}
    inputProps= {{
      ...args.inputProps,
      disabled: true,
      placeholder: 'Custom input',
      inputClassName: 'pd-border-2 pd-border-blue-500 pd-p-2 pd-rounded pd-w-full',
    }}
  />,
};

export const WidthCustomInputWithSuggestions: StoryObj<typeof AutocompleteInput> = {
  render: (args) => {
    const initialOptions = [
      { id: 'opt-a', name: 'Option Cargada A' },
      { id: 'opt-b', name: 'Option Cargada B' },
      { id: 'opt-c', name: 'Another Option Cargada C' },
      { id: 'opt-d', name: 'Different Option Cargada D' },
    ];
    const [value, setValue] = useState('');
    const [options, setOptions] = useState<OptionsDropdownProps[]>([]);
    const [selectedOption, setSelectedOption] = useState<OptionsDropdownProps | null>(null);
    const [isOpen, setIsOpen] = useState(false);
    useEffect(() => {
      if (!selectedOption && value.length > 0) {
        setIsOpen(true);
      } else {
        setIsOpen(false);
      }
    }, [value, selectedOption]);

    const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
      const newValue = event.target.value;
      setValue(newValue);
      action('onChange')(event);
      const newFilteredOptions = initialOptions.filter((option) => option.name.toLowerCase().includes(newValue.toLowerCase()));
      setOptions(newFilteredOptions);
      setSelectedOption(null);
    };

    const handleSelect = (option: OptionsDropdownProps) => {
      action('onSelect')(option);
      setValue(option.name || '');
      setOptions(initialOptions);
    };

    const handleBlur = (event: React.FocusEvent<HTMLInputElement>) => {
      setTimeout(() => {
        setIsOpen(false);
        action('onBlur')(event);
      }, 100);
    };

    return (
      <AutocompleteInput
        {...args}
        inputProps= {{
          ...args.inputProps,
          onChange: handleInputChange,
          onBlur: handleBlur,
          value,
          placeholder: 'Custom input',
          inputClassName: 'pd-border-2 pd-border-blue-500 pd-p-2 pd-rounded pd-w-full',
        }}
        dropdownSuggestionsProps={{
          ...args.dropdownSuggestionsProps,
          isOpen,
          onSelect: handleSelect,
          options,
        }}
      />
    );
  },
};

export const WidthCustomInputWithAvatarOptions: StoryObj<typeof AutocompleteInput> = {
  render: (args) => {
    const [value, setValue] = useState('');

    const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
      const newValue = event.target.value;
      setValue(newValue);
      action('onChange')(event);
    };

    return (
      <AutocompleteInput
        {...args}
        inputProps= {{
          ...args.inputProps,
          value,
          placeholder: 'Custom input',
          inputClassName: 'pd-border-2 pd-border-blue-500 pd-p-2 pd-rounded pd-w-full',
          onChange: handleInputChange,
        }}
        dropdownSuggestionsProps={{
          ...args.dropdownSuggestionsProps,
          isOpen: true,
          options: [
            {
              id: 'option-1',
              name: 'Option 1',
              renderer: (cardValue: unknown) => {
                const option = cardValue as OptionsDropdownProps;

                return (
                  <div className='pd-flex pd-items-center pd-gap-2 pd-p-2 pd-text-xsm'>
                    <img
                      src="https://randomuser.me/api/portraits/men/1.jpg"
                      alt={option.name}
                      className='pd-w-10 pd-h-10 pd-rounded-full'
                />
                    <span>{option.name}</span>
                  </div>
                );
              },
            },
            {
              id: 'option-2',
              name: 'Option 2',
              renderer: (cardValue: unknown) => {
                const option = cardValue as OptionsDropdownProps;

                return (
                  <div className='pd-flex pd-items-center pd-gap-2 pd-p-2 pd-text-xsm'>
                    <img
                      src="https://randomuser.me/api/portraits/men/1.jpg"
                      alt={option.name}
                      className='pd-w-10 pd-h-10 pd-rounded-full'
                />
                    <span>{option.name}</span>
                  </div>
                );
              },
            },
          ],
        }}
      />
    );
  },
};

export const InfiniteScrollWithoutSelect: StoryObj<typeof AutocompleteInput> = {
  render: (args) => {
    const allOptions = Array.from({ length: 50 }, (_, i) => ({
      id: `opt-${i}`,
      name: `Option ${i + 1}`,
    }));

    const initialLoadCount = 5;
    const loadMoreCount = 5;

    const [visibleOptions, setVisibleOptions] = useState(allOptions.slice(0, initialLoadCount));
    const [loading, setLoading] = useState(false);
    const [hasMore, setHasMore] = useState(true);

    const loadMoreOptions = () => {
      if (loading || !hasMore) return;

      setLoading(true);
      setTimeout(() => {
        const currentLength = visibleOptions.length;
        const nextOptions = allOptions.slice(currentLength, currentLength + loadMoreCount);

        setVisibleOptions([...visibleOptions, ...nextOptions]);
        setLoading(false);

        if (visibleOptions.length + nextOptions.length >= allOptions.length) {
          setHasMore(false);
        }
      }, 500);
    };

    const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
      const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;
      if (scrollTop + clientHeight >= scrollHeight - 20) {
        loadMoreOptions();
      }
    };

    return (
      <div className="pd-flex pd-gap-5 pd-w-full">
        <div className="pd-flex-1">
          <AutocompleteInput
            {...args}

            inputProps={{
              ...args.inputProps,
              value: '',
              onChange: () => {},
              disabled: true,
            }}
            dropdownSuggestionsProps={{
              ...args.dropdownSuggestionsProps,
              options: visibleOptions,
              isOpen: true,
              handleScroll,
            }}
          />
          {loading && <p>Loading more options...</p>}
          {!hasMore && <p>No more options to load.</p>}
        </div>
        <div className="pd-flex-1">
          <h4>Status:</h4>
          <p>Visible Options: {visibleOptions.length}</p>
          <p>Loading: {loading ? 'Yes' : 'No'}</p>
          <p>Has More Options: {hasMore ? 'Yes' : 'No'}</p>
        </div>
      </div>
    );
  },
};
