import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';

import { PieChartComponent } from '../components/chart/PieChart.Component';
import Theme from '../configurations/Theme.Configuration';

const chartData = [
  { name: 'Desktop', value: 400 },
  { name: 'Mobile', value: 300 },
  { name: 'Tablet', value: 200 },
];

const chartDataCustom = [
  { name: 'Sales', value: 120 },
  { name: 'Purchases', value: 90 },
  { name: 'Returns', value: 60 },
  { name: 'Others', value: 30 },
];

const meta = {
  title: 'Components/Charts/PieChart',
  component: PieChartComponent,
  args: {
    data: chartData,
    series: [
      { key: 'Desktop', label: 'Desktop', color: Theme.colors.primary },
      { key: 'Mobile', label: 'Mobile', color: Theme.colors.secondary },
      { key: 'Tablet', label: 'Tablet', color: Theme.colors.fadedBlue },
    ],
    dataKey: 'value',
    nameKey: 'name',
    innerRadius: 0,
    outerRadius: '80%',
    showLegend: false,
    legendVerticalAlign: 'bottom',
    marginRight: 0,
    marginLeft: 0,
    marginTop: 0,
    marginBottom: 0,
    showLabel: false,
    showLabelLine: false,
    isAnimationActive: false,
    className: '',
  },
  parameters: {
    layout: 'centered',
    key: 'PieChart',
    docs: {
      disable: false,
    },
  },
  argTypes: {
    legendVerticalAlign: {
      control: { type: 'select' },
      options: ['top', 'bottom'],
    },
    innerRadius: {
      control: {
        type: 'number',
      },
    },
    showLegend: {
      control: {
        type: 'boolean',
      },
    },
    showLabel: {
      control: {
        type: 'boolean',
      },
    },
    showLabelLine: {
      control: {
        type: 'boolean',
      },
    },
    isAnimationActive: {
      control: {
        type: 'boolean',
      },
    },
  },
  decorators: [
    (Story) => (
      <div className='pd-flex pd-justify-center pd-align-middle pd-h-[300px] pd-w-full'>
        <Story />
      </div>
    ),
  ],
} as Meta<typeof PieChartComponent>;

type Story = StoryObj<typeof PieChartComponent>;

export const Default: Story = {};

export const WithLegend: Story = {
  args: {
    showLegend: true,
    isAnimationActive: true,
  },
  parameters: {
    docs: {
      disable: true,
    },
  },
};

export const WithLabels: Story = {
  args: {
    showLabel: true,
    marginTop: 20,
    isAnimationActive: true,
  },
  parameters: {
    docs: {
      disable: true,
    },
  },
};

export const WithLabelLines: Story = {
  args: {
    showLabelLine: true,
    showLabel: true,
    marginTop: 20,
    isAnimationActive: true,
  },
  parameters: {
    docs: {
      disable: true,
    },
  },
};

export const CustomColors: Story = {
  args: {
    data: chartDataCustom,
    series: [
      { key: 'Sales', label: 'Sales', color: '#00C49F' },
      { key: 'Purchases', label: 'Purchases', color: '#FFBB28' },
      { key: 'Returns', label: 'Returns', color: '#FF8042' },
      { key: 'Others', label: 'Others', color: '#8884d8' },
    ],
    showLegend: true,
    isAnimationActive: true,
  },
  parameters: {
    docs: {
      disable: true,
    },
  },
};

export const Donut: Story = {
  args: {
    innerRadius: 40,
    outerRadius: 100,
    isAnimationActive: true,
  },
  parameters: {
    docs: {
      disable: true,
    },
  },
};

export default meta;
