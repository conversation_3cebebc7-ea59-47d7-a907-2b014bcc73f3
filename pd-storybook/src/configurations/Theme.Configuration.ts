const Theme = {
  colors: {
    primary: '#17448d',
    primaryVariants: {
      100: '#C9D1E5',
      200: '#A3B1D6',
      300: '#7B8BC2',
      400: '#4F66B2',
      500: '#2B5EC9',
      600: '#17448D',
      700: '#102E5C',
    },
    primaryHover: '#2B5EC9',
    secondary: '#0A88ED',
    secondaryBlue: '#459CED',
    secondaryMagenta: '#D377F3',
    transparent: 'transparent',
    positive: '#5D923D',
    negative: '#E55D57',
    white: '#FFFFFF',
    dark: {
      900: '#000000',
      800: '#101214',
      700: '#1D1F24',
      600: '#3A3D44',
      500: '#6B6E75',
      400: '#A3A5AB',
      300: '#D3D5DA',
      200: '#F6F7FA',
      100: '#FAFAFA',
    },
    line: '#E0E3E8',
    fadedViolet: '#CFCBE5',
    fadedRed: '#F7D6D2',
    fadedBlue: '#CEE4F8',
    fadedGray: '#F4F6F9',
    fadedGreen: '#E5F3DD',
    fadedPeach: '#FADAC2',
    fadedYellow: '#F9FAC2',
    darkBlue: '#222683',
    toscaBlue: '#4A86E4',
    orange: '#F4A261',
    lightGray: '#FAFAFA',
    fadedOrange: '#feebe2',
    fadedTosca: '#bed6fb',
  },
  borderRadius: {
    DEFAULT: '8px',
    xl: '12px',
    xxl: '16px',
    rounded: '50%',
  },
  fontFamily: {
    poppins: ['Geist', 'Poppins', 'Open Sans'],
  },
  fontSize: {
    xxsm: ['12px', { lineHeight: '16px' }] as [string, { lineHeight: string }],
    xsm: ['13px', { lineHeight: '18px' }] as [string, { lineHeight: string }],
    sm: ['14px', { lineHeight: '20px' }] as [string, { lineHeight: string }],
    base: ['16px', { lineHeight: '24px' }] as [string, { lineHeight: string }],
    mdPlus: ['18px', { lineHeight: '28px' }] as [string, { lineHeight: string }],
    lg: ['20px', { lineHeight: '28px' }] as [string, { lineHeight: string }],
    lgPlus: ['22px', { lineHeight: '30px' }] as [string, { lineHeight: string }],
    xlg: ['24px', { lineHeight: '32px' }] as [string, { lineHeight: string }],
    xxlg: ['26px', { lineHeight: '34px' }] as [string, { lineHeight: string }],
  },
  fontWeight: {
    light: '300',
    regular: '400',
    medium: '500',
    semiBold: '600',
    bold: '700',
  },
  buttonSize: {
    small: {
      padding: '7px 14px',
    },
    medium: {
      padding: '10px 14px',
    },
    large: {
      padding: '12px 22px',
    },
  },
  shadow: {
    DEFAULT: '0 0 3px 0 rgb(0 0 0 / 0.1), 0 0 2px 0 rgb(0 0 0 / 0.1)',
    lgSquare: '0 0 6px 0 rgb(0 0 0 / 0.1), 0 0 4px 0 rgb(0 0 0 / 0.1)',
    lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
  },
  minWidth: {
    quarter: '25%',
    third: '33.33%',
    middle: '50%',
    fifth: '20%',
  },
  maxWidth: {
    quarter: '25%',
    third: '33.33%',
    middle: '50%',
    fifth: '20%',
  },
  width: {
    0: '0px',
    1: '0.25rem',
    2: '0.5rem',
    3: '0.75rem',
    4: '1rem',
    5: '1.25rem',
    6: '1.5rem',
    8: '2rem',
    10: '2.5rem',
    12: '3rem',
    16: '4rem',
    20: '5rem',
    24: '6rem',
    28: '7rem',
    32: '8rem',
    36: '9rem',
    40: '10rem',
    44: '11rem',
    48: '12rem',
    52: '13rem',
    56: '14rem',
    60: '15rem',
    64: '16rem',
    72: '18rem',
    80: '20rem',
    96: '24rem',
    auto: 'auto',
    '1/2': '50%',
    '1/3': '33.333333%',
    '2/3': '66.666667%',
    '1/4': '25%',
    '2/4': '50%',
    '3/4': '75%',
    '1/5': '20%',
    '2/5': '40%',
    '3/5': '60%',
    '4/5': '80%',
    full: '100%',
    screen: '100vw',
    min: 'min-content',
    max: 'max-content',
    fit: 'fit-content',
  },
  height: {
    0: '0px',
    1: '0.25rem',
    2: '0.5rem',
    3: '0.75rem',
    4: '1rem',
    5: '1.25rem',
    6: '1.5rem',
    8: '2rem',
    10: '2.5rem',
    12: '3rem',
    16: '4rem',
    20: '5rem',
    24: '6rem',
    28: '7rem',
    32: '8rem',
    36: '9rem',
    40: '10rem',
    44: '11rem',
    48: '12rem',
    52: '13rem',
    56: '14rem',
    60: '15rem',
    64: '16rem',
    72: '18rem',
    80: '20rem',
    96: '24rem',
    auto: 'auto',
    '1/2': '50%',
    '1/3': '33.333333%',
    '2/3': '66.666667%',
    '1/4': '25%',
    '2/4': '50%',
    '3/4': '75%',
    '1/5': '20%',
    '2/5': '40%',
    '3/5': '60%',
    '4/5': '80%',
    full: '100%',
    screen: '100vh',
    min: 'min-content',
    max: 'max-content',
    fit: 'fit-content',
  },
};

export default Theme;
