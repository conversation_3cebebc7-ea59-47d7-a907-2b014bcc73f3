import { css } from '@emotion/react';

import theme from '../configurations/Theme.Configuration';
import { CommonTextProps } from '../types/StyledGlobalText.Type';

export const StyledGlobalTextStyles = (props: CommonTextProps) => css`
        font-size: ${theme.fontSize[props.size || 'base']};
        font-weight: ${theme.fontWeight[props.weight || 'medium']};
        text-decoration-line: ${(props.underline ? 'underline' : 'none')};
    `;
