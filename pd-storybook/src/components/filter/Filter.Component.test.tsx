import { fireEvent, render, screen } from '@testing-library/react';

import { Filter, FilterGroup, FilterProps } from './Filter.Component';

const defaultProps: FilterProps = {
  filters: [
    {
      name: 'Category',
      key: 'category',
      options: [
        { id: 'electronics', label: 'Electronics' },
        { id: 'clothing', label: 'Clothing' },
        { id: 'books', label: 'Books' },
      ],
    },
    {
      name: 'Brand',
      key: 'brand',
      options: [
        { id: 'apple', label: 'Apple' },
        { id: 'samsung', label: 'Samsung' },
        { id: 'sony', label: 'Sony' },
      ],
    },
  ],
  onApply: jest.fn(),
  buttonText: 'Filter',
  applyText: 'Apply',
  className: 'test-class',
};

describe('Filter Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render the filter button with correct text', () => {
    render(<Filter {...defaultProps} />);
    expect(screen.getByTestId('filter-button')).toHaveTextContent('Filter');
  });

  it('should open the filter menu when the filter button is clicked', () => {
    render(<Filter {...defaultProps} />);
    fireEvent.click(screen.getByTestId('filter-button'));
    expect(screen.getByTestId('filter-menu')).toBeInTheDocument();
  });

  it('should render the correct number of filter groups and options', () => {
    render(<Filter {...defaultProps} />);
    fireEvent.click(screen.getByTestId('filter-button'));
    expect(screen.getByText('Category')).toBeInTheDocument();
    expect(screen.getByText('Brand')).toBeInTheDocument();
    expect(screen.getByLabelText('Category - Electronics')).toBeInTheDocument();
    expect(screen.getByLabelText('Category - Clothing')).toBeInTheDocument();
    expect(screen.getByLabelText('Category - Books')).toBeInTheDocument();
    expect(screen.getByLabelText('Brand - Apple')).toBeInTheDocument();
    expect(screen.getByLabelText('Brand - Samsung')).toBeInTheDocument();
    expect(screen.getByLabelText('Brand - Sony')).toBeInTheDocument();
  });

  it('should call onApply with selected filters when apply button is clicked', () => {
    render(<Filter {...defaultProps} />);
    fireEvent.click(screen.getByTestId('filter-button'));
    fireEvent.click(screen.getByLabelText('Category - Electronics'));
    fireEvent.click(screen.getByLabelText('Brand - Apple'));
    fireEvent.click(screen.getByTestId('apply-button'));
    const selectedOptions: FilterGroup[] = [
      {
        name: 'Category',
        key: 'category',
        options: [
          { id: 'electronics', label: 'Electronics', selected: true },
          { id: 'clothing', label: 'Clothing' },
          { id: 'books', label: 'Books' },
        ],
      },
      {
        name: 'Brand',
        key: 'brand',
        options: [
          { id: 'apple', label: 'Apple', selected: true },
          { id: 'samsung', label: 'Samsung' },
          { id: 'sony', label: 'Sony' },
        ],
      },
    ];

    expect(defaultProps.onApply).toHaveBeenCalledWith(selectedOptions);
  });

  it('should toggle filter options correctly', () => {
    render(<Filter {...defaultProps} />);
    fireEvent.click(screen.getByTestId('filter-button'));
    const electronicsCheckbox = screen.getByLabelText('Category - Electronics');
    fireEvent.click(electronicsCheckbox);
    expect(electronicsCheckbox).toBeChecked();
    fireEvent.click(electronicsCheckbox);
    expect(electronicsCheckbox).not.toBeChecked();
  });

  it('should apply the correct className', () => {
    render(<Filter {...defaultProps} />);
    expect(screen.getByTestId('filter-container')).toHaveClass('test-class');
  });

  it('should close the filter menu when apply button is clicked', () => {
    render(<Filter {...defaultProps} />);
    fireEvent.click(screen.getByTestId('filter-button'));
    fireEvent.click(screen.getByTestId('apply-button'));
    expect(screen.queryByTestId('filter-menu')).not.toBeInTheDocument();
  });

  it('should render custom buttonText and applyText', () => {
    render(<Filter {...defaultProps} buttonText="Custom Filter" applyText="Custom Apply" />);
    expect(screen.getByTestId('filter-button')).toHaveTextContent('Custom Filter');
    fireEvent.click(screen.getByTestId('filter-button'));
    expect(screen.getByTestId('apply-button')).toHaveTextContent('Custom Apply');
  });

  it('should render default buttonText when not provided', () => {
    const propsWithoutButtonText = { ...defaultProps, buttonText: undefined };
    render(<Filter {...propsWithoutButtonText} />);
    expect(screen.getByTestId('filter-button')).toHaveTextContent('Filter');
  });

  it('should render default applyText when not provided', () => {
    const propsWithoutApplyText = { ...defaultProps, applyText: undefined };
    render(<Filter {...propsWithoutApplyText} />);
    fireEvent.click(screen.getByTestId('filter-button'));
    expect(screen.getByTestId('apply-button')).toHaveTextContent('Apply');
  });

  it('should work with default empty filters array', () => {
    render(<Filter />);
    fireEvent.click(screen.getByTestId('filter-button'));
    expect(screen.getByText('No filters available')).toBeInTheDocument();
  });

  it('should not show "No filters available" when filters are provided', () => {
    render(<Filter {...defaultProps} />);
    fireEvent.click(screen.getByTestId('filter-button'));
    expect(screen.queryByText('No filters available')).not.toBeInTheDocument();
  });

  it('should work with default onApply function', () => {
    render(<Filter />);
    fireEvent.click(screen.getByTestId('filter-button'));
    fireEvent.click(screen.getByTestId('apply-button'));
    expect(screen.queryByTestId('filter-menu')).not.toBeInTheDocument();
  });

  it('should close the filter menu when the user clicks outside', () => {
    render(<Filter {...defaultProps} />);
    fireEvent.click(screen.getByTestId('filter-button'));
    expect(screen.getByTestId('filter-menu')).toBeInTheDocument();
    fireEvent.mouseDown(document);
    expect(screen.queryByTestId('filter-menu')).not.toBeInTheDocument();
  });

  it('should apply initial selection correctly', () => {
    const propsWithSelectedFilters: FilterProps = {
      ...defaultProps,
      filters: defaultProps.filters?.map((group, i) => ({
        ...group,
        options: group.options.map((option, j) => ({ ...option, selected: i === 0 && j === 0 })),
      })),
    };

    render(<Filter {...propsWithSelectedFilters} />);
    fireEvent.click(screen.getByTestId('filter-button'));
    expect(screen.getByLabelText('Category - Electronics')).toBeChecked();
    expect(screen.getByLabelText('Brand - Apple')).not.toBeChecked();
  });
});
