import {
  useCallback,
  useEffect,
  useState,
} from 'react';

import OutsideClick from '../../utils/OutsideClick';
import { Button } from '../button/Button.Component';
import { IconImporter } from '../iconImporter/IconImporter.Component';

import {
  CheckboxLabel,
  FilterButton,
  FilterContainer,
  FilterGroupContainer,
  FilterGroupTitle,
  FilterMenu,
} from './Filter.Style';

export interface FilterOption {
  id: string;
  label: string;
  selected?: boolean;
}

export interface FilterGroup< T extends string = string> {
  name: string;
  key: T;
  options: FilterOption[];
}

export interface FilterProps<T extends string = string> {
  filters?: FilterGroup<T>[];
  onApply?: (selectedFilters: FilterGroup[]) => void;
  buttonText?: string;
  applyText?: string;
  className?: string;
}

export const Filter = <T extends string = string>({
  filters = [],
  onApply = () => {},
  buttonText,
  applyText,
  className,
  ...props
}: FilterProps<T>) => {
  const [isOpen, setIsOpen] = useState(false);
  const [filtersState, setFiltersState] = useState<FilterGroup<T>[]>([]);

  useEffect(() => {
    if (filters.length === 0 && filtersState.length === 0) return;

    const clonedFilters = filters.map((group) => ({
      ...group,
      options: group.options.map((option) => ({
        ...option,
      })),
    }));

    setFiltersState(clonedFilters);
  }, [filters]);

  const handleFilterChange = useCallback((option: FilterOption) => {
    const newOption = option;

    newOption.selected = !option.selected;
    setFiltersState([...filtersState]);
  }, [filtersState]);

  const handleApply = useCallback(() => {
    onApply(filtersState);
    setIsOpen(false);
  }, [onApply, filtersState]);

  return (
    <FilterContainer className={className} data-testid='filter-container' {...props}>
      <FilterButton
        onClick={() => setIsOpen(!isOpen)}
        aria-expanded={isOpen}
        aria-controls="filter-menu"
        data-testid="filter-button"
      >
        <IconImporter name="funnelSimple" />
        {buttonText || 'Filter'}
      </FilterButton>
      {isOpen && (
        <OutsideClick onOutsideClick={() => setIsOpen(false)}>
          <FilterMenu
            id="filter-menu"
            role="menu"
            data-testid="filter-menu"
          > {filtersState.length === 0 && <p>No filters available</p>}
            {filtersState.map((group) => (
              <FilterGroupContainer key={group.key as string}>
                <FilterGroupTitle>{group.name}</FilterGroupTitle>
                {group.options.map((option) => (
                  <CheckboxLabel key={option.id}>
                    <input
                      type="checkbox"
                      checked={!!option.selected}
                      onChange={() => handleFilterChange(option)}
                      aria-label={`${group.name} - ${option.label}`}
                      data-testid={`checkbox-${group.name}-${option.id}`}
                    />
                    {option.label}
                  </CheckboxLabel>
                ))}
              </FilterGroupContainer>
            ))}
            <Button size='small' onClick={handleApply} data-testid="apply-button" className='pd-self-center pd-mt-2 !pd-py-1 !pd-text-sm'>
              {applyText || 'Apply'}
            </Button>
          </FilterMenu>
        </OutsideClick>
      )}
    </FilterContainer>
  );
};
