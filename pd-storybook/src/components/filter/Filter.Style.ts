import styled from '@emotion/styled';

import Theme from '../../configurations/Theme.Configuration';

export const FilterContainer = styled.div`
    position: relative;
`;

export const FilterButton = styled.button`
    padding: 6px 12px;
    background-color: ${Theme.colors.white};
    border: 1px solid ${Theme.colors.line};
    border-radius: ${Theme.borderRadius.DEFAULT};
    cursor: pointer;
    display: flex;
    gap: 8px;
    justify-content: space-between;
    align-items: center;
    font-size: ${Theme.fontSize.xsm};
    transition: all 300ms ease-in-out;

    &:hover {
    background-color: ${Theme.colors.line};
    }
    height: 36px;
`;

export const FilterMenu = styled.div`
    position: absolute;
    top: 2.5rem;
    right: 0;
    background-color: ${Theme.colors.white};
    border: 1px solid ${Theme.colors.line};
    border-radius: ${Theme.borderRadius.DEFAULT};
    padding: 12px;
    z-index: 100;
    min-width: 200px;
    display: flex;
    flex-direction: column;
    gap: 8px;
    max-height: 30rem;
    overflow-y: auto;
`;

export const FilterGroupContainer = styled.div`
    display: flex;
    flex-direction: column;
    gap: 4px;
`;

export const FilterGroupTitle = styled.h3`
    font-size: ${Theme.fontSize.sm};
    font-weight: ${Theme.fontWeight.semiBold};
    text-transform: capitalize;
`;

export const CheckboxLabel = styled.label`
    display: block;
    font-size: ${Theme.fontSize.xxsm};
    margin-left: 8px;
    display: flex;
    gap: 8px;
`;
