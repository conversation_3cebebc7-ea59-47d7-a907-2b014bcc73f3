// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`StyledFilter Snapshots renders CheckboxLabel correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  display: block;
  font-size: 12px;
  line-height: 16px;
  margin-left: 8px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 8px;
}

<label
    class="emotion-0"
  >
    Checkbox Label
  </label>
</DocumentFragment>
`;

exports[`StyledFilter Snapshots renders FilterButton correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  padding: 6px 12px;
  background-color: #FFFFFF;
  border: 1px solid #E0E3E8;
  border-radius: 8px;
  cursor: pointer;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 8px;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  font-size: 13px;
  line-height: 18px;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
  height: 36px;
}

.emotion-0:hover {
  background-color: #E0E3E8;
}

<button
    class="emotion-0"
  >
    Filter
  </button>
</DocumentFragment>
`;

exports[`StyledFilter Snapshots renders FilterContainer correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`StyledFilter Snapshots renders FilterGroupContainer correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: 4px;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`StyledFilter Snapshots renders FilterGroupTitle correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-size: 14px;
  line-height: 20px;
  font-weight: 600;
  text-transform: capitalize;
}

<h3
    class="emotion-0"
  >
    Group Title
  </h3>
</DocumentFragment>
`;

exports[`StyledFilter Snapshots renders FilterMenu correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: absolute;
  top: 2.5rem;
  right: 0;
  background-color: #FFFFFF;
  border: 1px solid #E0E3E8;
  border-radius: 8px;
  padding: 12px;
  z-index: 100;
  min-width: 200px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: 8px;
  max-height: 30rem;
  overflow-y: auto;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;
