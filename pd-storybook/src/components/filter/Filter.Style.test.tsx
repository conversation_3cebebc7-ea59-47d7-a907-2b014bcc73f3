import { render } from '@testing-library/react';

import {
  CheckboxLabel,
  FilterButton,
  FilterContainer,
  FilterGroupContainer,
  FilterGroupTitle,
  FilterMenu,
} from './Filter.Style';

describe('StyledFilter Snapshots', () => {
  it('renders FilterContainer correctly', () => {
    const { asFragment } = render(<FilterContainer />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders FilterButton correctly', () => {
    const { asFragment } = render(<FilterButton>Filter</FilterButton>);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders FilterMenu correctly', () => {
    const { asFragment } = render(<FilterMenu />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders FilterGroupContainer correctly', () => {
    const { asFragment } = render(<FilterGroupContainer />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders FilterGroupTitle correctly', () => {
    const { asFragment } = render(<FilterGroupTitle>Group Title</FilterGroupTitle>);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders CheckboxLabel correctly', () => {
    const { asFragment } = render(<CheckboxLabel>Checkbox Label</CheckboxLabel>);
    expect(asFragment()).toMatchSnapshot();
  });
});
