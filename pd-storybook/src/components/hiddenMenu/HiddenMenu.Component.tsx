import type React from 'react';

interface HiddenMenuProps {
  isOpen: boolean;
  children: React.ReactNode;
  className?: string;
}

export function HiddenMenu({
  isOpen,
  children,
  className = '',
}: HiddenMenuProps) {
  return (
    <div
      // eslint-disable-next-line max-len
      className={`pd-fixed pd-top-0 pd-right-0 pd-h-[100dvh] pd-z-40 pd-transition-all pd-duration-300 pd-ease-in-out pd-w-64 pd-bg-white ${isOpen ? 'pd-translate-x-0' : 'pd-translate-x-full'} ${className}`}
    >
      {children}
    </div>
  );
}
