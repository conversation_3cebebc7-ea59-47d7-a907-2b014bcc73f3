import styled from '@emotion/styled';

import theme from '../../configurations/Theme.Configuration';

export type PopoverPosition = 'top' | 'right' | 'bottom' | 'left';

export interface StyledPopoverProps extends React.HtmlHTMLAttributes<HTMLDivElement> {
  position?: PopoverPosition;
  bgColor?: string;
  fontColor?: string;
  width?: string;
  maxWidth?: string;
  maxHeight?: string;
  zIndex?: number;
  isVisible?: boolean;
  hasScroll?: boolean;
}

export const PopoverContainer = styled.div`
  position: relative;
  display: inline-block;
`;

export const PopoverTrigger = styled.div`
  cursor: pointer;
`;

export const PopoverContent = styled.div<StyledPopoverProps>`
  position: absolute;
  visibility: ${(props) => (props.isVisible ? 'visible' : 'hidden')};
  opacity: ${(props) => (props.isVisible ? 1 : 0)};
  z-index: ${(props) => props.zIndex || 10};
  width: ${(props) => props.width || 'max-content'};
  max-width: ${(props) => props.maxWidth || 'none'};
  max-height: ${(props) => props.maxHeight || 'none'};
  padding: 12px;
  border-radius: ${theme.borderRadius.DEFAULT};
  background-color: ${(props) => props.bgColor || theme.colors.white};
  color: ${(props) => props.fontColor || theme.colors.dark[700]};
  box-shadow: ${theme.shadow.lg};
  transition: all 0.3s ease;
  border: 1px solid ${theme.colors.line};
  overflow-y: ${(props) => (props.hasScroll ? 'auto' : 'visible')};
  overflow-x: hidden;
  word-wrap: break-word;
  word-break: break-word;

  &.visible {
    visibility: visible;
    opacity: 1;
  }

  ${(props) => {
    switch (props.position) {
      case 'right':
        return `
          left: 100%;
          top: 50%;
          margin-left: 8px;
        `;
      case 'bottom':
        return `
          top: 100%;
          margin-top: 8px;
        `;
      case 'left':
        return `
          right: 100%;
          top: 50%;
          margin-right: 8px;
        `;
      case 'top':
      default:
        return `
          bottom: 100%;
          margin-bottom: 8px;
        `;
    }
  }}
`;
