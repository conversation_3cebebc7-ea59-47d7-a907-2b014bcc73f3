import { render } from '@testing-library/react';

import { PopoverContainer, PopoverContent, PopoverTrigger } from './Popover.Style';

describe('Popover Style Snapshots', () => {
  it('renders PopoverContainer correctly', () => {
    const { asFragment } = render(<PopoverContainer>Popover Container</PopoverContainer>);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders PopoverTrigger correctly', () => {
    const { asFragment } = render(<PopoverTrigger>Trigger Content</PopoverTrigger>);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders PopoverContent with default props correctly', () => {
    const { asFragment } = render(<PopoverContent>Default Content</PopoverContent>);
    expect(asFragment()).toMatchSnapshot();
  });

  describe('PopoverContent positions', () => {
    const positions = ['top', 'right', 'bottom', 'left'] as const;

    positions.forEach((position) => {
      it(`renders PopoverContent with ${position} position correctly`, () => {
        const { asFragment } = render(
          <PopoverContent position={position}>
            {position} positioned content
          </PopoverContent>,
        );
        expect(asFragment()).toMatchSnapshot();
      });
    });
  });

  it('renders PopoverContent with custom colors correctly', () => {
    const { asFragment } = render(
      <PopoverContent bgColor="#FF5733" fontColor="#FFFFFF">
        Custom colored content
      </PopoverContent>,
    );
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders PopoverContent with custom width correctly', () => {
    const { asFragment } = render(
      <PopoverContent width="300px">
        Custom width content
      </PopoverContent>,
    );
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders PopoverContent with custom maxHeight correctly', () => {
    const { asFragment } = render(
      <PopoverContent maxHeight="200px">
        Custom max height content
      </PopoverContent>,
    );
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders PopoverContent with custom zIndex correctly', () => {
    const { asFragment } = render(
      <PopoverContent zIndex={100}>
        Custom z-index content
      </PopoverContent>,
    );
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders PopoverContent when visible correctly', () => {
    const { asFragment } = render(
      <PopoverContent isVisible={true}>
        Visible content
      </PopoverContent>,
    );
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders PopoverContent when not visible correctly', () => {
    const { asFragment } = render(
      <PopoverContent isVisible={false}>
        Not visible content
      </PopoverContent>,
    );
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders PopoverContent with scroll enabled correctly', () => {
    const { asFragment } = render(
      <PopoverContent hasScroll={true}>
        Content with scroll
      </PopoverContent>,
    );
    expect(asFragment()).toMatchSnapshot();
  });
});
