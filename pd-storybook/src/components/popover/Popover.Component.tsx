import React, { ReactNode, useState } from 'react';

import OutsideClick from '../../utils/OutsideClick';

import {
  PopoverContainer, PopoverContent, PopoverTrigger, StyledPopoverProps,
} from './Popover.Style';

export type CustomPopoverContent = {
  title: string;
  description: string;
  items: string[];
};

export interface PopoverProps extends Omit<StyledPopoverProps, 'content'> {
  content?: ReactNode | CustomPopoverContent;
  children?: ReactNode;
  className?: string;
  isOpen?: boolean;
  onOpenChange?: (isOpen: boolean) => void;
  closeOnOutsideClick?: boolean;
  renderer?: (content: ReactNode | CustomPopoverContent) => ReactNode;
  triggerMode?: 'click' | 'hover';
  maxHeight?: string;
  hasScroll?: boolean;
}

export const Popover: React.FC<PopoverProps> = ({
  content,
  children,
  className = '',
  isOpen: controlledIsOpen,
  onOpenChange,
  closeOnOutsideClick = true,
  renderer,
  triggerMode = 'click',
  ...props
}) => {
  const [uncontrolledIsOpen, setUncontrolledIsOpen] = useState<boolean>(false);

  const isControlled = controlledIsOpen !== undefined;
  const isOpen = isControlled ? controlledIsOpen : uncontrolledIsOpen;

  const handleToggle = () => {
    if (isControlled) {
      onOpenChange?.(!isOpen);
    } else {
      setUncontrolledIsOpen(!isOpen);
    }
  };

  const handleClose = () => {
    if (isControlled) {
      onOpenChange?.(false);
    } else {
      setUncontrolledIsOpen(false);
    }
  };

  const handleOutsideClick = () => {
    if (closeOnOutsideClick) {
      handleClose();
    }
  };

  const triggerProps = triggerMode === 'hover'
    ? {
      onMouseEnter: () => {
        if (isControlled) {
          onOpenChange?.(true);
        } else {
          setUncontrolledIsOpen(true);
        }
      },
      onMouseLeave: () => {
        if (isControlled) {
          onOpenChange?.(false);
        } else {
          setUncontrolledIsOpen(false);
        }
      },
    }
    : {
      onClick: handleToggle,
    };

  const renderContent = () => {
    if (renderer) {
      return renderer(content);
    }

    if (content && typeof content === 'object' && 'title' in content && 'description' in content && 'items' in content) {
      const typedContent = content as CustomPopoverContent;
      return (
        <div>
          <div>{typedContent.title}</div>
          <p>{typedContent.description}</p>
          <ul>
            {typedContent.items.map((item, index) => (
              <li key={index}>{item}</li>
            ))}
          </ul>
        </div>
      );
    }
    return content;
  };

  return (
    <OutsideClick onOutsideClick={handleOutsideClick}>
      <PopoverContainer className={className}>
        <PopoverTrigger {...triggerProps} data-testid="popover-trigger">
          {children}
        </PopoverTrigger>
        <PopoverContent
          className={`${isOpen ? 'visible' : ''}`}
          position={props.position || 'bottom'}
          data-testid="popover-content"
          isVisible={isOpen}
          {...props}
        >
          {renderContent()}
        </PopoverContent>
      </PopoverContainer>
    </OutsideClick>
  );
};
