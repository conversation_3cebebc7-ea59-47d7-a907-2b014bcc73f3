// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Popover Style Snapshots PopoverContent positions renders PopoverContent with bottom position correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: absolute;
  visibility: hidden;
  opacity: 0;
  z-index: 10;
  width: -webkit-max-content;
  width: -moz-max-content;
  width: max-content;
  max-width: none;
  max-height: none;
  padding: 12px;
  border-radius: 8px;
  background-color: #FFFFFF;
  color: #1D1F24;
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1),0 4px 6px -4px rgb(0 0 0 / 0.1);
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
  border: 1px solid #E0E3E8;
  overflow-y: visible;
  overflow-x: hidden;
  word-wrap: break-word;
  word-break: break-word;
  top: 100%;
  margin-top: 8px;
}

.emotion-0.visible {
  visibility: visible;
  opacity: 1;
}

<div
    class="emotion-0"
  >
    bottom positioned content
  </div>
</DocumentFragment>
`;

exports[`Popover Style Snapshots PopoverContent positions renders PopoverContent with left position correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: absolute;
  visibility: hidden;
  opacity: 0;
  z-index: 10;
  width: -webkit-max-content;
  width: -moz-max-content;
  width: max-content;
  max-width: none;
  max-height: none;
  padding: 12px;
  border-radius: 8px;
  background-color: #FFFFFF;
  color: #1D1F24;
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1),0 4px 6px -4px rgb(0 0 0 / 0.1);
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
  border: 1px solid #E0E3E8;
  overflow-y: visible;
  overflow-x: hidden;
  word-wrap: break-word;
  word-break: break-word;
  right: 100%;
  top: 50%;
  margin-right: 8px;
}

.emotion-0.visible {
  visibility: visible;
  opacity: 1;
}

<div
    class="emotion-0"
  >
    left positioned content
  </div>
</DocumentFragment>
`;

exports[`Popover Style Snapshots PopoverContent positions renders PopoverContent with right position correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: absolute;
  visibility: hidden;
  opacity: 0;
  z-index: 10;
  width: -webkit-max-content;
  width: -moz-max-content;
  width: max-content;
  max-width: none;
  max-height: none;
  padding: 12px;
  border-radius: 8px;
  background-color: #FFFFFF;
  color: #1D1F24;
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1),0 4px 6px -4px rgb(0 0 0 / 0.1);
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
  border: 1px solid #E0E3E8;
  overflow-y: visible;
  overflow-x: hidden;
  word-wrap: break-word;
  word-break: break-word;
  left: 100%;
  top: 50%;
  margin-left: 8px;
}

.emotion-0.visible {
  visibility: visible;
  opacity: 1;
}

<div
    class="emotion-0"
  >
    right positioned content
  </div>
</DocumentFragment>
`;

exports[`Popover Style Snapshots PopoverContent positions renders PopoverContent with top position correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: absolute;
  visibility: hidden;
  opacity: 0;
  z-index: 10;
  width: -webkit-max-content;
  width: -moz-max-content;
  width: max-content;
  max-width: none;
  max-height: none;
  padding: 12px;
  border-radius: 8px;
  background-color: #FFFFFF;
  color: #1D1F24;
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1),0 4px 6px -4px rgb(0 0 0 / 0.1);
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
  border: 1px solid #E0E3E8;
  overflow-y: visible;
  overflow-x: hidden;
  word-wrap: break-word;
  word-break: break-word;
  bottom: 100%;
  margin-bottom: 8px;
}

.emotion-0.visible {
  visibility: visible;
  opacity: 1;
}

<div
    class="emotion-0"
  >
    top positioned content
  </div>
</DocumentFragment>
`;

exports[`Popover Style Snapshots renders PopoverContainer correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
  display: inline-block;
}

<div
    class="emotion-0"
  >
    Popover Container
  </div>
</DocumentFragment>
`;

exports[`Popover Style Snapshots renders PopoverContent when not visible correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: absolute;
  visibility: hidden;
  opacity: 0;
  z-index: 10;
  width: -webkit-max-content;
  width: -moz-max-content;
  width: max-content;
  max-width: none;
  max-height: none;
  padding: 12px;
  border-radius: 8px;
  background-color: #FFFFFF;
  color: #1D1F24;
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1),0 4px 6px -4px rgb(0 0 0 / 0.1);
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
  border: 1px solid #E0E3E8;
  overflow-y: visible;
  overflow-x: hidden;
  word-wrap: break-word;
  word-break: break-word;
  bottom: 100%;
  margin-bottom: 8px;
}

.emotion-0.visible {
  visibility: visible;
  opacity: 1;
}

<div
    class="emotion-0"
  >
    Not visible content
  </div>
</DocumentFragment>
`;

exports[`Popover Style Snapshots renders PopoverContent when visible correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: absolute;
  visibility: visible;
  opacity: 1;
  z-index: 10;
  width: -webkit-max-content;
  width: -moz-max-content;
  width: max-content;
  max-width: none;
  max-height: none;
  padding: 12px;
  border-radius: 8px;
  background-color: #FFFFFF;
  color: #1D1F24;
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1),0 4px 6px -4px rgb(0 0 0 / 0.1);
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
  border: 1px solid #E0E3E8;
  overflow-y: visible;
  overflow-x: hidden;
  word-wrap: break-word;
  word-break: break-word;
  bottom: 100%;
  margin-bottom: 8px;
}

.emotion-0.visible {
  visibility: visible;
  opacity: 1;
}

<div
    class="emotion-0"
  >
    Visible content
  </div>
</DocumentFragment>
`;

exports[`Popover Style Snapshots renders PopoverContent with custom colors correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: absolute;
  visibility: hidden;
  opacity: 0;
  z-index: 10;
  width: -webkit-max-content;
  width: -moz-max-content;
  width: max-content;
  max-width: none;
  max-height: none;
  padding: 12px;
  border-radius: 8px;
  background-color: #FF5733;
  color: #FFFFFF;
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1),0 4px 6px -4px rgb(0 0 0 / 0.1);
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
  border: 1px solid #E0E3E8;
  overflow-y: visible;
  overflow-x: hidden;
  word-wrap: break-word;
  word-break: break-word;
  bottom: 100%;
  margin-bottom: 8px;
}

.emotion-0.visible {
  visibility: visible;
  opacity: 1;
}

<div
    class="emotion-0"
  >
    Custom colored content
  </div>
</DocumentFragment>
`;

exports[`Popover Style Snapshots renders PopoverContent with custom maxHeight correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: absolute;
  visibility: hidden;
  opacity: 0;
  z-index: 10;
  width: -webkit-max-content;
  width: -moz-max-content;
  width: max-content;
  max-width: none;
  max-height: 200px;
  padding: 12px;
  border-radius: 8px;
  background-color: #FFFFFF;
  color: #1D1F24;
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1),0 4px 6px -4px rgb(0 0 0 / 0.1);
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
  border: 1px solid #E0E3E8;
  overflow-y: visible;
  overflow-x: hidden;
  word-wrap: break-word;
  word-break: break-word;
  bottom: 100%;
  margin-bottom: 8px;
}

.emotion-0.visible {
  visibility: visible;
  opacity: 1;
}

<div
    class="emotion-0"
  >
    Custom max height content
  </div>
</DocumentFragment>
`;

exports[`Popover Style Snapshots renders PopoverContent with custom width correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: absolute;
  visibility: hidden;
  opacity: 0;
  z-index: 10;
  width: 300px;
  max-width: none;
  max-height: none;
  padding: 12px;
  border-radius: 8px;
  background-color: #FFFFFF;
  color: #1D1F24;
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1),0 4px 6px -4px rgb(0 0 0 / 0.1);
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
  border: 1px solid #E0E3E8;
  overflow-y: visible;
  overflow-x: hidden;
  word-wrap: break-word;
  word-break: break-word;
  bottom: 100%;
  margin-bottom: 8px;
}

.emotion-0.visible {
  visibility: visible;
  opacity: 1;
}

<div
    class="emotion-0"
    width="300px"
  >
    Custom width content
  </div>
</DocumentFragment>
`;

exports[`Popover Style Snapshots renders PopoverContent with custom zIndex correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: absolute;
  visibility: hidden;
  opacity: 0;
  z-index: 100;
  width: -webkit-max-content;
  width: -moz-max-content;
  width: max-content;
  max-width: none;
  max-height: none;
  padding: 12px;
  border-radius: 8px;
  background-color: #FFFFFF;
  color: #1D1F24;
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1),0 4px 6px -4px rgb(0 0 0 / 0.1);
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
  border: 1px solid #E0E3E8;
  overflow-y: visible;
  overflow-x: hidden;
  word-wrap: break-word;
  word-break: break-word;
  bottom: 100%;
  margin-bottom: 8px;
}

.emotion-0.visible {
  visibility: visible;
  opacity: 1;
}

<div
    class="emotion-0"
  >
    Custom z-index content
  </div>
</DocumentFragment>
`;

exports[`Popover Style Snapshots renders PopoverContent with default props correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: absolute;
  visibility: hidden;
  opacity: 0;
  z-index: 10;
  width: -webkit-max-content;
  width: -moz-max-content;
  width: max-content;
  max-width: none;
  max-height: none;
  padding: 12px;
  border-radius: 8px;
  background-color: #FFFFFF;
  color: #1D1F24;
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1),0 4px 6px -4px rgb(0 0 0 / 0.1);
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
  border: 1px solid #E0E3E8;
  overflow-y: visible;
  overflow-x: hidden;
  word-wrap: break-word;
  word-break: break-word;
  bottom: 100%;
  margin-bottom: 8px;
}

.emotion-0.visible {
  visibility: visible;
  opacity: 1;
}

<div
    class="emotion-0"
  >
    Default Content
  </div>
</DocumentFragment>
`;

exports[`Popover Style Snapshots renders PopoverContent with scroll enabled correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: absolute;
  visibility: hidden;
  opacity: 0;
  z-index: 10;
  width: -webkit-max-content;
  width: -moz-max-content;
  width: max-content;
  max-width: none;
  max-height: none;
  padding: 12px;
  border-radius: 8px;
  background-color: #FFFFFF;
  color: #1D1F24;
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1),0 4px 6px -4px rgb(0 0 0 / 0.1);
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
  border: 1px solid #E0E3E8;
  overflow-y: auto;
  overflow-x: hidden;
  word-wrap: break-word;
  word-break: break-word;
  bottom: 100%;
  margin-bottom: 8px;
}

.emotion-0.visible {
  visibility: visible;
  opacity: 1;
}

<div
    class="emotion-0"
  >
    Content with scroll
  </div>
</DocumentFragment>
`;

exports[`Popover Style Snapshots renders PopoverTrigger correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  cursor: pointer;
}

<div
    class="emotion-0"
  >
    Trigger Content
  </div>
</DocumentFragment>
`;
