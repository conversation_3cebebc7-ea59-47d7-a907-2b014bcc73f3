import { fireEvent, render, screen } from '@testing-library/react';

import { Popover } from './Popover.Component';

jest.mock('../../utils/OutsideClick', () => ({
  __esModule: true,
  default: ({ children, onOutsideClick }: { children: React.ReactNode, onOutsideClick: () => void }) => (
    <div data-testid="outside-click" onClick={onOutsideClick}>
      {children}
    </div>
  ),
}));

describe('Popover Component', () => {
  const defaultProps = {
    content: <div>Popover content</div>,
    children: <button>Click me</button>,
  };

  it('renders correctly with default props', () => {
    render(<Popover {...defaultProps} />);
    expect(screen.getByText('Click me')).toBeInTheDocument();
    expect(screen.getByText('Popover content')).toBeInTheDocument();
    expect(screen.getByTestId('popover-content')).not.toHaveClass('visible');
  });

  test('hover mode (uncontrolled): abre/cierra con eventos de mouse', () => {
    const { getByTestId } = render(
      <Popover triggerMode="hover" content="Popover Content">
        <button>Hover me</button>
      </Popover>,
    );

    const trigger = getByTestId('popover-trigger');
    const content = getByTestId('popover-content');

    fireEvent.mouseEnter(trigger);
    expect(content).toHaveClass('visible');
    fireEvent.mouseLeave(trigger);
    expect(content).not.toHaveClass('visible');
  });

  test('hover mode (controlled): llama a onOpenChange con true/false', () => {
    const onOpenChange = jest.fn();
    const { getByTestId } = render(
      <Popover
        triggerMode="hover"
        isOpen={false}
        onOpenChange={onOpenChange}
        content="Popover Content"
      >
        <button>Hover me</button>
      </Popover>,
    );

    const trigger = getByTestId('popover-trigger');

    fireEvent.mouseEnter(trigger);
    expect(onOpenChange).toHaveBeenCalledWith(true);
    fireEvent.mouseLeave(trigger);
    expect(onOpenChange).toHaveBeenCalledWith(false);
  });

  test('click mode (controlled): alterna onOpenChange', () => {
    const onOpenChange = jest.fn();
    const { getByTestId } = render(
      <Popover
        triggerMode="click"
        isOpen={false}
        onOpenChange={onOpenChange}
        content="Popover Content"
      >
        <button>Click me</button>
      </Popover>,
    );

    const trigger = getByTestId('popover-trigger');

    fireEvent.click(trigger);
    expect(onOpenChange).toHaveBeenCalledWith(true);

    onOpenChange.mockClear();
    fireEvent.click(trigger);
    expect(onOpenChange).toHaveBeenCalledWith(false);
  });

  it('uses custom renderer when provided', () => {
    const customRenderer = jest.fn((content) => <div data-testid="custom-content">Custom {content}</div>);
    render(
      <Popover
        {...defaultProps}
        content="Test Content"
        renderer={customRenderer}
      />,
    );

    fireEvent.click(screen.getByText('Click me'));
    expect(customRenderer).toHaveBeenCalledWith('Test Content');
    expect(screen.getByTestId('custom-content')).toBeInTheDocument();
    expect(screen.getByText('Custom Test Content')).toBeInTheDocument();
  });

  it('works in controlled mode', () => {
    const onOpenChange = jest.fn();
    render(
      <Popover
        {...defaultProps}
        isOpen={false}
        onOpenChange={onOpenChange}
      />,
    );

    fireEvent.click(screen.getByText('Click me'));
    expect(onOpenChange).toHaveBeenCalledWith(true);
  });

  it('works with hover trigger mode', () => {
    render(<Popover {...defaultProps} triggerMode="hover" />);

    fireEvent.mouseEnter(screen.getByTestId('popover-trigger'));
    expect(screen.getByTestId('popover-content')).toHaveClass('visible');

    fireEvent.mouseLeave(screen.getByTestId('popover-trigger'));
    expect(screen.getByTestId('popover-content')).not.toHaveClass('visible');
  });

  test('modo controlado sin onOpenChange: no cambia isOpen al hacer clic', () => {
    const { getByTestId } = render(
      <Popover isOpen={false} triggerMode="click" content="Content">
        <button>Trigger</button>
      </Popover>,
    );

    const trigger = getByTestId('popover-trigger');
    const content = getByTestId('popover-content');

    fireEvent.click(trigger);
    expect(content).not.toHaveClass('visible');
  });

  test('renderer indefinido: renderiza el contenido directamente', () => {
    const { getByText } = render(
      <Popover content="Contenido de prueba">
        <button>Trigger</button>
      </Popover>,
    );

    fireEvent.click(getByText('Trigger'));
    expect(getByText('Contenido de prueba')).toBeInTheDocument();
  });

  test('click fuera con onOpenChange indefinido: no cierra el popover en modo controlado', () => {
    const { getByTestId } = render(
      <Popover isOpen={true} closeOnOutsideClick content="Content">
        <button>Trigger</button>
      </Popover>,
    );

    const content = getByTestId('popover-content');
    expect(content).toHaveClass('visible');

    fireEvent.click(document.body);
    expect(content).toHaveClass('visible');
  });

  test('hover sin onOpenChange (modo no controlado): abre/cierra con eventos de mouse', () => {
    const { getByTestId } = render(
      <Popover triggerMode="hover" content="Content">
        <button>Hover me</button>
      </Popover>,
    );

    const trigger = getByTestId('popover-trigger');
    const content = getByTestId('popover-content');

    fireEvent.mouseEnter(trigger);
    expect(content).toHaveClass('visible');

    fireEvent.mouseLeave(trigger);
    expect(content).not.toHaveClass('visible');
  });

  test('renders CustomPopoverContent correctly without renderer', () => {
    const customContent = {
      title: 'Test Title',
      description: 'Test Description',
      items: ['Item 1', 'Item 2'],
    };

    render(
      <Popover content={customContent}>
        <button>Trigger</button>
      </Popover>,
    );

    fireEvent.click(screen.getByText('Trigger'));
    expect(screen.getByText('Test Title')).toBeInTheDocument();
    expect(screen.getByText('Test Description')).toBeInTheDocument();
    expect(screen.getByText('Item 1')).toBeInTheDocument();
    expect(screen.getByText('Item 2')).toBeInTheDocument();
  });

  test('renders CustomPopoverContent with custom renderer', () => {
    const customContent = {
      title: 'Custom Title',
      description: 'Custom Description',
      items: ['Custom Item 1', 'Custom Item 2'],
    };

    const customRenderer = jest.fn((content) => {
      const typedContent = content as typeof customContent;
      return <div data-testid="custom-rendered">Rendered: {typedContent.title}</div>;
    });

    render(
      <Popover
        content={customContent}
        renderer={customRenderer}
      >
        <button>Custom Trigger</button>
      </Popover>,
    );

    fireEvent.click(screen.getByText('Custom Trigger'));
    expect(customRenderer).toHaveBeenCalledWith(customContent);
    expect(screen.getByTestId('custom-rendered')).toBeInTheDocument();
    expect(screen.getByText('Rendered: Custom Title')).toBeInTheDocument();
  });
});
