import { render } from '@testing-library/react';

import { StyledForm, StyledFormProps } from './Form.Style';

describe('StyledForm Snapshots', () => {
  const defaultProps: StyledFormProps = {
    orientation: 'vertical',
    columnsNumber: 3,
  };

  it('renders correctly with default props', () => {
    const { asFragment } = render(<StyledForm {...defaultProps} />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders correctly with horizontal orientation', () => {
    const { asFragment } = render(<StyledForm {...defaultProps} orientation="horizontal" />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders correctly with custom columns number', () => {
    const { asFragment } = render(<StyledForm {...defaultProps} columnsNumber={4} />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders correctly with custom className', () => {
    const { asFragment } = render(<StyledForm {...defaultProps} className="custom-class" />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders correctly with additional props', () => {
    const { asFragment } = render(<StyledForm {...defaultProps} data-testid="styled-form" />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders correctly without orientation prop', () => {
    const { asFragment } = render(<StyledForm columnsNumber={3} />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders correctly without columnsNumber prop', () => {
    const { asFragment } = render(<StyledForm orientation="vertical" />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders correctly without optional props', () => {
    const { asFragment } = render(<StyledForm />);
    expect(asFragment()).toMatchSnapshot();
  });
});
