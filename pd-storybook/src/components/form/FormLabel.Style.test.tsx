import { render } from '@testing-library/react';

import { StyledFormLabel, StyledFormLabelHeader } from './FormLabel.Style';

describe('StyledFormLabel Snapshots', () => {
  it('renders StyledFormLabelHeader correctly', () => {
    const { asFragment } = render(<StyledFormLabelHeader />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders StyledFormLabel correctly with default styles', () => {
    const { asFragment } = render(<StyledFormLabel />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders StyledFormLabel correctly with custom children', () => {
    const { asFragment } = render(<StyledFormLabel>Custom Label</StyledFormLabel>);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders StyledFormLabel correctly with additional props', () => {
    const { asFragment } = render(<StyledFormLabel data-testid="styled-form-label" />);
    expect(asFragment()).toMatchSnapshot();
  });
});
