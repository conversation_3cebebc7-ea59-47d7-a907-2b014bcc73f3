// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`StyledFormLabel Snapshots renders StyledFormLabel correctly with additional props 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-size: 12px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 0.25rem;
  color: #6B6E75;
}

<h2
    class="emotion-0"
    data-testid="styled-form-label"
  />
</DocumentFragment>
`;

exports[`StyledFormLabel Snapshots renders StyledFormLabel correctly with custom children 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-size: 12px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 0.25rem;
  color: #6B6E75;
}

<h2
    class="emotion-0"
  >
    Custom Label
  </h2>
</DocumentFragment>
`;

exports[`StyledFormLabel Snapshots renders StyledFormLabel correctly with default styles 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-size: 12px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 0.25rem;
  color: #6B6E75;
}

<h2
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`StyledFormLabel Snapshots renders StyledFormLabelHeader correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;
