// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`StyledForm Snapshots renders correctly with additional props 1`] = `
<DocumentFragment>
  .emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  min-width: 200px;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  row-gap: 1rem;
  grid-template-columns: repeat(3, 1fr);
}

<form
    class="emotion-0"
    data-testid="styled-form"
    orientation="vertical"
  />
</DocumentFragment>
`;

exports[`StyledForm Snapshots renders correctly with custom className 1`] = `
<DocumentFragment>
  .emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  min-width: 200px;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  row-gap: 1rem;
  grid-template-columns: repeat(3, 1fr);
}

<form
    class="custom-class emotion-0"
    orientation="vertical"
  />
</DocumentFragment>
`;

exports[`StyledForm Snapshots renders correctly with custom columns number 1`] = `
<DocumentFragment>
  .emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  min-width: 200px;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  row-gap: 1rem;
  grid-template-columns: repeat(4, 1fr);
}

<form
    class="emotion-0"
    orientation="vertical"
  />
</DocumentFragment>
`;

exports[`StyledForm Snapshots renders correctly with default props 1`] = `
<DocumentFragment>
  .emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  min-width: 200px;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  row-gap: 1rem;
  grid-template-columns: repeat(3, 1fr);
}

<form
    class="emotion-0"
    orientation="vertical"
  />
</DocumentFragment>
`;

exports[`StyledForm Snapshots renders correctly with horizontal orientation 1`] = `
<DocumentFragment>
  .emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  min-width: 200px;
  display: grid;
  gap: 1rem;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: start;
  -webkit-box-align: start;
  -ms-flex-align: start;
  align-items: start;
  grid-template-columns: repeat(3, 1fr);
}

<form
    class="emotion-0"
    orientation="horizontal"
  />
</DocumentFragment>
`;

exports[`StyledForm Snapshots renders correctly without columnsNumber prop 1`] = `
<DocumentFragment>
  .emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  min-width: 200px;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  row-gap: 1rem;
  grid-template-columns: repeat(3, 1fr);
}

<form
    class="emotion-0"
    orientation="vertical"
  />
</DocumentFragment>
`;

exports[`StyledForm Snapshots renders correctly without optional props 1`] = `
<DocumentFragment>
  .emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  min-width: 200px;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  row-gap: 1rem;
}

<form
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`StyledForm Snapshots renders correctly without orientation prop 1`] = `
<DocumentFragment>
  .emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  min-width: 200px;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  row-gap: 1rem;
}

<form
    class="emotion-0"
  />
</DocumentFragment>
`;
