import Theme from '../../configurations/Theme.Configuration';
import { IconImporter, IconImporterProps } from '../iconImporter/IconImporter.Component';
import { Tooltip, TooltipProps } from '../tooltip/Tooltip.Component';

import { StyledFormLabel, StyledFormLabelHeader } from './FormLabel.Style';

export interface FormLabelProps {
  title: string;
  tooltip?: TooltipProps;
  icon?: IconImporterProps;
}

export const FormLabel = (
  { title, tooltip, icon }: FormLabelProps,
) => {
  const tooltipComponent = tooltip && (
    <Tooltip
      data-testid="tooltip"
      content={tooltip?.content}
      {...tooltip}
    >
      <IconImporter
        name={icon?.name || 'warning'}
        color={Theme.colors.dark[500]}
        size={18}
        {...icon}
      />
    </Tooltip>
  );

  return (
    <StyledFormLabelHeader>
      <div className="pd-flex pd-gap-3">
        <StyledFormLabel>
          {title}
          {tooltipComponent}
        </StyledFormLabel>
      </div>

    </StyledFormLabelHeader>
  );
};
