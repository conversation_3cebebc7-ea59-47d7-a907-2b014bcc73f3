import React, {
  Children, cloneElement, isValidElement,
  ReactElement,
  ReactNode,
  useEffect,
  useRef,
  useState,
} from 'react';

type childWithDisplayName = ReactElement & {type: {displayName: string, name: string}};

export type FormHandlerState = Record<string, string | number | boolean | File | File[]>;

interface FormHandlerProps {
  children: childWithDisplayName;
  onSubmit?: (formData: FormHandlerState) => void;
  onChange?: (formData: FormHandlerState) => void;
  keepState?: boolean;
  initialState?: FormHandlerState;
}

type InputElementProps = {
  name: string;
  type?: string;
  value?: string | number | boolean;
  checked?: boolean;
  onChange?: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => void;
};

type InputChangeHandler = (name: string, value: string | number | boolean | File | File[], isFile?: boolean) => void;

function handleCheckboxInput(
  inputElement: ReactElement<InputElementProps>,
  formData: FormHandlerState,
  handleInputChange: InputChangeHandler,
): ReactElement {
  const isChecked = formData[inputElement.props.name] !== undefined
    ? Boolean(formData[inputElement.props.name])
    : Boolean(inputElement.props.checked);

  return cloneElement(
    inputElement,
    {
      ...inputElement.props,
      onChange: (e: React.ChangeEvent<HTMLInputElement>) => {
        if (inputElement.props.onChange) {
          inputElement.props.onChange(e);
        }
        handleInputChange(inputElement.props.name, e.target.checked);
      },
      checked: isChecked,
    } as Partial<InputElementProps>,
  );
}

function handleFileInput(
  inputElement: ReactElement<InputElementProps>,
  handleInputChange: InputChangeHandler,
): ReactElement {
  return cloneElement(
    inputElement,
    {
      ...inputElement.props,
      onChange: (e: React.ChangeEvent<HTMLInputElement>) => {
        if (inputElement.props.onChange) {
          inputElement.props.onChange(e);
        }

        if (e.target.files && e.target.files.length > 0) {
          const fileValue = e.target.multiple
            ? Array.from(e.target.files)
            : e.target.files[0];

          handleInputChange(inputElement.props.name, fileValue, true);
        }
      },
    } as Partial<InputElementProps>,
  );
}

function handleStandardInput(
  inputElement: ReactElement<InputElementProps>,
  formData: FormHandlerState,
  handleInputChange: InputChangeHandler,
): ReactElement {
  const currentValue = formData[inputElement.props.name] !== undefined
    ? formData[inputElement.props.name]
    : (inputElement.props.value || '');

  return cloneElement(
    inputElement,
    {
      ...inputElement.props,
      onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
        if (inputElement.props.onChange) {
          inputElement.props.onChange(e);
        }
        handleInputChange(inputElement.props.name, e.target.value);
      },
      value: currentValue,
    } as Partial<InputElementProps>,
  );
}

function processFormElement(
  element: childWithDisplayName,
  formData: FormHandlerState,
  handleInputChange: InputChangeHandler,
): ReactElement | null {
  if (!('name' in element.props)) {
    return element;
  }

  const inputElement = element;
  const inputType = inputElement.props.type?.toLowerCase() || inputElement.type.displayName || inputElement.type.name;

  if (inputType === 'checkbox') {
    return handleCheckboxInput(inputElement, formData, handleInputChange);
  }

  if (['file', 'FileInput'].includes(inputType)) {
    return handleFileInput(inputElement, handleInputChange);
  }

  return handleStandardInput(inputElement, formData, handleInputChange);
}

function isInputElement(child: childWithDisplayName): boolean {
  const componentName = child.type.displayName || child.type.name;

  return (
    child.type === 'input'
    || child.type === 'select'
    || child.type === 'textarea'
    || child.type === 'number'
    || (typeof child.type === 'string' && ['input', 'select', 'textarea'].includes(child.type.toLowerCase()))
    || ['InputComponent', 'InputPrice', 'FileInput', 'SelectInput', 'TextAreaInput'].includes(componentName)
    || (child.props && 'name' in child.props)
  );
}

function extractValues(children: childWithDisplayName): FormHandlerState {
  const extractedValues: FormHandlerState = {};

  const extractInitialValues = (element: ReactElement<{
    name?: string,
    value: string | number | boolean | File | File[]
    children: ReactNode[],
  }>): void => {
    if (!isValidElement(element)) return;

    if (element.props?.name) {
      const { name } = element.props;

      if ('value' in element.props && element.props.value !== undefined && element.props.value !== '') {
        extractedValues[name] = element.props.value;
      }

      if ('checked' in element.props && element.props.checked !== undefined) {
        extractedValues[name] = Boolean(element.props.checked);
      }
    }

    if (element.props && element.props.children) {
      React.Children.forEach(element.props.children, (child) => {
        if (isValidElement(child)) {
          extractInitialValues(child as unknown as ReactElement);
        }
      });
    }
  };

  extractInitialValues(children);

  return extractedValues;
}

export function FormHandlerComponent({
  children, onSubmit, onChange, initialState = {}, keepState = false,
}: FormHandlerProps) {
  const [formData, setFormData] = useState<FormHandlerState>({});
  const [files, setFiles] = useState<Record<string, File | File[]>>({});
  const initialLoadRef = useRef(false);
  const extractedValues = extractValues(children);

  useEffect(() => {
    if (initialLoadRef.current && keepState) return;

    const mergedValues = { ...extractedValues, ...initialState };

    if (Object.keys(mergedValues).length > 0) {
      setFormData(mergedValues);
    }

    initialLoadRef.current = true;
  }, [JSON.stringify(extractValues), JSON.stringify(initialState)]);

  const handleInputChange: InputChangeHandler = (name, value, isFile = false) => {
    if (isFile && (value instanceof File || (Array.isArray(value) && value.length > 0 && value[0] instanceof File))) {
      setFiles((prev) => {
        const fileValue = value as (File | File[]);
        return { ...prev, [name]: fileValue };
      });
    }

    const updatedData = { ...formData, [name]: value };
    setFormData(updatedData);

    if (onChange) {
      setTimeout(() => {
        onChange(updatedData);
      }, 0);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (onSubmit) {
      const completeFormData = { ...formData, ...files };
      onSubmit(completeFormData);
    }
  };

  const traverseChildrenElements = (childrenNodes: childWithDisplayName): ReactNode => Children.map(childrenNodes, (child) => {
    if (!isValidElement(child)) {
      return child;
    }

    if (isInputElement(child)) {
      return processFormElement(child, formData, handleInputChange);
    }

    if (child.props.children) {
      return cloneElement(child, {
        ...child.props,
        children: traverseChildrenElements(Array.isArray(child.props.children) ? child.props.children : [child.props.children]),
      });
    }

    return child;
  });

  return (
    <form onSubmit={handleSubmit} encType="multipart/form-data" className='w-full'>
      {traverseChildrenElements(children)}
    </form>
  );
}
