import { css } from '@emotion/react';
import styled from '@emotion/styled';

import Theme from '../../configurations/Theme.Configuration';

export interface StyledFormProps extends React.FormHTMLAttributes<HTMLFormElement> {
    orientation?: 'horizontal' | 'vertical';
    columnsNumber?: number;
}

export const StyledForm = styled.form<StyledFormProps>`
    display: flex;
    min-width: 200px;

    ${(props) => ((props.orientation ?? 'vertical') === 'vertical'
    ? css`
        flex-direction: column;
        row-gap: 1rem;
        `
    : css`
        display: grid;
        gap: 1rem;
        justify-content: center;
        align-items: start;
        `)
}

    ${(props) => props.orientation && css`
        grid-template-columns: repeat(${props.columnsNumber || 3}, 1fr);
        `}
    `;

export const StyledErrorMessages = styled.p`
    color: ${Theme.colors.negative};
    font-size: ${Theme.fontSize.xxsm};
`;

export const StyledInput = styled.div`
    height: 100%;
`;
