import React, { useEffect, useState } from 'react';

import {
  Button, ButtonProps,
} from '../button/Button.Component';
import { IconImporter } from '../iconImporter/IconImporter.Component';
import { IconName } from '../iconImporter/IconMap.Component';
import { StyledRequired } from '../input/Input.Style';
import { Tooltip, TooltipProps } from '../tooltip/Tooltip.Component';

import {
  StyledErrorMessages, StyledForm, StyledFormProps, StyledInput,
} from './Form.Style';

 type FormInputElement = {
  value: unknown
  name: string
}

export type InfoProps = {
  iconName?: IconName;
  tooltip: TooltipProps;
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export interface FormInput<T = any, P =any > {
  label?: string
  formInputClassName?: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  component: React.FC<any & { onChange?: (event: React.ChangeEvent<FormInputElement>) => void}>
  props?: P & {
    onChange?: (event: React.ChangeEvent<FormInputElement>) => void,
    value?: unknown,
    info?: InfoProps,
    customLabelSibling?: React.ReactNode,
    bottomChild?: React.ReactNode;
  }
  validate?: (value?: T) => string | undefined
}
export interface FormProps extends StyledFormProps {
  inputs: FormInput<unknown>[];
  btnSubmit?: ButtonProps;
  formValidation?: (error: boolean) => void;
  formClassName?: string;
  dataErrorName?: string;
}

export const FormComponent: React.FC<FormProps> = ({
  inputs,
  btnSubmit,
  formValidation,
  formClassName,
  dataErrorName,
  ...props
}) => {
  const [formState, setFormState] = useState<{[key: string]: {dirty: boolean}}>({});

  const internalOnChange = (
    event: React.ChangeEvent<FormInputElement>,
    original?: (event: React.ChangeEvent<FormInputElement>) => void,
  ) => {
    const { name } = event.target;
    setFormState({
      ...formState,
      [name]: { dirty: true },
    });

    if (original) original(event);
  };

  const [hasError, setHasError] = useState(false);

  useEffect(() => {
    setHasError(inputs.some((input) => input.validate && input.validate(input.props.value)));
  }, [formState, inputs]);

  useEffect(() => {
    if (formValidation) {
      formValidation(hasError);
    }
  }, [hasError, formValidation]);

  useEffect(() => {
    if (dataErrorName) {
      setFormState({
        ...formState,
        [dataErrorName]: { dirty: true },
      });
    }
  }, [dataErrorName]);

  return (
    <StyledForm
      className={formClassName}
      data-testid="form"
      {...props}
    >
      {inputs.map((input) => {
        const {
          props: {
            onChange,
            value,
            isRequired,
            formInputClassName,
            info,
            customLabelSibling,
            bottomChild,
            ...restProps
          },
          label,
        } = input;

        const error = formState[input.props.name]?.dirty && input.validate?.(value);

        return (
          <div key={input.props.name} className={input.formInputClassName}>
            {
              label?.length !== 0
                && <div className='pd-flex pd-justify-between pd-gap-4'>
                  <div className='pd-flex pd-gap-1 pd-items-center pd-grow'>
                    {(label && isRequired) && <StyledRequired>* </StyledRequired>}
                    <div className='pd-text-dark-500 pd-text-xxsm'>{label}</div>
                    {(label && info)
                      && <Tooltip content={info.tooltip.content} {...info.tooltip}>
                        <IconImporter name={info.iconName || 'info'} size={16} />
                      </Tooltip>}
                  </div>

                  {customLabelSibling}
                </div>
            }
            <StyledInput className={restProps.className}>
              {
                input.component({
                  onChange: (e: React.ChangeEvent<FormInputElement>) => internalOnChange(e, input.props?.onChange), error, value, ...restProps,
                })
              }
            </StyledInput>

            <StyledErrorMessages
              className='pd-text-negative pd-text-xxsm pd-mt-2'>{formState[input.props.name]?.dirty && input.validate && input.validate(value)}
            </StyledErrorMessages>

            {bottomChild}
          </div>
        );
      })}
      {btnSubmit && <Button btnType='submit' {...btnSubmit} />}
    </StyledForm>
  );
};
