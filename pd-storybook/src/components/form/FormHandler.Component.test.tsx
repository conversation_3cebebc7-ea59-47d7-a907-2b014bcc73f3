import {
  fireEvent,
  render, screen,
  waitFor,
} from '@testing-library/react';

import { FormInputType, InputComponent, InputPrice } from '../input/Input.Component';

import { FormHandlerComponent } from './FormHandler.Component';

describe('FormHandlerComponent', () => {
  test('renders form with standard input elements', () => {
    render(
      <FormHandlerComponent>
        <div>
          <InputComponent
            name="username"
            inputType={FormInputType.Text}
            placeholder="Enter username"
            data-testid="username-input"
          />
          <button type="submit" data-testid="submit-button">Submit</button>
        </div>
      </FormHandlerComponent>,
    );

    expect(screen.getByTestId('username-input')).toBeInTheDocument();
    expect(screen.getByTestId('submit-button')).toBeInTheDocument();
  });

  test('handles input changes correctly', () => {
    const onChangeMock = jest.fn();

    render(
      <FormHandlerComponent onChange={onChangeMock}>
        <div>
          <InputComponent
            name="username"
            inputType={FormInputType.Text}
            placeholder="Enter username"
            data-testid="username-input"
          />
        </div>
      </FormHandlerComponent>,
    );

    fireEvent.change(screen.getByTestId('username-input'), {
      target: { value: 'testuser' },
    });

    waitFor(() => {
      expect(onChangeMock).toHaveBeenCalledWith(expect.objectContaining({
        username: 'testuser',
      }));
    });
  });

  test('handles form submission', () => {
    const onSubmitMock = jest.fn();

    render(
      <FormHandlerComponent onSubmit={onSubmitMock}>
        <div>
          <InputComponent
            name="username"
            inputType={FormInputType.Text}
            value="testuser"
            data-testid="username-input"
          />
          <button type="submit" data-testid="submit-button">Submit</button>
        </div>
      </FormHandlerComponent>,
    );

    fireEvent.click(screen.getByTestId('submit-button'));

    expect(onSubmitMock).toHaveBeenCalledWith(
      expect.objectContaining({
        username: 'testuser',
      }),
    );
  });

  test('handles checkbox inputs', () => {
    const onChangeMock = jest.fn();

    render(
      <FormHandlerComponent onChange={onChangeMock}>
        <div>
          <input
            name="agreement"
            type="checkbox"
            data-testid="agreement-checkbox"
          />
        </div>
      </FormHandlerComponent>,
    );

    fireEvent.click(screen.getByTestId('agreement-checkbox'));

    waitFor(() => {
      expect(onChangeMock).toHaveBeenCalledWith(expect.objectContaining({
        agreement: true,
      }));
    });
  });

  test('handles nested input elements', () => {
    const onChangeMock = jest.fn();

    render(
      <FormHandlerComponent onChange={onChangeMock}>
        <div>
          <div>
            <InputComponent
              name="email"
              inputType={FormInputType.Email}
              data-testid="email-input"
            />
          </div>
        </div>
      </FormHandlerComponent>,
    );

    fireEvent.change(screen.getByTestId('email-input'), {
      target: { value: '<EMAIL>' },
    });

    waitFor(() => {
      expect(onChangeMock).toHaveBeenCalledWith(expect.objectContaining({
        email: '<EMAIL>',
      }));
    });
  });

  test('handles custom InputPrice component', () => {
    const onChangeMock = jest.fn();

    render(
      <FormHandlerComponent onChange={onChangeMock}>
        <div>
          <InputPrice
            name="price"
            onChange={() => {}}
            data-testid="price-input"
          />
        </div>
      </FormHandlerComponent>,
    );

    const priceInput = screen.getByTestId('price-input');
    expect(priceInput).toBeInTheDocument();

    fireEvent.change(priceInput, {
      target: { value: '123.45' },
    });

    waitFor(() => {
      expect(onChangeMock).toHaveBeenCalled();
    });
  });

  test('preserves initial values', () => {
    render(
      <FormHandlerComponent>
        <div>
          <InputComponent
            name="username"
            inputType={FormInputType.Text}
            value="initialValue"
            data-testid="username-input"
          />
        </div>
      </FormHandlerComponent>,
    );

    expect(screen.getByTestId('username-input')).toHaveValue('initialValue');
  });

  test('handles multiple inputs in the same form', () => {
    const onSubmitMock = jest.fn();

    render(
      <FormHandlerComponent onSubmit={onSubmitMock}>
        <div>
          <InputComponent
            name="firstName"
            inputType={FormInputType.Text}
            value="John"
            data-testid="first-name-input"
          />
          <InputComponent
            name="lastName"
            inputType={FormInputType.Text}
            value="Doe"
            data-testid="last-name-input"
          />
          <button type="submit" data-testid="submit-button">Submit</button>
        </div>
      </FormHandlerComponent>,
    );

    fireEvent.change(screen.getByTestId('first-name-input'), {
      target: { value: 'Jane' },
    });

    expect(screen.getByTestId('last-name-input')).toHaveValue('Doe');

    fireEvent.click(screen.getByTestId('submit-button'));

    expect(onSubmitMock).toHaveBeenCalledWith(
      expect.objectContaining({
        firstName: 'Jane',
        lastName: 'Doe',
      }),
    );
  });

  test('calls custom onChange handler of input as well as FormHandler', () => {
    const inputOnChange = jest.fn();
    const formOnChange = jest.fn();
    render(
      <FormHandlerComponent onChange={formOnChange}>
        <div>
          <input
            name="custom"
            type="text"
            data-testid="custom-input"
            onChange={inputOnChange}
          />
        </div>
      </FormHandlerComponent>,
    );
    fireEvent.change(screen.getByTestId('custom-input'), { target: { value: 'abc' } });
    expect(inputOnChange).toHaveBeenCalled();
    return waitFor(() => {
      expect(formOnChange).toHaveBeenCalledWith(expect.objectContaining({ custom: 'abc' }));
    });
  });

  test('handles file input (single file)', () => {
    const onChangeMock = jest.fn();
    render(
      <FormHandlerComponent onChange={onChangeMock}>
        <div>
          <input
            name="file"
            type="file"
            data-testid="file-input"
          />
        </div>
      </FormHandlerComponent>,
    );
    const file = new File(['hello'], 'hello.png', { type: 'image/png' });
    fireEvent.change(screen.getByTestId('file-input'), {
      target: { files: [file], multiple: false },
    });
    return waitFor(() => {
      expect(onChangeMock).toHaveBeenCalledWith(expect.objectContaining({ file }));
    });
  });

  test('handles file input (multiple files)', () => {
    const onChangeMock = jest.fn();
    render(
      <FormHandlerComponent onChange={onChangeMock}>
        <div>
          <input
            name="files"
            type="file"
            multiple
            data-testid="multi-file-input"
          />
        </div>
      </FormHandlerComponent>,
    );
    const file1 = new File(['a'], 'a.txt', { type: 'text/plain' });
    const file2 = new File(['b'], 'b.txt', { type: 'text/plain' });
    fireEvent.change(screen.getByTestId('multi-file-input'), {
      target: { files: [file1, file2], multiple: true },
    });
    return waitFor(() => {
      expect(onChangeMock).toHaveBeenCalledWith(expect.objectContaining({ files: [file1, file2] }));
    });
  });

  test('renders element without name prop as is', () => {
    render(
      <FormHandlerComponent>
        <div>
          <span data-testid="no-name">No Name</span>
        </div>
      </FormHandlerComponent>,
    );
    expect(screen.getByTestId('no-name')).toBeInTheDocument();
  });

  test('does not break if onSubmit is not provided', () => {
    render(
      <FormHandlerComponent>
        <div>
          <InputComponent
            name="test"
            inputType={FormInputType.Text}
            value="testvalue"
            data-testid="test-input"
          />
          <button type="submit" data-testid="submit-btn">Enviar</button>
        </div>
      </FormHandlerComponent>,
    );
    fireEvent.click(screen.getByTestId('submit-btn'));
    expect(screen.getByTestId('test-input')).toHaveValue('testvalue');
  });

  test('preserves state when keepState is true', () => {
    const initial = { username: 'persisted' };
    render(
      <FormHandlerComponent initialState={initial} keepState={true}>
        <div>
          <InputComponent
            name="username"
            inputType={FormInputType.Text}
            data-testid="persisted-input"
          />
        </div>
      </FormHandlerComponent>,
    );
    expect(screen.getByTestId('persisted-input')).toHaveValue('persisted');
  });

  test('extractValues: handles checked without value', () => {
    render(
      <FormHandlerComponent>
        <div>
          <input
            name="agree"
            type="checkbox"
            checked={true}
            data-testid="agree-checkbox"
          />
        </div>
      </FormHandlerComponent>,
    );
    expect(screen.getByTestId('agree-checkbox')).toBeChecked();
  });

  test('extractValues: handles nested elements with empty values', () => {
    render(
      <FormHandlerComponent>
        <div>
          <div>
            <InputComponent
              name="empty"
              inputType={FormInputType.Text}
              value=""
              data-testid="empty-input"
            />
          </div>
        </div>
      </FormHandlerComponent>,
    );
    expect(screen.getByTestId('empty-input')).toHaveValue('');
  });

  test('calls custom onChange handler of file input as well as FormHandler', () => {
    const inputOnChange = jest.fn();
    const formOnChange = jest.fn();
    render(
      <FormHandlerComponent onChange={formOnChange}>
        <div>
          <input
            name="file"
            type="file"
            data-testid="file-input"
            onChange={inputOnChange}
          />
        </div>
      </FormHandlerComponent>,
    );
    const file = new File(['hello'], 'hello.png', { type: 'image/png' });
    fireEvent.change(screen.getByTestId('file-input'), {
      target: { files: [file], multiple: false },
    });
    expect(inputOnChange).toHaveBeenCalled();
    return waitFor(() => {
      expect(formOnChange).toHaveBeenCalledWith(expect.objectContaining({ file }));
    });
  });

  test('calls custom onChange handler of checkbox as well as FormHandler', () => {
    const inputOnChange = jest.fn();
    const formOnChange = jest.fn();
    render(
      <FormHandlerComponent onChange={formOnChange}>
        <div>
          <input
            name="check"
            type="checkbox"
            data-testid="checkbox"
            onChange={inputOnChange}
          />
        </div>
      </FormHandlerComponent>,
    );
    fireEvent.click(screen.getByTestId('checkbox'));
    expect(inputOnChange).toHaveBeenCalled();
    return waitFor(() => {
      expect(formOnChange).toHaveBeenCalledWith(expect.objectContaining({ check: true }));
    });
  });

  test('handleInputChange handles file input with empty files array', () => {
    const onChangeMock = jest.fn();
    render(
      <FormHandlerComponent onChange={onChangeMock}>
        <div>
          <input name="file" type="file" data-testid="file-input" multiple />
        </div>
      </FormHandlerComponent>,
    );
    fireEvent.change(screen.getByTestId('file-input'), {
      target: { files: [], multiple: true },
    });
    expect(onChangeMock).not.toHaveBeenCalledWith(expect.objectContaining({ file: [] }));
  });

  test('calls onSubmit with empty object if no inputs present', () => {
    const onSubmitMock = jest.fn();
    render(
      <FormHandlerComponent onSubmit={onSubmitMock}>
        <form>
          <button type="submit" data-testid="submit-btn">Enviar</button>
        </form>
      </FormHandlerComponent>,
    );
    fireEvent.click(screen.getByTestId('submit-btn'));
    expect(onSubmitMock).toHaveBeenCalledWith({});
  });

  test('returns element as is if type is not recognized in processFormElement', () => {
    render(
      <FormHandlerComponent>
        <div>
          <div data-testid="unknown-element">Elemento desconocido</div>
        </div>
      </FormHandlerComponent>,
    );
    expect(screen.getByTestId('unknown-element')).toBeInTheDocument();
  });

  test('handles element without name prop correctly in processFormElement', () => {
    render(
      <FormHandlerComponent>
        <div>
          <div data-testid="no-name-element">
            <span>Element with no name prop</span>
          </div>
          <InputComponent
            name="withName"
            inputType={FormInputType.Text}
            data-testid="with-name-element"
            value="test"
          />
        </div>
      </FormHandlerComponent>,
    );

    // Verify both elements are rendered correctly
    expect(screen.getByTestId('no-name-element')).toBeInTheDocument();
    expect(screen.getByTestId('with-name-element')).toHaveValue('test');

    // Ensure no-name element was returned as is (not processed)
    expect(screen.getByTestId('no-name-element').textContent).toBe('Element with no name prop');
  });

  test('traverseChildrenElements returns non-input element with no children as is', () => {
    // This test verifies line 242
    render(
      <FormHandlerComponent>
        <div>
          <span data-testid="simple-element">Simple element</span>
          <div data-testid="empty-div"></div>
        </div>
      </FormHandlerComponent>,
    );

    expect(screen.getByTestId('simple-element')).toBeInTheDocument();
    expect(screen.getByTestId('simple-element').textContent).toBe('Simple element');
    expect(screen.getByTestId('empty-div')).toBeInTheDocument();
  });
});
