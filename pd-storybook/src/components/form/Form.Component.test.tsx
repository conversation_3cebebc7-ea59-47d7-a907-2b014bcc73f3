import { fireEvent, render, screen } from '@testing-library/react';

import { InputComponent } from '../input/Input.Component';

import { FormComponent, FormProps } from './Form.Component';

describe('FormComponent', () => {
  const mockOnChange = jest.fn();
  const mockOnClick = jest.fn();

  const defaultBtnSubmit = {
    children: 'Send',
    onClick: mockOnClick,
  };

  const defaultProps: FormProps = {
    inputs: [
      {
        label: 'Name',
        component: InputComponent,
        props: {
          name: 'name',
          value: '',
          onChange: mockOnChange,
          isRequired: true,
        },
        validate: (value) => (value ? undefined : 'Name is required'),
      },
    ],
    className: 'test-form',
  };

  it('must to render the form component correctly', () => {
    render(<FormComponent {...defaultProps} btnSubmit={defaultBtnSubmit} />);
    expect(screen.getByText('Name')).toBeInTheDocument();
    expect(screen.getByText('*')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /send/i })).toBeInTheDocument();
    expect(screen.getByTestId('form')).toHaveClass('test-form');
  });

  it('must to call onChange when the input value change', () => {
    render(<FormComponent {...defaultProps} />);
    const input = screen.getByRole('textbox');
    fireEvent.change(input, { target: { value: 'Juan' } });
    expect(mockOnChange).toHaveBeenCalled();
  });

  it('must to render correctly when btnSubmit is not provided', () => {
    const propsWithoutButton: FormProps = { ...defaultProps };
    render(<FormComponent {...propsWithoutButton} />);
    expect(screen.queryByRole('button')).toBeNull();
  });

  it('must to handle multiple inputs', () => {
    const multipleInputsProps: FormProps = {
      ...defaultProps,
      inputs: [
        ...defaultProps.inputs,
        {
          label: 'Last Name',
          component: InputComponent,
          props: {
            name: 'lastName',
            isRequired: false,
            value: '',
            onChange: mockOnChange,
          },
          validate: () => undefined,
        },
      ],
    };
    render(<FormComponent {...multipleInputsProps} />);
    expect(screen.getByText('Name')).toBeInTheDocument();
    expect(screen.getByText('Last Name')).toBeInTheDocument();
  });

  it('must to aply the custom clases correctly', () => {
    render(<FormComponent {...defaultProps} className="custom-class" />);
    expect(screen.getByTestId('form')).toHaveClass('custom-class');
  });

  it('must to render the required indicator when isRequired is true', () => {
    render(<FormComponent {...defaultProps} />);
    expect(screen.getByText('*')).toBeInTheDocument();
  });

  it('must to send the additional props to the Input component', () => {
    const placeholderText = 'Write your name';
    const propsWithPlaceholder: FormProps = {
      ...defaultProps,
      inputs: [
        {
          ...defaultProps.inputs[0],
          props: {
            ...defaultProps.inputs[0].props,
            placeholder: placeholderText,
          },
        },
      ],
    };
    render(<FormComponent {...propsWithPlaceholder} />);
    expect(screen.getByPlaceholderText(placeholderText)).toBeInTheDocument();
  });

  it('must call formValidation with the correct error state', () => {
    const mockFormValidation = jest.fn();
    const propsWithValidation: FormProps = {
      ...defaultProps,
      formValidation: mockFormValidation,
    };

    render(<FormComponent {...propsWithValidation} />);
    expect(mockFormValidation).toHaveBeenCalledWith(true); // Initially called with true because the required field is empty

    const input = screen.getByRole('textbox');
    fireEvent.change(input, { target: { value: 'John' } });
    expect(mockFormValidation).toHaveBeenCalledWith(false); // Called with false when field is filled
  });

  it('should not render label when label is empty string', () => {
    const propsWithEmptyLabel: FormProps = {
      ...defaultProps,
      inputs: [
        {
          ...defaultProps.inputs[0],
          label: '',
        },
      ],
    };
    render(<FormComponent {...propsWithEmptyLabel} />);
    expect(screen.queryByText('*')).not.toBeInTheDocument();
  });

  it('should not render label when label is undefined', () => {
    const propsWithoutLabel: FormProps = {
      ...defaultProps,
      inputs: [
        {
          ...defaultProps.inputs[0],
          label: undefined,
        },
      ],
    };
    render(<FormComponent {...propsWithoutLabel} />);
    expect(screen.queryByText('*')).not.toBeInTheDocument();
  });

  it('should render tooltip with icon when info prop is provided', () => {
    const tooltipContent = 'This is a help text';
    const propsWithInfo: FormProps = {
      ...defaultProps,
      inputs: [
        {
          ...defaultProps.inputs[0],
          props: {
            ...defaultProps.inputs[0].props,
            info: {
              tooltip: {
                content: tooltipContent,
              },
              iconName: 'x',
            },
          },
        },
      ],
    };

    render(<FormComponent {...propsWithInfo} />);
    expect(screen.getByTestId('x')).toBeInTheDocument();
  });

  it('should use default info icon when iconName is not provided', () => {
    const propsWithDefaultIcon: FormProps = {
      ...defaultProps,
      inputs: [
        {
          ...defaultProps.inputs[0],
          props: {
            ...defaultProps.inputs[0].props,
            info: {
              tooltip: {
                content: 'Help text',
              },
            },
          },
        },
      ],
    };

    render(<FormComponent {...propsWithDefaultIcon} />);
    expect(screen.getByTestId('info')).toBeInTheDocument();
  });

  it('should set formState correctly when dataErrorName is provided', () => {
    const dataErrorName = 'testError';
    const propsWithDataErrorName: FormProps = {
      ...defaultProps,
      dataErrorName,
    };

    render(<FormComponent {...propsWithDataErrorName} />);
    expect(screen.getByTestId('form')).toBeInTheDocument();
    expect(screen.getByTestId('form')).toHaveClass('test-form');
  });
});
