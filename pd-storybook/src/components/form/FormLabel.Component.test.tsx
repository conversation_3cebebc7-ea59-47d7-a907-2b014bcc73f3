import { render, screen } from '@testing-library/react';

import { FormLabel, FormLabelProps } from './FormLabel.Component';

describe('FormLabel', () => {
  const defaultProps: FormLabelProps = {
    title: 'Test Label',
  };

  it('should render the label title correctly', () => {
    render(<FormLabel {...defaultProps} />);
    expect(screen.getByText('Test Label')).toBeInTheDocument();
  });

  it('should render the tooltip when provided', () => {
    const tooltipContent = 'This is a tooltip';
    const propsWithTooltip: FormLabelProps = {
      ...defaultProps,
      tooltip: {
        content: tooltipContent,
      },
    };

    render(<FormLabel {...propsWithTooltip} />);
    expect(screen.getByTestId('tooltip')).toBeInTheDocument();
    expect(screen.getByText(tooltipContent)).toBeInTheDocument();
  });

  it('should render the default icon when tooltip is provided', () => {
    const propsWithTooltip: FormLabelProps = {
      ...defaultProps,
      tooltip: {
        content: 'Tooltip content',
      },
    };

    render(<FormLabel {...propsWithTooltip} />);
    expect(screen.getByTestId('tooltip')).toBeInTheDocument();
    expect(screen.getByTestId('warning')).toBeInTheDocument();
  });

  it('should not render the tooltip when not provided', () => {
    render(<FormLabel {...defaultProps} />);
    expect(screen.queryByTestId('tooltip')).toBeNull();
  });
});
