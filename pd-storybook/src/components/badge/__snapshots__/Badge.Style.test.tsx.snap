// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`StyledBadge Snapshots renders StyledBadge with active type correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  gap: 0.25rem;
  background-color: #CEE4F8;
  color: #1D1F24;
  font-size: 12px;
  line-height: 16px;
  padding: 0rem;
  width: auto;
  height: -webkit-min-content;
  height: -moz-min-content;
  height: min-content;
  min-height: 24px;
  min-width: 24px;
  border-radius: 16px;
  background-color: #E5F3DD;
  color: #5D923D;
  min-width: 64px;
}

<div
    class="emotion-0"
  >
    Active Badge
  </div>
</DocumentFragment>
`;

exports[`StyledBadge Snapshots renders StyledBadge with custom color correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  gap: 0.25rem;
  background-color: #FF5733;
  color: #1D1F24;
  font-size: 12px;
  line-height: 16px;
  padding: 0rem;
  width: auto;
  height: -webkit-min-content;
  height: -moz-min-content;
  height: min-content;
  min-height: 24px;
  min-width: 24px;
  border-radius: 16px;
}

<div
    class="emotion-0"
    color="#FF5733"
  >
    Custom Color Badge
  </div>
</DocumentFragment>
`;

exports[`StyledBadge Snapshots renders StyledBadge with danger type correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  gap: 0.25rem;
  background-color: #CEE4F8;
  color: #1D1F24;
  font-size: 12px;
  line-height: 16px;
  padding: 0rem;
  width: auto;
  height: -webkit-min-content;
  height: -moz-min-content;
  height: min-content;
  min-height: 24px;
  min-width: 24px;
  border-radius: 16px;
  background-color: #E55D57;
  color: #FFFFFF;
}

<div
    class="emotion-0"
  >
    Danger Badge
  </div>
</DocumentFragment>
`;

exports[`StyledBadge Snapshots renders StyledBadge with default props correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  gap: 0.25rem;
  background-color: #CEE4F8;
  color: #1D1F24;
  font-size: 12px;
  line-height: 16px;
  padding: 0rem;
  width: auto;
  height: -webkit-min-content;
  height: -moz-min-content;
  height: min-content;
  min-height: 24px;
  min-width: 24px;
  border-radius: 16px;
}

<div
    class="emotion-0"
  >
    Default Badge
  </div>
</DocumentFragment>
`;

exports[`StyledBadge Snapshots renders StyledBadge with disabled type correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  gap: 0.25rem;
  background-color: #CEE4F8;
  color: #1D1F24;
  font-size: 12px;
  line-height: 16px;
  padding: 0rem;
  width: auto;
  height: -webkit-min-content;
  height: -moz-min-content;
  height: min-content;
  min-height: 24px;
  min-width: 24px;
  border-radius: 16px;
  background-color: #F4F6F9;
  min-width: 84px;
}

<div
    class="emotion-0"
  >
    Disabled Badge
  </div>
</DocumentFragment>
`;

exports[`StyledBadge Snapshots renders StyledBadge with warning type correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  gap: 0.25rem;
  background-color: #CEE4F8;
  color: #1D1F24;
  font-size: 12px;
  line-height: 16px;
  padding: 0rem;
  width: auto;
  height: -webkit-min-content;
  height: -moz-min-content;
  height: min-content;
  min-height: 24px;
  min-width: 24px;
  border-radius: 16px;
  background-color: #F4A261;
  color: #FFFFFF;
}

<div
    class="emotion-0"
  >
    Warning Badge
  </div>
</DocumentFragment>
`;

exports[`StyledBadge Snapshots renders StyledIndicator with active type correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  width: 0.5rem;
  height: 0.5rem;
  border-radius: 50%;
  background-color: #5D923D;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`StyledBadge Snapshots renders StyledIndicator with disabled type correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  width: 0.5rem;
  height: 0.5rem;
  border-radius: 50%;
  background-color: #1D1F24;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;
