import { render, screen } from '@testing-library/react';

import Theme from '../../configurations/Theme.Configuration';

import { Badge, BadgeProps, BadgeType } from './Badge.Component';

describe('Badge Component', () => {
  const defaultProps: BadgeProps = {
    badgeType: BadgeType.Default,
    className: 'test-class',
  };

  it('renders correctly with default props', () => {
    render(<Badge {...defaultProps}>Default Badge</Badge>);
    const badge = screen.getByText('Default Badge');
    expect(badge).toBeInTheDocument();
    expect(badge).toHaveClass('test-class');
  });

  it('renders with default badge type and no icon or indicator', () => {
    render(<Badge badgeType={BadgeType.Default} />);
    const badge = screen.getByTestId('badge');
    expect(badge).toBeInTheDocument();
    expect(badge).not.toContainHTML('svg');
    expect(screen.queryByTestId('indicator')).not.toBeInTheDocument();
  });

  it('renders correctly with warning type and icon', () => {
    render(<Badge {...defaultProps} badgeType={BadgeType.Warning} />);
    const badge = screen.getByTestId('badge');
    expect(badge).toBeInTheDocument();
    expect(badge).toHaveStyle(`background-color: ${Theme.colors.orange}`);
    const icon = screen.getByTestId('exclamationMark');
    expect(icon).toBeInTheDocument();
  });

  it('renders correctly with danger type and icon', () => {
    render(<Badge {...defaultProps} badgeType={BadgeType.Danger} />);
    const badge = screen.getByTestId('badge');
    expect(badge).toBeInTheDocument();
    expect(badge).toHaveStyle(`background-color: ${Theme.colors.negative}`);
    const icon = screen.getByTestId('exclamationMark');
    expect(icon).toBeInTheDocument();
  });

  it('renders correctly with active type and indicator', () => {
    render(<Badge {...defaultProps} badgeType={BadgeType.Active} />);
    const badge = screen.getByTestId('badge');
    expect(badge).toBeInTheDocument();
    expect(badge).toHaveStyle(`background-color: ${Theme.colors.fadedGreen}`);
    const indicator = screen.getByTestId('indicator');
    expect(indicator).toBeInTheDocument();
    expect(screen.getByText('Active')).toBeInTheDocument();
  });

  it('renders correctly with disabled type and indicator', () => {
    render(<Badge {...defaultProps} badgeType={BadgeType.Disabled} />);
    const badge = screen.getByTestId('badge');
    expect(badge).toBeInTheDocument();
    expect(badge).toHaveStyle(`background-color: ${Theme.colors.fadedGray}`);
    const indicator = screen.getByTestId('indicator');
    expect(indicator).toBeInTheDocument();
    expect(screen.getByText('Disabled')).toBeInTheDocument();
  });

  it('renders correctly with outline type and no icon or indicator', () => {
    render(<Badge {...defaultProps} badgeType={BadgeType.Outline} />);
    const badge = screen.getByTestId('badge');
    expect(badge).toBeInTheDocument();
    expect(badge).not.toContainHTML('svg');
    expect(screen.queryByTestId('indicator')).not.toBeInTheDocument();
  });

  it('renders correctly with custom color', () => {
    render(<Badge {...defaultProps} color={Theme.colors.fadedPeach}>Default Badge</Badge>);
    const badge = screen.getByText('Default Badge');
    expect(badge).toBeInTheDocument();
    expect(badge).toHaveStyle(`background-color: ${Theme.colors.fadedPeach}`);
  });

  it('renders without children when children is not provided', () => {
    render(<Badge badgeType={BadgeType.Default} />);
    const badge = screen.getByTestId('badge');
    expect(badge).toBeInTheDocument();
    expect(badge).toHaveTextContent('');
  });

  it('applies correct icon for warning type', () => {
    render(<Badge badgeType={BadgeType.Warning}>Warning Badge</Badge>);
    const badge = screen.getByTestId('badge');
    expect(badge).toBeInTheDocument();
    expect(screen.getByTestId('warningCircle')).toBeInTheDocument();
  });

  it('applies correct icon for danger type', () => {
    render(<Badge badgeType={BadgeType.Danger}>Danger Badge</Badge>);
    const badge = screen.getByTestId('badge');
    expect(badge).toBeInTheDocument();
    expect(screen.getByTestId('warningCircle')).toBeInTheDocument();
  });

  it('displays children correctly when provided', () => {
    render(<Badge badgeType={BadgeType.Default}>Visible Text</Badge>);
    const badge = screen.getByTestId('badge');
    expect(badge).toBeInTheDocument();
    expect(badge).toHaveTextContent('Visible Text');
  });

  it('uses BadgeType.Default when badgeType prop is not provided', () => {
    render(<Badge>Test</Badge>);
    const badge = screen.getByTestId('badge');

    // Verificar que el badge tiene los estilos correspondientes al tipo Default
    expect(badge).toHaveStyle(`background-color: ${Theme.colors.fadedBlue}`);

    // Verificar que no tiene iconos ni indicadores que son específicos de otros tipos
    expect(screen.queryByTestId('indicator')).not.toBeInTheDocument();
    expect(screen.queryByTestId('warningCircle')).not.toBeInTheDocument();
    expect(screen.queryByTestId('exclamationMark')).not.toBeInTheDocument();
  });
});
