import { render } from '@testing-library/react';

import { StyledBadge, StyledIndicator } from './Badge.Style';

describe('StyledBadge Snapshots', () => {
  it('renders StyledBadge with default props correctly', () => {
    const { asFragment } = render(<StyledBadge>Default Badge</StyledBadge>);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders StyledBadge with warning type correctly', () => {
    const { asFragment } = render(<StyledBadge badgeType="warning">Warning Badge</StyledBadge>);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders StyledBadge with danger type correctly', () => {
    const { asFragment } = render(<StyledBadge badgeType="danger">Danger Badge</StyledBadge>);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders StyledBadge with active type correctly', () => {
    const { asFragment } = render(<StyledBadge badgeType="active">Active Badge</StyledBadge>);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders StyledBadge with disabled type correctly', () => {
    const { asFragment } = render(<StyledBadge badgeType="disabled">Disabled Badge</StyledBadge>);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders StyledBadge with custom color correctly', () => {
    const { asFragment } = render(<StyledBadge color="#FF5733">Custom Color Badge</StyledBadge>);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders StyledIndicator with active type correctly', () => {
    const { asFragment } = render(<StyledIndicator badgeType="active" />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders StyledIndicator with disabled type correctly', () => {
    const { asFragment } = render(<StyledIndicator badgeType="disabled" />);
    expect(asFragment()).toMatchSnapshot();
  });
});
