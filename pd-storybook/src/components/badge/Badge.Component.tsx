import React from 'react';

import Theme from '../../configurations/Theme.Configuration';
import { IconImporter } from '../iconImporter/IconImporter.Component';
import { IconName } from '../iconImporter/IconMap.Component';

import {
  StyledBadge, StyledBadgeProps,
  StyledIndicator,
} from './Badge.Style';

export enum BadgeType {
  Default = 'default',
  Outline = 'outline',
  Danger = 'danger',
  Warning = 'warning',
  Active = 'active',
  Disabled = 'disabled',
}
export interface BadgeProps extends StyledBadgeProps {
    badgeType?: BadgeType;
    className?: string;
}
export const Badge: React.FC<BadgeProps> = ({
  children,
  badgeType = BadgeType.Default,
  className,
  ...props
}) => {
  const circleIcon = 'warningCircle' as IconName;
  const tinyIcon = 'exclamationMark' as IconName;
  const hasChildren = !!children;

  return (
    <StyledBadge
      badgeType={badgeType}
      className={className}
      hasChildren={hasChildren}
      data-testid="badge"
      {...props}>
      {(badgeType === BadgeType.Active || badgeType === BadgeType.Disabled)
              && <StyledIndicator
                data-testid='indicator'
                badgeType={badgeType}>
                </StyledIndicator>
            }
      {(badgeType === BadgeType.Danger || badgeType === BadgeType.Warning)
              && <IconImporter
                size={hasChildren ? 24 : 20}
                color={hasChildren ? Theme.colors.fadedRed : Theme.colors.white}
                name={hasChildren ? circleIcon : tinyIcon }
                weight={hasChildren ? 'fill' : 'bold'}
                data-testid={hasChildren ? circleIcon : tinyIcon}
                />
            }
      { badgeType === BadgeType.Active && 'Active'}
      { badgeType === BadgeType.Disabled && 'Disabled'}
      {children}
    </StyledBadge>);
};
