import styled from '@emotion/styled';

import theme from '../../configurations/Theme.Configuration';

import { BadgeType } from './Badge.Component';

export interface StyledBadgeProps extends React.HTMLAttributes<HTMLDivElement> {
    color?: string;
    badgeType?: string;
    hasChildren?: boolean;
}

export interface GenericStyledBadgeProps extends React.HTMLAttributes<HTMLDivElement> {
    color: string;
}

export const StyledBadge = styled.div<StyledBadgeProps>`
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.25rem;
    background-color: ${(props) => props.color || theme.colors.fadedBlue};
    color: ${theme.colors.dark[700]};
    font-size: ${theme.fontSize.xxsm};
    padding: ${(props) => (props.hasChildren ? '0.25rem 0.5rem' : '0rem')};
    width: auto;
    height: min-content;
    min-height: 24px;
    min-width: 24px;
    border-radius: ${theme.borderRadius.xxl};

    ${(props) => props.badgeType === BadgeType.Warning && `
        background-color: ${theme.colors.orange};
        color: ${theme.colors.white};
    `}

    ${(props) => props.badgeType === BadgeType.Danger && `
        background-color: ${theme.colors.negative};
        color: ${theme.colors.white};
    `}

    ${(props) => props.badgeType === BadgeType.Active && `
        background-color: ${theme.colors.fadedGreen};
        color: ${theme.colors.positive};
        min-width: 64px;
    `}

    ${(props) => props.badgeType === BadgeType.Disabled && `
        background-color: ${theme.colors.fadedGray};
        min-width: 84px;
    `}

    ${(props) => props.badgeType === BadgeType.Outline && `
        background-color: ${theme.colors.transparent};
        color: ${(props.color || theme.colors.dark[700])};
        border: 1px solid ${(props.color || theme.colors.dark[700])};
    `}
`;

export const StyledIndicator = styled.div<StyledBadgeProps>`
    width: 0.5rem;
    height: 0.5rem;
    border-radius: ${theme.borderRadius.rounded};
    background-color: ${(props) => (props.badgeType === 'active' ? theme.colors.positive : theme.colors.dark[700])};
    justify-content: center;
    align-items: center;
`;
