import styled from '@emotion/styled';

import Theme from '../../configurations/Theme.Configuration';

export const StyledProvidersFormContainer = styled.section`
  width: 100%;
  min-width: 363px;
  padding: 20px; 
  background-color: ${Theme.colors.white};
  border-radius: 1rem;
  position: relative;
  box-shadow: ${Theme.shadow.lg};
`;

export const StyledTitle = styled.div`
  font-size: 1.25rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
`;

export const StyledProviderContainer = styled.article`
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid ${Theme.colors.fadedGray};
  margin-bottom: 10px;
  padding-right: 5px;
`;
export const StyledProviderInfo = styled.div`
  display: flex;
  align-items: center;
  gap: 14px;
  padding: 20px;

  > img {
    width: 44px;
    height: 44px;
    border-radius: 12px; 
  }
`;

export const StyledTextDiscount = styled.span`
  font-size: 16px;
  font-weight: semibold;
  margin-left: 5px;
  color: red;
`;

export const StyledTextAmount = styled.span`
  font-size: 12px;
  margin-left: 5px;
  font-weight: bold;
`;

export const StyledTextSm = styled.p`
  font-weight: semibold;
  font-size: 14px;
  color: ${Theme.colors.dark[500]};

  display: flex;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
  
`;

export const StyledTextXsm = styled.div`
  font-size: 12px;
  color: ${Theme.colors.dark[400]}
`;

export const StyledBoldSpan = styled.span`
  font-weight: bold;
  margin-left: 5px;
  color: ${Theme.colors.dark[700]}
`;

export const ContainerForm = styled.article`
  margin-bottom: 96px;
`;

export const DropdownWrapper = styled.div`
  height: 99px;
  position: relative;
  margin-top: 16px;
  margin-bottom: 16px;
`;

export const InputsWrapper = styled.div`
  display: flex;
  gap: 10px;
  margin-bottom: 16px;
`;

export const TextWrapper = styled.div`
  display: flex;
  gap: 5px;
`;

export const ProviderInfoContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 5px;
`;

export const StyledOptionName = styled.p`
  whiteSpace: nowrap;
  overflow: hidden;
  textOverflow: ellipsis;
`;

export const StyledNoProviderMessage = styled.div`
width: 100%;
height: 100px;
display: flex;
justify-content: center;
align-items: center;
`;
