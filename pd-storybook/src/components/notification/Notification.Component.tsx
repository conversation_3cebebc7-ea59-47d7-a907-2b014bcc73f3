import { useRef, useState } from 'react';

import { NotificationsProps } from '../header/Header.Component';
import {
  StyledHeaderNavNotificationButton,
  StyledNotificationsContainer,
  StyledNotificationsMenuContainer,
  StyledNotificationsMenuMessages,
} from '../header/Header.Style';
import { IconImporter } from '../iconImporter/IconImporter.Component';
import { RouteLink } from '../routeLink/RouteLink.Component';
import { Title } from '../title/Title.Component';

export const NotificationComponent = ({
  notifications,
}: {
  notifications: NotificationsProps[] | null;
}) => {
  const notificationsRef = useRef<HTMLDivElement>(null);
  const [isNotificationOpen, setIsNotificationOpen] = useState(false);

  const handleNotification = () => {
    setIsNotificationOpen(!isNotificationOpen);
  };

  return (
    <StyledNotificationsContainer ref={notificationsRef}>
      <StyledHeaderNavNotificationButton
        data-testid="notificationButton"
        onClick={handleNotification}
      >
        <IconImporter name={isNotificationOpen ? 'x' : 'bell'} />
      </StyledHeaderNavNotificationButton>
      {isNotificationOpen && (
        <StyledNotificationsMenuContainer isOpen={isNotificationOpen}>
          <Title as="h4" size="xsm" weight="regular">
            Notifications
          </Title>
          {notifications?.length ? (
            <StyledNotificationsMenuMessages>
              {notifications.map((notification) => (
                <RouteLink
                  to={notification.to}
                  className="!pd-text-dark-600 pd-py-2 pd-px-3 hover:pd-bg-slate-200 hover:!pd-text-dark-500 hover:pd-rounded"
                  size="xxsm"
                  weight="light"
                  key={notification.id}
                >
                  {notification.message}
                </RouteLink>
              ))}
            </StyledNotificationsMenuMessages>
          ) : (
            <Title as="h5" size="xxsm" weight="light" data-testid="noNotifications">
              No notifications
            </Title>
          )}
        </StyledNotificationsMenuContainer>
      )}
    </StyledNotificationsContainer>
  );
};
