import { MenuItem, MenuItemProps } from '../menuItem/MenuItem.Component';

import { StyledMenu, StyledMenuContainer } from './Menu.Style';

export interface MenuItemsProps {
    menuItems: MenuItemProps[];
}

export const Menu: React.FC<MenuItemsProps> = ({
  menuItems,
}) => (
  <StyledMenuContainer>
    <StyledMenu>
      {menuItems.map((item) => (
        <MenuItem key={item.id} {...item} />
      ))}
    </StyledMenu>
  </StyledMenuContainer>
);
