import { fireEvent, render, screen } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';

import { Menu, MenuItemsProps } from './Menu.Component';

const defaultItems: MenuItemsProps = {
  menuItems: [
    {
      id: '1',
      title: 'Menu',
      icon: 'airplane',
      items: [
        {
          id: '1',
          title: 'Sub Menu Item',
          to: '/sub-menu-item',
        },
        {
          id: '2',
          title: 'Sub Menu Item 2',
          to: '/sub-menu-item-2',
        },
      ],
      to: '/menu-item',
    },
    {
      id: '2',
      title: 'Another Menu',
      icon: 'buildings',
      items: [
        {
          id: '1',
          title: 'Sub Menu Item 3',
          to: '/sub-menu-item',
        },
        {
          id: '2',
          title: 'Sub Menu Item 4',
          to: '/sub-menu-item-2',
        },
      ],
      to: '/menu-item',
    },
    {
      id: '3',
      title: 'Without submenus',
      icon: 'clock',
      to: '/menu-item',
    },
    {
      id: '4',
      title: 'With many submenus',
      icon: 'video',
      items: [
        {
          id: '1',
          title: 'Sub Menu Item 5',
          to: '/sub-menu-item',
        },
        {
          id: '2',
          title: 'Sub Menu Item 6',
          to: '/sub-menu-item-2',
        },
        {
          id: '3',
          title: 'Sub Menu Item 7',
          to: '/sub-menu-item',
        },
        {
          id: '4',
          title: 'Sub Menu Item 8',
          to: '/sub-menu-item-2',
        },
        {
          id: '5',
          title: 'Sub Menu Item 9',
          to: '/sub-menu-item',
        },
        {
          id: '6',
          title: 'Sub Menu Item 10',
          to: '/sub-menu-item-2',
        },
      ],
      to: '/menu-item',
    },
  ],
};

const renderWithRouter = (ui: React.ReactElement) => render(<MemoryRouter>{ui}</MemoryRouter>);

describe('Menu Component', () => {
  test('renders Menu with menu items', () => {
    renderWithRouter(<Menu {...defaultItems} />);
    expect(screen.getByText('Menu')).toBeInTheDocument();
    expect(screen.getByText('Another Menu')).toBeInTheDocument();
    expect(screen.getByText('Without submenus')).toBeInTheDocument();
    expect(screen.getByText('With many submenus')).toBeInTheDocument();
  });

  test('renders sub menu items when menu item is clicked', () => {
    renderWithRouter(<Menu {...defaultItems} />);
    const menuItem = screen.getByText('Menu');
    fireEvent.click(menuItem);
    expect(screen.getByText('Sub Menu Item')).toBeInTheDocument();
    expect(screen.getByText('Sub Menu Item 2')).toBeInTheDocument();
  });

  test('toggles sub menu items visibility on click', () => {
    renderWithRouter(<Menu {...defaultItems} />);
    const menuItem = screen.getByText('Menu');
    fireEvent.click(menuItem);
    expect(screen.getByText('Sub Menu Item')).toBeVisible();
    fireEvent.click(menuItem);
    expect(screen.queryByText('Sub Menu Item')).not.toBeVisible();
  });

  test('renders without sub menu items', () => {
    const propsWithoutSubItems: MenuItemsProps = {
      menuItems: [
        {
          id: '1',
          title: 'Without submenus',
          icon: 'clock',
          to: '/menu-item',
        },
      ],
    };
    renderWithRouter(<Menu {...propsWithoutSubItems} />);
    expect(screen.getByText('Without submenus')).toBeInTheDocument();
    expect(screen.queryByText('Sub Menu Item')).not.toBeInTheDocument();
  });

  test('renders RouteLink when there are no sub menu items', () => {
    const propsWithoutSubItems: MenuItemsProps = {
      menuItems: [
        {
          id: '1',
          title: 'Without submenus',
          icon: 'clock',
          to: '/menu-item',
        },
      ],
    };
    renderWithRouter(<Menu {...propsWithoutSubItems} />);
    expect(screen.getByRole('link', { name: /without submenus/i })).toHaveAttribute('href', '/menu-item');
  });
});
