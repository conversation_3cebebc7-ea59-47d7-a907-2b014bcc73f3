import { render } from '@testing-library/react';

import { StyledMenu, StyledMenuContainer } from './Menu.Style';

describe('StyledMenu Snapshots', () => {
  it('renders StyledMenuContainer correctly', () => {
    const { asFragment } = render(<StyledMenuContainer />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders StyledMenu correctly', () => {
    const { asFragment } = render(<StyledMenu />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders StyledMenu with children correctly', () => {
    const { asFragment } = render(
      <StyledMenu>
        <div>Child Element</div>
      </StyledMenu>,
    );
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders StyledMenuContainer with custom className', () => {
    const { asFragment } = render(<StyledMenuContainer className="custom-class" />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders StyledMenu with custom className', () => {
    const { asFragment } = render(<StyledMenu className="custom-class" />);
    expect(asFragment()).toMatchSnapshot();
  });
});
