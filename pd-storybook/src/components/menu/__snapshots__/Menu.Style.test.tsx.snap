// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`StyledMenu Snapshots renders StyledMenu correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: 12px;
  height: 100%;
  width: 100%;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`StyledMenu Snapshots renders StyledMenu with children correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: 12px;
  height: 100%;
  width: 100%;
}

<div
    class="emotion-0"
  >
    <div>
      Child Element
    </div>
  </div>
</DocumentFragment>
`;

exports[`StyledMenu Snapshots renders StyledMenu with custom className 1`] = `
<DocumentFragment>
  .emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: 12px;
  height: 100%;
  width: 100%;
}

<div
    class="custom-class emotion-0"
  />
</DocumentFragment>
`;

exports[`StyledMenu Snapshots renders StyledMenuContainer correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  width: 240px;
  -webkit-flex: none;
  -ms-flex: none;
  flex: none;
  background-color: #F6F7FA;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`StyledMenu Snapshots renders StyledMenuContainer with custom className 1`] = `
<DocumentFragment>
  .emotion-0 {
  width: 240px;
  -webkit-flex: none;
  -ms-flex: none;
  flex: none;
  background-color: #F6F7FA;
}

<div
    class="custom-class emotion-0"
  />
</DocumentFragment>
`;
