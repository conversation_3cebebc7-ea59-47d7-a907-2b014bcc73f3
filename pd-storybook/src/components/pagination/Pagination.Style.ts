import styled from '@emotion/styled';

import Theme from '../../configurations/Theme.Configuration';

export interface StyledPaginationButtonProps {
  disabled: boolean;
}

export const PaginationWrapper = styled.nav`
  display: flex;
  justify-content: center;
`;

export const PaginationList = styled.ul`
  display: flex;
  list-style-type: none;
  padding: 0;
  margin: 0;
  align-items: center;
`;

export const PaginationItem = styled.li`
  margin: 0 4px;
  display: flex;
  align-items: center;
`;

export const PaginationButton = styled.button<StyledPaginationButtonProps>`
  padding: 10px;
  border-radius: 50%;
  color: ${(props) => (props.disabled ? Theme.colors.dark[600] : Theme.colors.primary)};
  cursor: ${(props) => (props.disabled ? 'not-allowed' : 'pointer')};
  transition: all 0.3s ease;

  &:hover:not(:disabled) {
    background-color: ${Theme.colors.primary};
    color: ${Theme.colors.white};
  }
`;

export const PageInput = styled.input`
  width: auto;
  padding: 6px 0 6px 12px;
  text-align: center;
  border: 1px solid ${Theme.colors.line};
  border-radius: 8px;
  margin-right: 8px;
`;

export const PageInputLabel = styled.span`
  font-size: 14px;
  color: ${Theme.colors.dark[700]};
`;
