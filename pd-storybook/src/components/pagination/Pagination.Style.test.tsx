import { render } from '@testing-library/react';

import {
  PageInput,
  PageInputLabel,
  PaginationButton,
  PaginationItem,
  PaginationList,
  PaginationWrapper,
} from './Pagination.Style';

describe('StyledPagination Snapshots', () => {
  it('renders PaginationWrapper correctly', () => {
    const { asFragment } = render(<PaginationWrapper />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders PaginationList correctly', () => {
    const { asFragment } = render(<PaginationList />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders PaginationItem correctly', () => {
    const { asFragment } = render(<PaginationItem />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders PaginationButton correctly when enabled', () => {
    const { asFragment } = render(
      <PaginationButton disabled={false}>Enabled Button</PaginationButton>,
    );
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders PaginationButton correctly when disabled', () => {
    const { asFragment } = render(
      <PaginationButton disabled={true}>Disabled Button</PaginationButton>,
    );
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders PageInput correctly', () => {
    const { asFragment } = render(<PageInput />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders PageInputLabel correctly', () => {
    const { asFragment } = render(<PageInputLabel>Page Label</PageInputLabel>);
    expect(asFragment()).toMatchSnapshot();
  });
});
