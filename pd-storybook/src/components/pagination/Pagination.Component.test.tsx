import { fireEvent, render, screen } from '@testing-library/react';

import { Pagination } from './Pagination.Component';

describe('Pagination Component', () => {
  const mockOnPageChange = jest.fn();

  const defaultProps = {
    totalPages: 10,
    onPageChange: mockOnPageChange,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Rendering', () => {
    it('renders initial state correctly', () => {
      render(<Pagination {...defaultProps} />);

      expect(screen.getByRole('spinbutton')).toHaveValue(1);
      expect(screen.getByText('1 de 10')).toBeInTheDocument();
      expect(screen.getAllByRole('button')).toHaveLength(4);
    });

    it('renders with custom initial page', () => {
      render(<Pagination {...defaultProps} initialPage={5} />);

      expect(screen.getByRole('spinbutton')).toHaveValue(5);
      expect(screen.getByText('5 de 10')).toBeInTheDocument();
    });
  });

  describe('Navigation Controls', () => {
    it('handles next page click', () => {
      render(<Pagination {...defaultProps} initialPage={5} />);

      fireEvent.click(screen.getByTestId('caretRight'));
      expect(mockOnPageChange).toHaveBeenCalledWith(6);
    });

    it('handles previous page click', () => {
      render(<Pagination {...defaultProps} initialPage={5} />);

      fireEvent.click(screen.getByTestId('caretLeft'));
      expect(mockOnPageChange).toHaveBeenCalledWith(4);
    });

    it('handles first page navigation', () => {
      render(<Pagination {...defaultProps} initialPage={5} />);

      fireEvent.click(screen.getByTestId('caretLineLeft'));
      expect(mockOnPageChange).toHaveBeenCalledWith(1);
    });

    it('handles last page navigation', () => {
      render(<Pagination {...defaultProps} initialPage={5} />);

      fireEvent.click(screen.getByTestId('caretLineRight'));
      expect(mockOnPageChange).toHaveBeenCalledWith(10);
    });
  });

  describe('Input Handling', () => {
    it('updates page on valid input change and blur', () => {
      render(<Pagination {...defaultProps} />);
      const input = screen.getByRole('spinbutton');

      fireEvent.change(input, { target: { value: '5' } });
      fireEvent.blur(input);

      expect(mockOnPageChange).toHaveBeenCalledWith(5);
    });

    it('updates page on Enter key press', () => {
      render(<Pagination {...defaultProps} />);
      const input = screen.getByRole('spinbutton');

      fireEvent.change(input, { target: { value: '7' } });
      fireEvent.keyDown(input, { key: 'Enter' });

      expect(mockOnPageChange).toHaveBeenCalledWith(7);
    });

    it('corrects values exceeding total pages', () => {
      render(<Pagination {...defaultProps} />);
      const input = screen.getByRole('spinbutton');

      fireEvent.change(input, { target: { value: '15' } });
      fireEvent.blur(input);

      expect(mockOnPageChange).toHaveBeenCalledWith(10);
    });
  });

  describe('Loading State', () => {
    it('disables all controls when loading', () => {
      render(<Pagination {...defaultProps} isLoading={true} />);

      const buttons = screen.getAllByRole('button');
      const input = screen.getByRole('spinbutton');

      buttons.forEach((button) => {
        expect(button).toBeDisabled();
      });
      expect(input).toBeDisabled();
    });
  });
});
