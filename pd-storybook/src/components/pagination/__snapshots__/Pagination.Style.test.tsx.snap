// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`StyledPagination Snapshots renders PageInput correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  width: auto;
  padding: 6px 0 6px 12px;
  text-align: center;
  border: 1px solid #E0E3E8;
  border-radius: 8px;
  margin-right: 8px;
}

<input
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`StyledPagination Snapshots renders PageInputLabel correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-size: 14px;
  color: #1D1F24;
}

<span
    class="emotion-0"
  >
    Page Label
  </span>
</DocumentFragment>
`;

exports[`StyledPagination Snapshots renders PaginationButton correctly when disabled 1`] = `
<DocumentFragment>
  .emotion-0 {
  padding: 10px;
  border-radius: 50%;
  color: #3A3D44;
  cursor: not-allowed;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
}

.emotion-0:hover:not(:disabled) {
  background-color: #17448d;
  color: #FFFFFF;
}

<button
    class="emotion-0"
    disabled=""
  >
    Disabled <PERSON><PERSON>
  </button>
</DocumentFragment>
`;

exports[`StyledPagination Snapshots renders PaginationButton correctly when enabled 1`] = `
<DocumentFragment>
  .emotion-0 {
  padding: 10px;
  border-radius: 50%;
  color: #17448d;
  cursor: pointer;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
}

.emotion-0:hover:not(:disabled) {
  background-color: #17448d;
  color: #FFFFFF;
}

<button
    class="emotion-0"
  >
    Enabled Button
  </button>
</DocumentFragment>
`;

exports[`StyledPagination Snapshots renders PaginationItem correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0 4px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

<li
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`StyledPagination Snapshots renders PaginationList correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  list-style-type: none;
  padding: 0;
  margin: 0;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

<ul
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`StyledPagination Snapshots renders PaginationWrapper correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

<nav
    class="emotion-0"
  />
</DocumentFragment>
`;
