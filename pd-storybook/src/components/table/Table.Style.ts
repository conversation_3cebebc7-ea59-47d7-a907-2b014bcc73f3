import styled from '@emotion/styled';

import Theme from '../../configurations/Theme.Configuration';

export const StyledTableContainer = styled.main<{ boxShadow?: string }>`
    background-color: ${Theme.colors.white};
    border-radius: ${Theme.borderRadius.xl};
    padding: 18px;
    width: 100%;
    display: flex;
    flex-direction: column;
    box-shadow: ${({ boxShadow }) => boxShadow || Theme.shadow.lg};
    gap:16px;
`;

export const StyledTableHeader = styled.th<{ width?: string, align?: string }>`
    padding: 10px 4px;
    font-weight: ${Theme.fontWeight.medium};
    text-align: ${({ align }) => align || 'left'};
    font-size: ${Theme.fontSize.xsm};
    color: ${Theme.colors.dark[500]};
    border-bottom: 1px solid ${Theme.colors.line};
    width: ${({ width }) => width || 'auto'};
`;

export const StyledTableCell = styled.td<{ width?: string }>`
    border-bottom: 1px solid ${Theme.colors.line};
    font-size: ${Theme.fontSize.sm};
    color: ${Theme.colors.dark[600]};
    font-weight: ${Theme.fontWeight.regular};
    padding: 18px 8px;
    text-align: left;
    width: ${({ width }) => width || 'auto'};
`;

export const StyledTableNav = styled.nav`
    display: flex;
    justify-content: space-between;
    align-items: center;
`;

export const StyledTableAction = styled.div`
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0 auto;
    padding: 8px;
    border-radius: ${Theme.borderRadius.DEFAULT};
    height: 32px;
    transition: all 300ms ease-in-out;
    &:hover {
        cursor: pointer;
        background-color: ${Theme.colors.dark[200]};
    }
`;

export const StyledActionsMenu = styled.div`
    position: absolute;
    display: flex;
    flex-direction: column;
    padding: 8px;
    gap: 8px;
    background-color: ${Theme.colors.white};
    box-shadow: ${Theme.shadow.lg};
    border-radius: ${Theme.borderRadius.DEFAULT};
    top: 25px;
    right: 0;
    z-index: 100;
`;

export const StyledTableRow = styled.tr`
  background-color: ${({ isDisabled }: {isDisabled: boolean}) => (isDisabled ? Theme.colors.dark[200] : Theme.colors.white)};
`;

export const StyledActionsButton = styled.button`
    display: flex;
    align-items: center;
    font-size: 13px;

    p {
        margin-left: 5px;
    }
`;

export const StyledModalOverlay = styled.div`
  position: fixed;
  inset: 0;
  background-color: ${Theme.colors.dark[600]};
  opacity: 0.5;
  z-index: 10;
  border-radius: 0.75rem;
`;

export const StyledModalContent = styled.div`
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: ${Theme.colors.white};
  border: 1px solid ${Theme.colors.line};
  border-radius: 0.75rem;
  box-shadow: ${Theme.shadow.lg};
  z-index: 100;
  display: flex;
  flex-direction: column;
  max-width: 18rem;
`;

export const StyledModalBody = styled.div`
  width: 18rem;

  > p {
    align-self: ${({ align }: {align?: string}) => align || 'center'} ;
    text-align: ${({ align }: {align?: string}) => align || 'center'} ;
  }

  > div {
    display: flex;
    gap: 0.5rem;
    margin-top: 0.5rem;
    flex-wrap: wrap;
    justify-content: center;
  }
`;

export const StyledLoadingContainer = styled.div`
  height: 100px;

    > div:nth-child(1) {
      position: absolute;
      left: 50%;
      width: 100%;
      padding-top: 2em;
      margin-left: 7em; 
    }

    > div:nth-child(2) {
      display: flex;
      justify-content: center;
      margin-left: 14em; 
    }
`;

export const LoaderContainer = styled.td`
  height: 45vh;
`;
