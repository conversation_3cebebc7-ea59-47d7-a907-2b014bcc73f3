// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Table Component Snapshots renders correctly with boolean values 1`] = `
.emotion-0 {
  background-color: #FFFFFF;
  border-radius: 12px;
  padding: 18px;
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1),0 4px 6px -4px rgb(0 0 0 / 0.1);
  gap: 16px;
}

.emotion-1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-2 {
  padding: 10px 4px;
  font-weight: 500;
  text-align: left;
  font-size: 13px;
  line-height: 18px;
  color: #6B6E75;
  border-bottom: 1px solid #E0E3E8;
  width: 2%;
}

.emotion-3 {
  padding: 10px 4px;
  font-weight: 500;
  text-align: left;
  font-size: 13px;
  line-height: 18px;
  color: #6B6E75;
  border-bottom: 1px solid #E0E3E8;
  width: auto;
}

.emotion-7 {
  background-color: #FFFFFF;
}

.emotion-8 {
  border-bottom: 1px solid #E0E3E8;
  font-size: 14px;
  line-height: 20px;
  color: #3A3D44;
  font-weight: 400;
  padding: 18px 8px;
  text-align: left;
  width: auto;
}

<div>
  <main
    class="emotion-0"
  >
    <div
      class="pd-flex pd-justify-between"
    >
      <nav
        class="emotion-1"
      >
        <h2>
          2
           
          Products
        </h2>
      </nav>
      <div
        class="pd-flex pd-gap-2 pd-items-center"
      />
    </div>
    <table>
      <thead>
        <tr>
          <th
            class="emotion-2"
            width="2%"
          >
            <input
              data-testid="selectAll"
              name="selectAll"
              type="checkbox"
            />
          </th>
          <th
            class="emotion-3"
          >
            Name
          </th>
          <th
            class="emotion-3"
          >
            Age
          </th>
          <th
            class="emotion-3"
          >
            Actions
          </th>
          <th
            class="emotion-3"
          >
            Active
          </th>
        </tr>
      </thead>
      <tbody>
        <tr
          class="emotion-7"
        >
          <td
            class="emotion-8"
          >
            <input
              data-testid="selectRow"
              name="selectRow"
              type="checkbox"
            />
          </td>
          <td
            class="emotion-8"
          >
            John Doe
          </td>
          <td
            class="emotion-8"
          >
            30
          </td>
          <td
            class="emotion-8"
          />
          <td
            class="emotion-8"
          />
        </tr>
        <tr
          class="emotion-7"
        >
          <td
            class="emotion-8"
          >
            <input
              data-testid="selectRow"
              name="selectRow"
              type="checkbox"
            />
          </td>
          <td
            class="emotion-8"
          >
            Jane Doe
          </td>
          <td
            class="emotion-8"
          >
            25
          </td>
          <td
            class="emotion-8"
          />
          <td
            class="emotion-8"
          />
        </tr>
      </tbody>
    </table>
  </main>
</div>
`;

exports[`Table Component Snapshots renders correctly with default props 1`] = `
.emotion-0 {
  background-color: #FFFFFF;
  border-radius: 12px;
  padding: 18px;
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1),0 4px 6px -4px rgb(0 0 0 / 0.1);
  gap: 16px;
}

.emotion-1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-2 {
  padding: 10px 4px;
  font-weight: 500;
  text-align: left;
  font-size: 13px;
  line-height: 18px;
  color: #6B6E75;
  border-bottom: 1px solid #E0E3E8;
  width: 2%;
}

.emotion-3 {
  padding: 10px 4px;
  font-weight: 500;
  text-align: left;
  font-size: 13px;
  line-height: 18px;
  color: #6B6E75;
  border-bottom: 1px solid #E0E3E8;
  width: auto;
}

.emotion-6 {
  background-color: #FFFFFF;
}

.emotion-7 {
  border-bottom: 1px solid #E0E3E8;
  font-size: 14px;
  line-height: 20px;
  color: #3A3D44;
  font-weight: 400;
  padding: 18px 8px;
  text-align: left;
  width: auto;
}

<div>
  <main
    class="emotion-0"
  >
    <div
      class="pd-flex pd-justify-between"
    >
      <nav
        class="emotion-1"
      >
        <h2>
          2
           
          Products
        </h2>
      </nav>
      <div
        class="pd-flex pd-gap-2 pd-items-center"
      />
    </div>
    <table>
      <thead>
        <tr>
          <th
            class="emotion-2"
            width="2%"
          >
            <input
              data-testid="selectAll"
              name="selectAll"
              type="checkbox"
            />
          </th>
          <th
            class="emotion-3"
          >
            Name
          </th>
          <th
            class="emotion-3"
          >
            Age
          </th>
          <th
            class="emotion-3"
          >
            Actions
          </th>
        </tr>
      </thead>
      <tbody>
        <tr
          class="emotion-6"
        >
          <td
            class="emotion-7"
          >
            <input
              data-testid="selectRow"
              name="selectRow"
              type="checkbox"
            />
          </td>
          <td
            class="emotion-7"
          >
            John Doe
          </td>
          <td
            class="emotion-7"
          >
            30
          </td>
          <td
            class="emotion-7"
          />
        </tr>
        <tr
          class="emotion-6"
        >
          <td
            class="emotion-7"
          >
            <input
              data-testid="selectRow"
              name="selectRow"
              type="checkbox"
            />
          </td>
          <td
            class="emotion-7"
          >
            Jane Doe
          </td>
          <td
            class="emotion-7"
          >
            25
          </td>
          <td
            class="emotion-7"
          />
        </tr>
      </tbody>
    </table>
  </main>
</div>
`;

exports[`Table Component Snapshots renders correctly with empty rows 1`] = `
.emotion-0 {
  background-color: #FFFFFF;
  border-radius: 12px;
  padding: 18px;
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1),0 4px 6px -4px rgb(0 0 0 / 0.1);
  gap: 16px;
}

.emotion-1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-2 {
  padding: 10px 4px;
  font-weight: 500;
  text-align: left;
  font-size: 13px;
  line-height: 18px;
  color: #6B6E75;
  border-bottom: 1px solid #E0E3E8;
  width: 2%;
}

.emotion-3 {
  padding: 10px 4px;
  font-weight: 500;
  text-align: left;
  font-size: 13px;
  line-height: 18px;
  color: #6B6E75;
  border-bottom: 1px solid #E0E3E8;
  width: auto;
}

<div>
  <main
    class="emotion-0"
  >
    <div
      class="pd-flex pd-justify-between"
    >
      <nav
        class="emotion-1"
      >
        <h2>
          0
           
          Products
        </h2>
      </nav>
      <div
        class="pd-flex pd-gap-2 pd-items-center"
      />
    </div>
    <table>
      <thead>
        <tr>
          <th
            class="emotion-2"
            width="2%"
          >
            <input
              data-testid="selectAll"
              name="selectAll"
              type="checkbox"
            />
          </th>
          <th
            class="emotion-3"
          >
            Name
          </th>
          <th
            class="emotion-3"
          >
            Age
          </th>
          <th
            class="emotion-3"
          >
            Actions
          </th>
        </tr>
      </thead>
      <tbody />
    </table>
  </main>
</div>
`;

exports[`Table Component Snapshots renders correctly with many columns 1`] = `
.emotion-0 {
  background-color: #FFFFFF;
  border-radius: 12px;
  padding: 18px;
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1),0 4px 6px -4px rgb(0 0 0 / 0.1);
  gap: 16px;
}

.emotion-1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-2 {
  padding: 10px 4px;
  font-weight: 500;
  text-align: left;
  font-size: 13px;
  line-height: 18px;
  color: #6B6E75;
  border-bottom: 1px solid #E0E3E8;
  width: 2%;
}

.emotion-3 {
  padding: 10px 4px;
  font-weight: 500;
  text-align: left;
  font-size: 13px;
  line-height: 18px;
  color: #6B6E75;
  border-bottom: 1px solid #E0E3E8;
  width: auto;
}

.emotion-8 {
  background-color: #FFFFFF;
}

.emotion-9 {
  border-bottom: 1px solid #E0E3E8;
  font-size: 14px;
  line-height: 20px;
  color: #3A3D44;
  font-weight: 400;
  padding: 18px 8px;
  text-align: left;
  width: auto;
}

<div>
  <main
    class="emotion-0"
  >
    <div
      class="pd-flex pd-justify-between"
    >
      <nav
        class="emotion-1"
      >
        <h2>
          2
           
          Products
        </h2>
      </nav>
      <div
        class="pd-flex pd-gap-2 pd-items-center"
      />
    </div>
    <table>
      <thead>
        <tr>
          <th
            class="emotion-2"
            width="2%"
          >
            <input
              data-testid="selectAll"
              name="selectAll"
              type="checkbox"
            />
          </th>
          <th
            class="emotion-3"
          >
            Name
          </th>
          <th
            class="emotion-3"
          >
            Age
          </th>
          <th
            class="emotion-3"
          >
            Actions
          </th>
          <th
            class="emotion-3"
          >
            Email
          </th>
          <th
            class="emotion-3"
          >
            Phone
          </th>
        </tr>
      </thead>
      <tbody>
        <tr
          class="emotion-8"
        >
          <td
            class="emotion-9"
          >
            <input
              data-testid="selectRow"
              name="selectRow"
              type="checkbox"
            />
          </td>
          <td
            class="emotion-9"
          >
            John Doe
          </td>
          <td
            class="emotion-9"
          >
            30
          </td>
          <td
            class="emotion-9"
          />
          <td
            class="emotion-9"
          >
            <EMAIL>
          </td>
          <td
            class="emotion-9"
          >
            1234567890
          </td>
        </tr>
        <tr
          class="emotion-8"
        >
          <td
            class="emotion-9"
          >
            <input
              data-testid="selectRow"
              name="selectRow"
              type="checkbox"
            />
          </td>
          <td
            class="emotion-9"
          >
            Jane Doe
          </td>
          <td
            class="emotion-9"
          >
            25
          </td>
          <td
            class="emotion-9"
          />
          <td
            class="emotion-9"
          >
            <EMAIL>
          </td>
          <td
            class="emotion-9"
          >
            0987654321
          </td>
        </tr>
      </tbody>
    </table>
  </main>
</div>
`;

exports[`Table Component Snapshots renders correctly with single row 1`] = `
.emotion-0 {
  background-color: #FFFFFF;
  border-radius: 12px;
  padding: 18px;
  width: 100%;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1),0 4px 6px -4px rgb(0 0 0 / 0.1);
  gap: 16px;
}

.emotion-1 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
}

.emotion-2 {
  padding: 10px 4px;
  font-weight: 500;
  text-align: left;
  font-size: 13px;
  line-height: 18px;
  color: #6B6E75;
  border-bottom: 1px solid #E0E3E8;
  width: 2%;
}

.emotion-3 {
  padding: 10px 4px;
  font-weight: 500;
  text-align: left;
  font-size: 13px;
  line-height: 18px;
  color: #6B6E75;
  border-bottom: 1px solid #E0E3E8;
  width: auto;
}

.emotion-6 {
  background-color: #FFFFFF;
}

.emotion-7 {
  border-bottom: 1px solid #E0E3E8;
  font-size: 14px;
  line-height: 20px;
  color: #3A3D44;
  font-weight: 400;
  padding: 18px 8px;
  text-align: left;
  width: auto;
}

<div>
  <main
    class="emotion-0"
  >
    <div
      class="pd-flex pd-justify-between"
    >
      <nav
        class="emotion-1"
      >
        <h2>
          1
           
          Products
        </h2>
      </nav>
      <div
        class="pd-flex pd-gap-2 pd-items-center"
      />
    </div>
    <table>
      <thead>
        <tr>
          <th
            class="emotion-2"
            width="2%"
          >
            <input
              data-testid="selectAll"
              name="selectAll"
              type="checkbox"
            />
          </th>
          <th
            class="emotion-3"
          >
            Name
          </th>
          <th
            class="emotion-3"
          >
            Age
          </th>
          <th
            class="emotion-3"
          >
            Actions
          </th>
        </tr>
      </thead>
      <tbody>
        <tr
          class="emotion-6"
        >
          <td
            class="emotion-7"
          >
            <input
              data-testid="selectRow"
              name="selectRow"
              type="checkbox"
            />
          </td>
          <td
            class="emotion-7"
          >
            John Doe
          </td>
          <td
            class="emotion-7"
          >
            30
          </td>
          <td
            class="emotion-7"
          />
        </tr>
      </tbody>
    </table>
  </main>
</div>
`;
