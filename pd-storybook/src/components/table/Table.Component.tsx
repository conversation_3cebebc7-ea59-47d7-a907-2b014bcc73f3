import React, {
  PropsWithChildren, useEffect, useRef, useState,
} from 'react';

import Theme from '../../configurations/Theme.Configuration';
import { Button } from '../button/Button.Component';
import { Filter, FilterProps } from '../filter/Filter.Component';
import { IconImporter } from '../iconImporter/IconImporter.Component';
import { SearchBox, SearchBoxProps } from '../searchBox/SearchBox.Component';

import {
  LoaderContainer,
  StyledActionsButton,
  StyledActionsMenu,
  StyledModalBody,
  StyledModalContent,
  StyledModalOverlay,
  StyledTableAction,
  StyledTableCell,
  StyledTableContainer,
  StyledTableHeader,
  StyledTableNav,
  StyledTableRow,
} from './Table.Style';
import { tableConstants } from './Table.constants';

export type TextAlign = 'center' | 'left' | 'right' | 'justify' | 'char' | undefined;

// eslint-disable-next-line @typescript-eslint/no-explicit-any
type ColumnRenderer = (value: any, align: TextAlign) => React.ReactNode;

export interface Column {
  header: string;
  dataKey: string;
  width?: string;
  renderer?: ColumnRenderer;
}

export interface Row {
  id: string | number;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [dataKey: string]: any;
}

export interface ActionProps {
  enableDisableProduct?: (row: Row|undefined) => void;
  editProduct?: (id: string | number) => void;
  deleteProduct?: (id: string | number) => void;
}

export interface HandleSetEnableDisableProps {
  row: Row;
}

export interface TableProps<T extends string = string> {
  columns: Column[];
  rows?: Row[];
  totalItems: number;
  selectTable?: boolean;
  actions?: ActionProps;
  classname?: string;
  searchBox?: SearchBoxProps;
  filter?: FilterProps<T>;
  totalItemsLabel?: string,
  actionsLabel?: string,
  boxShadow?: string;
  hideTitle?: boolean;
  align?: TextAlign;
  isLoading?: boolean;
  topRightButton?: React.ReactNode;
}

export interface EnableDisableProps {
  rowsDisabled: Row[];
  enableDisableRowSelected: Row | null;
  selectedRows: Row[];
  actions?: ActionProps;
  setRowsDisabled: React.Dispatch<React.SetStateAction<Row[]>>;
  enabled: boolean;
  setEnabled: React.Dispatch<React.SetStateAction<boolean>>;
  setConfirmationModal: React.Dispatch<React.SetStateAction<boolean>>;
}

const RenderCell = ({ row, column, align }: { row: Row; column: Column, align?: TextAlign }) => {
  const value = row[column.dataKey];
  return (
    <>{column.renderer ? column.renderer(value, align) : (value as React.ReactNode)}</>
  );
};

export function Table<T extends string = string>({
  columns,
  rows = [],
  totalItems,
  selectTable,
  actions,
  classname,
  searchBox,
  filter,
  totalItemsLabel,
  actionsLabel,
  hideTitle,
  align,
  isLoading,
  topRightButton,
  ...props
}: PropsWithChildren<TableProps<T>>) {
  const [selectedRows, setSelectedRows] = useState<Row[]>([]);
  const [selectAll, setSelectAll] = useState<boolean>(false);
  const [openRowId, setOpenRowId] = useState<string | number | null>(null);
  const [confirmationModal, setConfirmationModal] = useState<boolean>(false);
  const [enableDisableRowSelected, setEnableDisableRowSelected] = useState<Row|undefined>(undefined);
  const menuRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setOpenRowId(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleSelectAll = () => {
    const newSelectAll = !selectAll;
    setSelectAll(newSelectAll);
    setSelectedRows(newSelectAll ? [...rows] : []);
  };

  const handleSelectRow = (row: Row) => {
    const isSelected = selectedRows.some(
      (selectedRow) => selectedRow.id === row.id,
    );
    const newSelectedRows = isSelected
      ? selectedRows.filter((selectedRow) => selectedRow.id !== row.id)
      : [...selectedRows, row];
    setSelectedRows(newSelectedRows);
    setSelectAll(newSelectedRows.length === rows?.length);
  };

  const handleActionClick = (rowId: string | number) => {
    setOpenRowId((prevOpenRowId) => (prevOpenRowId === rowId ? null : rowId));
  };

  return (
    <StyledTableContainer className={classname} {...props}>
      <div className='pd-flex pd-justify-between'>
        { !hideTitle && <StyledTableNav>
          <h2>
            {totalItems} {totalItemsLabel || tableConstants.TOTAL_ITEMS_LABEL}
          </h2>
        </StyledTableNav>}
        <div className='pd-flex pd-gap-2 pd-items-center'>
          {searchBox && <SearchBox {...searchBox} />}
          {filter && <Filter {...filter} />}
          { topRightButton }
        </div>
      </div>
      <table>
        <thead>
          <tr>
            {selectTable && (
              <StyledTableHeader width='2%'>
                <input
                  name='selectAll'
                  type='checkbox'
                  checked={selectAll}
                  onChange={handleSelectAll}
                  data-testid='selectAll'
                />
              </StyledTableHeader>
            )}
            {columns.map((column) => (
              <StyledTableHeader key={column.dataKey} width={column.width} align={align}>
                {column.header}
              </StyledTableHeader>
            ))}
            {actions && (
              <StyledTableHeader width='3%'>{actionsLabel || tableConstants.ACTION_LABEL}</StyledTableHeader>
            )}
          </tr>
        </thead>
        <tbody>
          {isLoading ? <tr>
            <LoaderContainer colSpan={7} className="text-center">
              <div className="loader">Cargando...</div>
            </LoaderContainer>
          </tr> : rows.map((row) => (
            <StyledTableRow
              key={row.id}
              isDisabled={row.status || false}
              >
              {selectTable && (
              <StyledTableCell>
                <input
                  name='selectRow'
                  type='checkbox'
                  checked={selectedRows.some(
                    (selectedRow) => selectedRow.id === row.id,
                  )}
                  onChange={() => handleSelectRow(row)}
                  data-testid='selectRow'
                  disabled={row.status || false}
                    />
              </StyledTableCell>
              )}
              {columns.map((column) => (
                <StyledTableCell
                  key={`${column.dataKey}-${row.id}`}
                  width={column.width}
                  >
                  <RenderCell row={row} column={column} align={align}/>
                </StyledTableCell>
              ))}
              {actions && (
              <StyledTableCell>
                <StyledTableAction
                  onClick={() => handleActionClick(row.id) }
                    >
                  <IconImporter
                    size={24}
                    name='dotsThree'
                    className='pd-mx-auto'
                    data-testid='dotsThree'
                      />
                  {openRowId === row.id && (
                  <StyledActionsMenu
                    ref={menuRef}
                    data-testid='actions-menu'
                        >
                    {!row.status ? (
                      <StyledActionsButton
                        onClick={() => {
                          setEnableDisableRowSelected(row);
                          setConfirmationModal(true);
                        }}
                        data-testid='disable-button'
                            >
                        <IconImporter
                          size={16}
                          color={Theme.colors.dark[600]}
                          name='eyeSlash'
                          data-testid='disable'
                              />
                        <p>Disable</p>
                      </StyledActionsButton>
                    ) : (
                      <StyledActionsButton
                        onClick={() => {
                          setEnableDisableRowSelected(row);
                          setConfirmationModal(true);
                        }}
                        data-testid='enable-button'
                            >
                        <IconImporter
                          size={16}
                          color={Theme.colors.dark[600]}
                          name='eye'
                          data-testid='enable'
                              />
                        <p>Enable</p>
                      </StyledActionsButton>
                    )}

                    <StyledActionsButton
                      onClick={() => actions.editProduct?.(row.id)}
                      data-testid='edit-button'
                          >
                      <IconImporter
                        size={16}
                        color={Theme.colors.dark[600]}
                        name='notePencil'
                        data-testid='notePencil'
                            />
                      <p>Edit</p>
                    </StyledActionsButton>
                    <StyledActionsButton
                      onClick={() => actions.deleteProduct?.(row.id)}
                      data-testid='delete-button'
                          >
                      <IconImporter
                        size={16}
                        color={Theme.colors.dark[600]}
                        name='trash'
                        data-testid='trash'
                            />
                      <p>Delete</p>
                    </StyledActionsButton>
                  </StyledActionsMenu>
                  )}
                </StyledTableAction>
              </StyledTableCell>
              )}
            </StyledTableRow>
          ))}
        </tbody>
      </table>

      {confirmationModal && <StyledModalOverlay />}

      {confirmationModal && (
        <StyledModalContent>
          <div className='pd-flex pd-flex-col pd-p-4 pd-gap-4'>
            <div className='pd-flex pd-justify-between pd-items-center'>
              <StyledModalBody>
                <p data-testid='confirm-message'>
                  {!enableDisableRowSelected?.status
                    ? tableConstants.ARE_YOU_SURE_YOU_WANT_TO_DISABLE_THIS_ITEM
                    : tableConstants.ARE_YOU_SURE_YOU_WANT_TO_ENABLE_THIS_ITEM}
                </p>
              </StyledModalBody>
            </div>
            <div className='pd-flex pd-gap-4 pd-justify-center'>
              <Button onClick={() => {
                actions?.enableDisableProduct?.(enableDisableRowSelected);
                setConfirmationModal(false);
              }
              }>
                {tableConstants.CONFIRM}
              </Button>
              <Button
                variant='outlined'
                onClick={() => setConfirmationModal(false)}
              >
                {tableConstants.CANCEL}
              </Button>
            </div>
          </div>
        </StyledModalContent>
      )}
    </StyledTableContainer>
  );
}
