import {
  act, fireEvent, render, screen,
} from '@testing-library/react';

import { Table, TableProps } from './Table.Component';
import { tableConstants } from './Table.constants';

const mockColumns = [
  { header: 'Name', dataKey: 'name' },
  { header: 'Age', dataKey: 'age' },
];

const mockRows = [
  {
    id: 1, name: '<PERSON>', age: 30,
  },
  {
    id: 2, name: '<PERSON>', age: 25, status: '2025-01-15 12:07:30:351 -05:00',
  },
];

const mockEditProduct = jest.fn();
const mockDeleteProduct = jest.fn();
const mockEnableDisableProduct = jest.fn();

const mockActions = {
  editProduct: mockEditProduct,
  deleteProduct: mockDeleteProduct,
  enableDisableProduct: mockEnableDisableProduct,
};

const mockProps: TableProps = {
  selectTable: true,
  columns: mockColumns,
  rows: mockRows,
  totalItems: 2,
  actions: mockActions,
  totalItemsLabel: 'Total',
};

describe('Table Component', () => {
  it('renders correct number of rows', () => {
    render(<Table {...mockProps} />);
    const rows = screen.getAllByTestId('selectRow');
    expect(rows).toHaveLength(2);
  });

  it('handles select all checkbox', async () => {
    render(<Table {...mockProps} />);
    const selectAllCheckbox = screen.getByTestId('selectAll') as HTMLInputElement;

    await act(async () => {
      fireEvent.click(selectAllCheckbox);
    });

    expect(selectAllCheckbox.checked).toBe(true);

    const rowCheckboxes = screen.getAllByTestId('selectRow') as HTMLInputElement[];
    rowCheckboxes.forEach((checkbox) => {
      expect(checkbox.checked).toBe(true);
    });

    await act(async () => {
      fireEvent.click(selectAllCheckbox);
    });

    expect(selectAllCheckbox.checked).toBe(false);
    rowCheckboxes.forEach((checkbox) => {
      expect(checkbox.checked).toBe(false);
    });
  });

  it('handles individual row selection', () => {
    render(<Table {...mockProps} />);
    const rowCheckboxes = screen.getAllByTestId('selectRow') as HTMLInputElement[];

    fireEvent.click(rowCheckboxes[0]);
    expect(rowCheckboxes[0].checked).toBe(true);
    expect(rowCheckboxes[1].checked).toBe(false);

    fireEvent.click(rowCheckboxes[1]);
    expect(rowCheckboxes[0].checked).toBe(true);
    expect(rowCheckboxes[1].checked).toBe(true);

    const selectAllCheckbox = screen.getByTestId('selectAll') as HTMLInputElement;
    expect(selectAllCheckbox.checked).toBe(true);

    fireEvent.click(rowCheckboxes[0]);
    expect(rowCheckboxes[0].checked).toBe(false);
    expect(selectAllCheckbox.checked).toBe(false);
  });

  it('handles empty rows', () => {
    const emptyProps = { ...mockProps, rows: [] };
    render(<Table {...emptyProps} />);
    expect(screen.queryByTestId('selectRow')).not.toBeInTheDocument();
  });

  it('handles single row', () => {
    const singleRowProps = { ...mockProps, rows: [mockRows[0]] };
    render(<Table {...singleRowProps} />);
    const rowCheckboxes = screen.getAllByTestId('selectRow');
    expect(rowCheckboxes).toHaveLength(1);
  });

  it('renders actions column when actions are provided', () => {
    render(<Table {...mockProps} />);
    expect(screen.getByText('Action')).toBeInTheDocument();
  });

  it('toggles action menu on click', () => {
    render(<Table {...mockProps} />);
    const actionButton = screen.getAllByTestId('dotsThree');
    const firstButton = actionButton[0];

    fireEvent.click(firstButton);
    expect(screen.getByTestId('actions-menu')).toBeInTheDocument();

    fireEvent.click(firstButton);
    expect(screen.queryByTestId('actions-menu')).not.toBeInTheDocument();
  });

  it('renders icons in action menu', () => {
    render(<Table {...mockProps} />);
    const actionButton = screen.getAllByTestId('dotsThree');
    const firstButton = actionButton[0];

    fireEvent.click(firstButton);

    const editIcon = screen.getByTestId('notePencil');
    const deleteIcon = screen.getByTestId('trash');

    expect(editIcon).toBeInTheDocument();
    expect(deleteIcon).toBeInTheDocument();
  });

  it('calls editProduct when edit button is clicked', () => {
    render(<Table {...mockProps} />);
    const actionButton = screen.getAllByTestId('dotsThree')[0];

    fireEvent.click(actionButton);
    const editButton = screen.getByTestId('notePencil').parentElement;
    fireEvent.click(editButton!);

    expect(mockEditProduct).toHaveBeenCalledWith(mockRows[0].id);
  });

  it('calls deleteProduct when delete button is clicked', () => {
    render(<Table {...mockProps} />);
    const actionButton = screen.getAllByTestId('dotsThree')[0];

    fireEvent.click(actionButton);
    const deleteButton = screen.getByTestId('trash').parentElement;
    fireEvent.click(deleteButton!);

    expect(mockDeleteProduct).toHaveBeenCalledWith(mockRows[0].id);
  });

  it('renders correctly without selectTable', () => {
    const propsWithoutSelectTable = { ...mockProps, selectTable: false };
    render(<Table {...propsWithoutSelectTable} />);
    expect(screen.queryByTestId('selectAll')).not.toBeInTheDocument();
  });

  it('renders correctly without actions', () => {
    const propsWithoutActions = { ...mockProps, actions: undefined };
    render(<Table {...propsWithoutActions} />);
    expect(screen.queryByText('Action')).not.toBeInTheDocument();
  });

  it('renders cells using renderer when provided', () => {
    const rendererMock = jest.fn((value) => <span data-testid="custom-render">{value}</span>);
    const columnsWithRenderer = [
      { header: 'Name', dataKey: 'name', renderer: rendererMock },
      { header: 'Age', dataKey: 'age' },
    ];
    const propsWithRenderer: TableProps = {
      ...mockProps,
      columns: columnsWithRenderer,
    };
    render(<Table {...propsWithRenderer} />);
    expect(rendererMock).toHaveBeenCalledWith('John Doe', undefined);
    expect(screen.getAllByTestId('custom-render')[0]).toHaveTextContent('John Doe', undefined);
  });

  it('renders cell value directly when renderer is not provided', () => {
    render(<Table {...mockProps} />);
    expect(screen.getByText('John Doe')).toBeInTheDocument();
  });

  it('renders Filter when filter prop is provided', () => {
    const filterProps = {
      filters: [
        {
          name: 'Category',
          key: 'category',
          options: [
            { id: 'option1', label: 'Option1', selected: false },
            { id: 'option2', label: 'Option2', selected: false },
          ],
        },
      ],
    };
    render(<Table {...mockProps} filter={filterProps} />);
    const filterButton = screen.getByTestId('filter-container');
    fireEvent.click(filterButton);
  });

  it('render searchbox when searchBox prop is provided', () => {
    const searchBoxProps = {
      placeholder: 'Search',
      onSearchChange: jest.fn(),
    };
    render(<Table {...mockProps} searchBox={searchBoxProps} />);
    const searchBox = screen.getByPlaceholderText('Search');
    fireEvent.change(searchBox, { target: { value: 'John' } });
    expect(searchBox).toHaveValue('John');
  });

  it('does not render Filter when filter prop is not provided', () => {
    render(<Table {...mockProps} filter={undefined} />);
    expect(screen.queryByTestId('filter-container')).not.toBeInTheDocument();
  });

  it('closes action menu when clicking outside', () => {
    render(<Table {...mockProps} />);

    const actionButton = screen.getAllByTestId('dotsThree')[0];
    fireEvent.click(actionButton);
    expect(screen.getByTestId('actions-menu')).toBeInTheDocument();

    fireEvent.mouseDown(document.body);
    expect(screen.queryByTestId('actions-menu')).not.toBeInTheDocument();
  });

  it('keeps action menu open when clicking inside', () => {
    render(<Table {...mockProps} />);

    const actionButton = screen.getAllByTestId('dotsThree')[0];
    fireEvent.click(actionButton);

    const menu = screen.getByTestId('actions-menu');
    fireEvent.mouseDown(menu);

    expect(screen.getByTestId('actions-menu')).toBeInTheDocument();
  });

  it('removes event listener on unmount', () => {
    const removeEventListenerSpy = jest.spyOn(document, 'removeEventListener');

    const { unmount } = render(<Table {...mockProps} />);
    unmount();

    expect(removeEventListenerSpy).toHaveBeenCalledWith('mousedown', expect.any(Function));
    removeEventListenerSpy.mockRestore();
  });

  it('should show the correct confirmation message based on the row status (disabled)', () => {
    render(
      <Table
        columns={mockColumns}
        rows={mockRows}
        totalItems={2}
        actions={mockActions}
      />,
    );

    const actionButton = screen.getAllByTestId('dotsThree')[0];
    fireEvent.click(actionButton);

    expect(screen.getByTestId('actions-menu')).toBeInTheDocument();

    fireEvent.click(screen.getByTestId('disable-button'));

    expect(screen.getByTestId('confirm-message')).toHaveTextContent(
      tableConstants.ARE_YOU_SURE_YOU_WANT_TO_DISABLE_THIS_ITEM,
    );
  });

  it('should show the correct confirmation message based on the row status (enabled)', () => {
    render(
      <Table
        columns={mockColumns}
        rows={mockRows}
        totalItems={2}
        actions={mockActions}
      />,
    );

    const actionButton = screen.getAllByTestId('dotsThree')[1];
    fireEvent.click(actionButton);

    expect(screen.getByTestId('actions-menu')).toBeInTheDocument();

    fireEvent.click(screen.getByTestId('enable-button'));

    expect(screen.getByTestId('confirm-message')).toHaveTextContent(
      tableConstants.ARE_YOU_SURE_YOU_WANT_TO_ENABLE_THIS_ITEM,
    );
  });

  it('should close the confirmation modal when cancel button is clicked', () => {
    render(
      <Table
        columns={mockColumns}
        rows={mockRows}
        totalItems={2}
        actions={mockActions}
      />,
    );

    const actionButton = screen.getAllByTestId('dotsThree')[0];
    fireEvent.click(actionButton);

    expect(screen.getByTestId('actions-menu')).toBeInTheDocument();

    fireEvent.click(screen.getByTestId('disable-button'));

    expect(screen.getByTestId('confirm-message')).toBeInTheDocument();

    const cancelButton = screen.getByText(tableConstants.CANCEL);
    fireEvent.click(cancelButton);

    expect(screen.queryByTestId('confirm-message')).not.toBeInTheDocument();
  });

  it('should call enableDisableProduct and close the modal when confirm button is clicked', () => {
    render(
      <Table
        columns={mockColumns}
        rows={mockRows}
        totalItems={2}
        actions={{ enableDisableProduct: mockEnableDisableProduct }}
      />,
    );

    const actionButton = screen.getAllByTestId('dotsThree')[0];
    fireEvent.click(actionButton);

    expect(screen.getByTestId('actions-menu')).toBeInTheDocument();

    fireEvent.click(screen.getByTestId('disable-button'));

    expect(screen.getByTestId('confirm-message')).toBeInTheDocument();

    const confirmButton = screen.getByText(tableConstants.CONFIRM);
    fireEvent.click(confirmButton);

    expect(mockEnableDisableProduct).toHaveBeenCalledWith(mockRows[0]);

    expect(screen.queryByTestId('confirm-message')).not.toBeInTheDocument();
  });

  it('renders no rows by default if none provided', () => {
    render(<Table columns={[{ header: 'Name', dataKey: 'name' }]} totalItems={0} />);
    const tableRows = screen.queryAllByRole('row');
    // The first row is the header, so we expect only one
    expect(tableRows).toHaveLength(1);
  });
});
