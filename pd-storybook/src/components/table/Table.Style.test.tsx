import {
  fireEvent, render, screen, waitFor,
} from '@testing-library/react';

import { Table, TableProps } from './Table.Component';

jest.mock('../iconImporter/IconImporter.Component', () => ({
  IconImporter: () => <div data-testid="mocked-icon" />,
}));

const mockColumns = [
  { header: 'Name', dataKey: 'name' },
  { header: 'Age', dataKey: 'age' },
  { header: 'Actions', dataKey: 'actions' },
];

const mockRows = [
  { id: 1, name: '<PERSON>', age: 30 },
  { id: 2, name: '<PERSON>', age: 25 },
];

const defaultProps: TableProps = {
  columns: mockColumns,
  rows: mockRows,
  totalItems: 2,
  selectTable: true,
};

const mockActions = {
  enableDisableProduct: jest.fn(),
  editProduct: jest.fn(),
  deleteProduct: jest.fn(),
};

describe('Table Component Snapshots', () => {
  it('renders correctly with default props', () => {
    const { container } = render(<Table {...defaultProps} />);
    expect(container).toMatchSnapshot();
  });

  it('renders correctly with empty rows', () => {
    const props = { ...defaultProps, rows: [], totalItems: 0 };
    const { container } = render(<Table {...props} />);
    expect(container).toMatchSnapshot();
  });

  it('renders correctly with single row', () => {
    const props = { ...defaultProps, rows: [mockRows[0]], totalItems: 1 };
    const { container } = render(<Table {...props} />);
    expect(container).toMatchSnapshot();
  });

  it('renders correctly with many columns', () => {
    const manyColumns = [
      ...mockColumns,
      { header: 'Email', dataKey: 'email' },
      { header: 'Phone', dataKey: 'phone' },
    ];
    const manyColumnRows = [
      {
        id: 1,
        name: 'John Doe',
        age: 30,
        email: '<EMAIL>',
        phone: '1234567890',
      },
      {
        id: 2,
        name: 'Jane Doe',
        age: 25,
        email: '<EMAIL>',
        phone: '0987654321',
      },
    ];
    const props = {
      ...defaultProps,
      columns: manyColumns,
      rows: manyColumnRows,
    };
    const { container } = render(<Table {...props} />);
    expect(container).toMatchSnapshot();
  });

  it('renders correctly with boolean values', () => {
    const columnsWithBoolean = [
      ...mockColumns,
      { header: 'Active', dataKey: 'active' },
    ];
    const rowsWithBoolean = [
      {
        id: 1, name: 'John Doe', age: 30, active: true,
      },
      {
        id: 2, name: 'Jane Doe', age: 25, active: false,
      },
    ];
    const props = {
      ...defaultProps,
      columns: columnsWithBoolean,
      rows: rowsWithBoolean,
    };
    const { container } = render(<Table {...props} />);
    expect(container).toMatchSnapshot();
  });

  it('should render StyledModalBody with centered text by default', async () => {
    render(

      <Table
        columns={mockColumns}
        rows={mockRows}
        totalItems={mockRows.length}
        actions={mockActions}
        />,

    );

    const actionButton = screen.getAllByTestId('mocked-icon')[0];

    expect(actionButton).toBeInTheDocument();

    fireEvent.click(actionButton);

    await waitFor(() => screen.getByTestId('actions-menu'));

    const disableButton = screen.getByTestId('disable-button');
    fireEvent.click(disableButton);

    const modalBody = screen.getByTestId('confirm-message');

    expect(modalBody).toBeInTheDocument();
    expect(modalBody).toHaveTextContent('Estas seguro que quieres deshabilitar este elemento?');

    const modalBodyParagraph = modalBody.closest('div > p');
    expect(modalBodyParagraph).toHaveStyle('align-self: center');
    expect(modalBodyParagraph).toHaveStyle('text-align: center');
  });
});
