import {
  AddressBook,
  AirplaneIn<PERSON>light,
  Armchair,
  ArrowElbowRightDown,
  ArrowUp,
  ArrowUUpLeft,
  Bell,
  Brain,
  Buildings,
  Calendar,
  Cardholder,
  CaretDown,
  CaretLeft,
  CaretLineLeft,
  CaretLineRight,
  CaretRight,
  CaretUp,
  Chat,
  Check,
  CheckCircle,
  CircleNotch,
  CirclesThreePlus,
  ClockCounterClockwise,
  DotsThree,
  Empty,
  ExclamationMark,
  Eye,
  EyeSlash,
  Factory,
  FloppyDisk,
  FunnelSimple,
  Gear,
  Heart,
  Image,
  Info,
  ListChecks,
  MagnifyingGlass,
  Minus,
  NotePencil,
  Package,
  Paperclip,
  PaperPlaneRight,
  Pencil,
  PencilSimple,
  Plus,
  Question,
  SealQuestion,
  SealWarning,
  ShoppingCart,
  Stack,
  Star,
  Storefront,
  Tag,
  Trash,
  User,
  UserCircle,
  UserMinus,
  Users,
  VideoConference,
  Wallet,
  WarningCircle,
  WhatsappLogo,
  X,
  XCircle,
} from '@phosphor-icons/react';

import { getIcon, IconName } from './IconMap.Component';

describe('IconMap Component', () => {
  it('should return the correct icon for each valid icon name', () => {
    const iconMap: Record<IconName, React.ComponentType> = {
      airplane: AirplaneInFlight,
      armchair: Armchair,
      buildings: Buildings,
      card: Cardholder,
      clock: ClockCounterClockwise,
      warning: SealWarning,
      video: VideoConference,
      caretRight: CaretRight,
      bell: Bell,
      userCircle: UserCircle,
      caretDown: CaretDown,
      search: MagnifyingGlass,
      caretLeft: CaretLeft,
      caretLineLeft: CaretLineLeft,
      caretLineRight: CaretLineRight,
      funnelSimple: FunnelSimple,
      xCircle: XCircle,
      x: X,
      warningCircle: WarningCircle,
      factory: Factory,
      addressBook: AddressBook,
      stack: Stack,
      checkCircle: CheckCircle,
      exclamationMark: ExclamationMark,
      dotsThree: DotsThree,
      trash: Trash,
      notePencil: NotePencil,
      info: Info,
      question: Question,
      image: Image,
      empty: Empty,
      arrowElbowRightDown: ArrowElbowRightDown,
      plus: Plus,
      arrowUUpLeft: ArrowUUpLeft,
      circlesThreePlus: CirclesThreePlus,
      check: Check,
      package: Package,
      eyeSlash: EyeSlash,
      eye: Eye,
      sealQuestion: SealQuestion,
      pencil: Pencil,
      floppyDisk: FloppyDisk,
      pencilSimple: PencilSimple,
      calendar: Calendar,
      whatsApp: WhatsappLogo,
      shoppingCart: ShoppingCart,
      listChecks: ListChecks,
      storefront: Storefront,
      minus: Minus,
      heart: Heart,
      star: Star,
      tag: Tag,
      circleNotch: CircleNotch,
      brain: Brain,
      arrowUp: ArrowUp,
      chat: Chat,
      paperclip: Paperclip,
      paperPlaneRight: PaperPlaneRight,
      caretUp: CaretUp,
      gear: Gear,
      user: User,
      userMinus: UserMinus,
      users: Users,
      wallet: Wallet,
    };

    Object.keys(iconMap).forEach((iconName) => {
      const icon = getIcon(iconName as IconName);
      expect(icon).toBe(iconMap[iconName as IconName]);
    });
  });

  it('should return the default icon for an invalid icon name', () => {
    const icon = getIcon('invalid' as IconName);
    expect(icon).toBe(SealWarning);
  });
});
