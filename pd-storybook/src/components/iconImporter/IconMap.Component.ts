import {
  AddressBook,
  AirplaneInFlight, Armchair,
  ArrowElbowRightDown,
  ArrowUp,
  ArrowUUpLeft,
  Bell,
  Brain,
  Buildings,
  Calendar,
  Cardholder,
  CaretDown,
  CaretLeft,
  CaretLineLeft,
  CaretLineRight,
  CaretRight,
  CaretUp,
  Chat,
  Check,
  CheckCircle,
  CircleNotch,
  CirclesThreePlus,
  ClockCounterClockwise,
  DotsThree,
  Empty,
  ExclamationMark,
  Eye,
  EyeSlash,
  Factory,
  FloppyDisk,
  FunnelSimple,
  Gear,
  Heart,
  Image,
  Info,
  ListChecks,
  MagnifyingGlass,
  Minus,
  NotePencil,
  Package,
  Paperclip,
  PaperPlaneRight,
  Pencil,
  PencilSimple,
  Icon as PhosphorIcon,
  Plus,
  Question,
  SealQuestion,
  SealWarning,
  ShoppingCart,
  Stack,
  Star,
  Storefront,
  Tag,
  Trash,
  User,
  UserCircle,
  UserMinus,
  Users,
  VideoConference,
  Wallet,
  WarningCircle,
  WhatsappLogo,
  X,
  XCircle,
} from '@phosphor-icons/react';

export type IconName =
  | 'airplane'
  | 'armchair'
  | 'buildings'
  | 'card'
  | 'clock'
  | 'warning'
  | 'video'
  | 'caretRight'
  | 'bell'
  | 'userCircle'
  | 'caretDown'
  | 'search'
  | 'caretLeft'
  | 'caretLineLeft'
  | 'caretLineRight'
  | 'funnelSimple'
  | 'xCircle'
  | 'x'
  | 'warningCircle'
  | 'factory'
  | 'addressBook'
  | 'stack'
  | 'checkCircle'
  | 'exclamationMark'
  | 'dotsThree'
  | 'notePencil'
  | 'trash'
  | 'info'
  | 'question'
  | 'image'
  | 'empty'
  | 'arrowElbowRightDown'
  | 'plus'
  | 'arrowUUpLeft'
  | 'gear'
  | 'circlesThreePlus'
  | 'check'
  | 'package'
  | 'eyeSlash'
  | 'eye'
  | 'sealQuestion'
  | 'floppyDisk'
  | 'package'
  | 'pencil'
  | 'pencilSimple'
  | 'calendar'
  | 'whatsApp'
  | 'shoppingCart'
  | 'listChecks'
  | 'storefront'
  | 'minus'
  | 'heart'
  | 'star'
  | 'circleNotch'
  | 'tag'
  | 'paperclip'
  | 'brain'
  | 'chat'
  | 'paperPlaneRight'
  | 'arrowUp'
  | 'caretUp'
  | 'user'
  | 'userMinus'
  | 'users'
  | 'wallet';

export const iconMap: Record<IconName, PhosphorIcon> = {
  airplane: AirplaneInFlight,
  armchair: Armchair,
  buildings: Buildings,
  card: Cardholder,
  clock: ClockCounterClockwise,
  warning: SealWarning,
  video: VideoConference,
  caretRight: CaretRight,
  bell: Bell,
  userCircle: UserCircle,
  caretDown: CaretDown,
  search: MagnifyingGlass,
  caretLeft: CaretLeft,
  caretLineLeft: CaretLineLeft,
  caretLineRight: CaretLineRight,
  funnelSimple: FunnelSimple,
  xCircle: XCircle,
  x: X,
  warningCircle: WarningCircle,
  factory: Factory,
  addressBook: AddressBook,
  stack: Stack,
  checkCircle: CheckCircle,
  exclamationMark: ExclamationMark,
  dotsThree: DotsThree,
  notePencil: NotePencil,
  trash: Trash,
  info: Info,
  question: Question,
  image: Image,
  empty: Empty,
  arrowElbowRightDown: ArrowElbowRightDown,
  plus: Plus,
  arrowUUpLeft: ArrowUUpLeft,
  circlesThreePlus: CirclesThreePlus,
  check: Check,
  package: Package,
  eyeSlash: EyeSlash,
  eye: Eye,
  sealQuestion: SealQuestion,
  pencil: Pencil,
  floppyDisk: FloppyDisk,
  pencilSimple: PencilSimple,
  calendar: Calendar,
  whatsApp: WhatsappLogo,
  shoppingCart: ShoppingCart,
  listChecks: ListChecks,
  storefront: Storefront,
  minus: Minus,
  heart: Heart,
  star: Star,
  tag: Tag,
  circleNotch: CircleNotch,
  brain: Brain,
  paperclip: Paperclip,
  chat: Chat,
  paperPlaneRight: PaperPlaneRight,
  arrowUp: ArrowUp,
  caretUp: CaretUp,
  gear: Gear,
  wallet: Wallet,
  user: User,
  users: Users,
  userMinus: UserMinus,
};

export const getIcon = (name: IconName): PhosphorIcon => iconMap[name] || SealWarning;
