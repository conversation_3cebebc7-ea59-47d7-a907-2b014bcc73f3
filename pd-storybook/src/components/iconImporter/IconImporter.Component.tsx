import { IconProps } from '@phosphor-icons/react';
import React from 'react';

import { getIcon, IconName } from './IconMap.Component';

export interface IconImporterProps extends Omit<IconProps, 'ref'> {
  name: IconName;
}

export const IconImporter = React.forwardRef<SVGSVGElement, IconImporterProps>(
  ({ name, ...props }, ref) => {
    const Icon = getIcon(name);
    return <Icon ref={ref} data-testid={name} {...props} />;
  },
);
IconImporter.displayName = 'IconImporter';
