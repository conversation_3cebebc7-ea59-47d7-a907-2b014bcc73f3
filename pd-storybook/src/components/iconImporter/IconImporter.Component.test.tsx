import { render } from '@testing-library/react';

import { IconImporter, IconImporterProps } from './IconImporter.Component';
import { getIcon } from './IconMap.Component';

jest.mock('./IconMap.Component', () => ({
  getIcon: jest.fn(),
}));

describe('IconImporter Component', () => {
  const defaultProps: IconImporterProps = {
    name: 'airplane',
    size: 24,
    color: 'black',
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render the correct icon based on the name prop', () => {
    const MockIcon = (props: IconImporterProps) => <svg {...props} />;
    (getIcon as jest.Mock).mockReturnValue(MockIcon);

    const { getByTestId } = render(<IconImporter data-testid={defaultProps.name} {...defaultProps} />);

    expect(getIcon).toHaveBeenCalledWith('airplane');
    expect(getByTestId('airplane')).toBeInTheDocument();
  });

  it('should pass the correct props to the icon component', () => {
    const MockIcon = (props: IconImporterProps) => <svg {...props} />;
    (getIcon as jest.Mock).mockReturnValue(MockIcon);

    const { getByTestId } = render(<IconImporter data-testid={defaultProps.name} {...defaultProps} />);

    const iconElement = getByTestId('airplane');
    expect(iconElement).toHaveAttribute('size', '24');
    expect(iconElement).toHaveAttribute('color', 'black');
  });
});
