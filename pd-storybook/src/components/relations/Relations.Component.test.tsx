import { fireEvent, render, screen } from '@testing-library/react';

import '@testing-library/jest-dom';
import { RelationsComponent, RelationsComponentProps } from './Relations.Component';

// Mock dependencies
jest.mock('../iconImporter/IconImporter.Component', () => ({
  IconImporter: ({ name, onClick }: { name: string; onClick?: () => void }) => (
    <div data-testid={`icon-${name}`} onClick={onClick} />
  ),
}));

const mockRelatedItems = [
  { id: '1', title: 'Item 1', onRemoveItem: jest.fn() },
  { id: '2', title: 'Item 2', onRemoveItem: jest.fn() },
];

const defaultProps: RelationsComponentProps = {
  header: {
    title: 'Test Header',
  },
  mode: 'showMode',
  relatedItems: mockRelatedItems,
  subTitle: 'Test Subtitle',
  togleModeButton: jest.fn(),
};

describe('RelationsComponent', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders correctly in show mode', () => {
    render(<RelationsComponent {...defaultProps} />);

    expect(screen.getByText('Test Header')).toBeInTheDocument();
    expect(screen.getByText('Test Subtitle')).toBeInTheDocument();
    expect(screen.getByTestId('icon-pencil')).toBeInTheDocument();
    expect(screen.getByText('Item 1')).toBeInTheDocument();
    expect(screen.getByText('Item 2')).toBeInTheDocument();
  });

  it('renders correctly in edit mode', () => {
    render(<RelationsComponent {...defaultProps} mode="editMode" />);

    expect(screen.getByTestId('icon-floppyDisk')).toBeInTheDocument();
    expect(screen.getAllByTestId('icon-x')).toHaveLength(2);
  });

  it('displays no items message when there are no related items', () => {
    render(<RelationsComponent {...defaultProps} relatedItems={[]} />);

    expect(screen.getByText('No items to show')).toBeInTheDocument();
  });

  it('displays custom no items message when provided', () => {
    render(
      <RelationsComponent
        {...defaultProps}
        relatedItems={[]}
        noItemsLabel="Custom empty message"
      />,
    );

    expect(screen.getByText('Custom empty message')).toBeInTheDocument();
  });

  it('calls togleModeButton when mode toggle icon is clicked', () => {
    render(<RelationsComponent {...defaultProps} />);

    fireEvent.click(screen.getByTestId('icon-pencil'));
    expect(defaultProps.togleModeButton).toHaveBeenCalled();
  });

  it('calls onRemoveItem when remove icon is clicked in edit mode', () => {
    render(<RelationsComponent {...defaultProps} mode="editMode" />);

    const removeButtons = screen.getAllByTestId('icon-x');
    fireEvent.click(removeButtons[0]);

    expect(mockRelatedItems[0].onRemoveItem).toHaveBeenCalled();
  });

  it('renders custom renderer when provided', () => {
    const customRenderer = () => <div data-testid="custom-renderer">Custom Content</div>;
    const itemsWithRenderer = [
      { ...mockRelatedItems[0], renderer: customRenderer },
    ];

    render(
      <RelationsComponent
        {...defaultProps}
        relatedItems={itemsWithRenderer}
      />,
    );

    expect(screen.getByTestId('custom-renderer')).toBeInTheDocument();
  });

  it('renders without subtitle when not provided', () => {
    const { rerender } = render(<RelationsComponent {...defaultProps} />);
    expect(screen.getByText('Test Subtitle')).toBeInTheDocument();

    rerender(<RelationsComponent {...defaultProps} subTitle={undefined} />);
    expect(screen.queryByText('Test Subtitle')).not.toBeInTheDocument();
  });

  it('uses empty array as default value for relatedItems when not provided', () => {
    const propsWithoutRelatedItems = {
      ...defaultProps,
      relatedItems: undefined,
    };

    render(<RelationsComponent {...propsWithoutRelatedItems} />);

    expect(screen.getByText('No items to show')).toBeInTheDocument();
    expect(screen.queryByRole('listitem')).not.toBeInTheDocument();
  });
});
