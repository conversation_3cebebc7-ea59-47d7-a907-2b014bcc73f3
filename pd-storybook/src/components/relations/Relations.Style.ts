import styled from '@emotion/styled';

import Theme from '../../configurations/Theme.Configuration';

export const RelationsSyledContainer = styled.div<{ disabled?: boolean }>`
  background-color: ${Theme.colors.white};
  border-radius: ${Theme.borderRadius.xxl};
  box-shadow: ${Theme.shadow.lg};
  width: 100%;
  min-height: 422px;
  display: flex;
  flex-direction: column;
  gap: 16px;

  ${(props) => props.disabled && `
    background - color: ${Theme.colors.lightGray};
    color: ${Theme.colors.dark[400]};
    pointer-events: none;
    user-select: none;
    cursor: not-allowed;
    opacity: 0.5;
    `}
`;

export const StyledHeaderContainer = styled.div`
    display: flex;
    flex-direction: column;
    gap: 12px;
    padding: 32px 32px 0;
`;

export const StyledSearchBoxContainer = styled.div`
    display: flex;
    justify-content: space-between;
`;

export const StyledBodyContainer = styled.div`
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding-bottom: 16px;
`;

export const StyledResultsContainer = styled.div`
    max-height: 240px;
    min-height: 240px;
    overflow-y: auto;
`;

export const NoResultsLabel = styled.div` 
    font-size: ${Theme.fontSize.sm}
    color: ${Theme.colors.dark[500]};
    font-style: italic;
    padding: 0 32px;
`;

export const StyledResult = styled.div<{disabled: boolean}>`
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 32px;
    height: 48px;
    width: 100%;
    border-radius: ${Theme.borderRadius.xl};
    transition: background-color 0.3s;
    cursor: default;

    ${(props) => props.disabled && `
        &:hover {
            background-color: ${Theme.colors.dark[200]};
+        }
    `}
`;
