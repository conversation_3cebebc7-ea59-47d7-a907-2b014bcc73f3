import { FormLabel, FormLabelProps } from '../form/FormLabel.Component';
import { IconImporter } from '../iconImporter/IconImporter.Component';
import { Title } from '../title/Title.Component';

import {
  NoResultsLabel,
  RelationsSyledContainer, StyledBodyContainer, StyledHeaderContainer,
  StyledResult,
  StyledResultsContainer,
  StyledSearchBoxContainer,
} from './Relations.Style';

const NO_ITEMS_LABEL = 'No items to show';

export type RelatedItemRenderer = (value: unknown) => React.ReactNode;

export interface RelatedItem {
  id: string;
  title: string;
  onRemoveItem?: () => void;
  renderer?: RelatedItemRenderer;
}

export type ComponentModes = 'showMode' | 'editMode' | 'disabledMode'

export interface RelationsComponentProps {
  header: FormLabelProps;
  subTitle?: string;
  noItemsLabel?: string;
  relatedItems?: RelatedItem[];
  togleModeButton?: () => void;
  mode: ComponentModes;
  className?: string;
  children?: React.ReactNode;
}

const RenderRelatedItem = (item: RelatedItem) => (
  <>
    {item.renderer ? item.renderer(item) : <Title as='p' size='sm' weight='regular'>{item.title}</Title>}
  </>
);

export const RelationsComponent = ({
  header,
  subTitle,
  relatedItems = [],
  noItemsLabel,
  togleModeButton,
  mode = 'showMode',
  className,
  children,
}: RelationsComponentProps) => (
  <RelationsSyledContainer
    disabled={mode === 'disabledMode'}
    className={className}
  >
    <StyledHeaderContainer>
      <StyledSearchBoxContainer>
        <FormLabel {...header}/>
        <IconImporter
          name={mode === 'editMode' ? 'floppyDisk' : 'pencil'}
          // eslint-disable-next-line max-len
          className={`pd-rounded-full pd-border pd-p-1 pd-cursor-pointer pd-transition-all pd-ease-in-out ${mode === 'showMode' && 'pd-text-dark-500 hover:pd-text-dark-700 pd-border pd-border-dark-400'} ${mode === 'editMode' && 'pd-text-white pd-bg-primary pd-border-primary hover:pd-text-primary hover:pd-bg-white'}`}
          size={24}
          onClick={togleModeButton}
          />
      </StyledSearchBoxContainer>
      {children}
    </StyledHeaderContainer>
    <StyledBodyContainer>
      {subTitle && <Title as='h6' size='base' weight='medium' className='pd-px-8'>{subTitle}</Title>}
      <StyledResultsContainer>
        {relatedItems.length === 0 && <NoResultsLabel>{noItemsLabel || NO_ITEMS_LABEL}</NoResultsLabel>}
        {relatedItems.map((item) => (
          <StyledResult
            key={item.id}
            disabled={mode === 'editMode'}
          >
            <RenderRelatedItem {...item}/>
            {mode === 'editMode' && <IconImporter name='x' className='pd-cursor-pointer' onClick={item.onRemoveItem} />
            }
          </StyledResult>
        ))}
      </StyledResultsContainer>
    </StyledBodyContainer>
  </RelationsSyledContainer>
);
