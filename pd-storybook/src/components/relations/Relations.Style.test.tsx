import { render } from '@testing-library/react';

import '@testing-library/jest-dom';
import {
  NoResultsLabel,
  RelationsSyledContainer,
  StyledBodyContainer,
  StyledHeaderContainer,
  StyledResult,
  StyledResultsContainer,
  StyledSearchBoxContainer,
} from './Relations.Style';

describe('Relations Styled Components', () => {
  it('RelationsSyledContainer renders correctly', () => {
    const { container } = render(<RelationsSyledContainer />);
    expect(container.firstChild).toMatchSnapshot();
  });

  it('RelationsSyledContainer renders correctly when disabled', () => {
    const { container } = render(<RelationsSyledContainer disabled />);
    expect(container.firstChild).toMatchSnapshot();
  });

  it('StyledHeaderContainer renders correctly', () => {
    const { container } = render(<StyledHeaderContainer />);
    expect(container.firstChild).toMatchSnapshot();
  });

  it('StyledSearchBoxContainer renders correctly', () => {
    const { container } = render(<StyledSearchBoxContainer />);
    expect(container.firstChild).toMatchSnapshot();
  });

  it('StyledBodyContainer renders correctly', () => {
    const { container } = render(<StyledBodyContainer />);
    expect(container.firstChild).toMatchSnapshot();
  });

  it('StyledResultsContainer renders correctly', () => {
    const { container } = render(<StyledResultsContainer />);
    expect(container.firstChild).toMatchSnapshot();
  });

  it('NoResultsLabel renders correctly', () => {
    const { container } = render(<NoResultsLabel />);
    expect(container.firstChild).toMatchSnapshot();
  });

  it('StyledResult renders correctly', () => {
    const { container } = render(<StyledResult disabled={false} />);
    expect(container.firstChild).toMatchSnapshot();
  });

  it('StyledResult renders correctly when disabled', () => {
    const { container } = render(<StyledResult disabled={true} />);
    expect(container.firstChild).toMatchSnapshot();
  });
});
