// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Relations Styled Components NoResultsLabel renders correctly 1`] = `
.emotion-0 {
  font-size: 14px;
  line-height: 20px;
  color: #6B6E75;
  font-style: italic;
  padding: 0 32px;
}

<div
  class="emotion-0"
/>
`;

exports[`Relations Styled Components RelationsSyledContainer renders correctly 1`] = `
.emotion-0 {
  background-color: #FFFFFF;
  border-radius: 16px;
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1),0 4px 6px -4px rgb(0 0 0 / 0.1);
  width: 100%;
  min-height: 422px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: 16px;
}

<div
  class="emotion-0"
/>
`;

exports[`Relations Styled Components RelationsSyledContainer renders correctly when disabled 1`] = `
.emotion-0 {
  background-color: #FFFFFF;
  border-radius: 16px;
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1),0 4px 6px -4px rgb(0 0 0 / 0.1);
  width: 100%;
  min-height: 422px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: 16px;
  background - color: #FAFAFA;
  color: #A3A5AB;
  pointer-events: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  cursor: not-allowed;
  opacity: 0.5;
}

<div
  class="emotion-0"
  disabled=""
/>
`;

exports[`Relations Styled Components StyledBodyContainer renders correctly 1`] = `
.emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: 8px;
  padding-bottom: 16px;
}

<div
  class="emotion-0"
/>
`;

exports[`Relations Styled Components StyledHeaderContainer renders correctly 1`] = `
.emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: 12px;
  padding: 32px 32px 0;
}

<div
  class="emotion-0"
/>
`;

exports[`Relations Styled Components StyledResult renders correctly 1`] = `
.emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: 0 32px;
  height: 48px;
  width: 100%;
  border-radius: 12px;
  -webkit-transition: background-color 0.3s;
  transition: background-color 0.3s;
  cursor: default;
}

<div
  class="emotion-0"
/>
`;

exports[`Relations Styled Components StyledResult renders correctly when disabled 1`] = `
.emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: 0 32px;
  height: 48px;
  width: 100%;
  border-radius: 12px;
  -webkit-transition: background-color 0.3s;
  transition: background-color 0.3s;
  cursor: default;
}

.emotion-0:hover {
  background-color: #F6F7FA;
}

<div
  class="emotion-0"
  disabled=""
/>
`;

exports[`Relations Styled Components StyledResultsContainer renders correctly 1`] = `
.emotion-0 {
  max-height: 240px;
  min-height: 240px;
  overflow-y: auto;
}

<div
  class="emotion-0"
/>
`;

exports[`Relations Styled Components StyledSearchBoxContainer renders correctly 1`] = `
.emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
}

<div
  class="emotion-0"
/>
`;
