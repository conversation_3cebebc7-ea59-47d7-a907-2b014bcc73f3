import { fireEvent, render, screen } from '@testing-library/react';

import { EditableHierarchy, EditableHierarchyComponent } from './EditableHierarchy.Component';
import { hierarchyConstants } from './Hierarchy.Constants';

describe('EditableHierarchyComponent', () => {
  const mockOnChange = jest.fn();

  beforeEach(() => {
    mockOnChange.mockClear();
  });

  const defaultProps = {
    hierarchies: [] as EditableHierarchy[],
    labelProps: { title: 'Test Label' },
    onChange: mockOnChange,
  };

  it('renders correctly with default props', () => {
    render(<EditableHierarchyComponent {...defaultProps} />);
    expect(screen.getByText('Test Label')).toBeInTheDocument();
    expect(screen.getByText(hierarchyConstants.EMPTY_HIERARCHY_LABEL)).toBeInTheDocument();
  });

  it('renders hierarchies correctly', () => {
    const hierarchies = [
      { id: '1', name: 'Parent', hierarchies: [{ id: '2', name: 'Child' }] },
    ];
    render(<EditableHierarchyComponent {...defaultProps} hierarchies={hierarchies} />);
    expect(screen.getByDisplayValue('Parent')).toBeInTheDocument();
    expect(screen.getByDisplayValue('Child')).toBeInTheDocument();
  });

  it('renders nested hierarchies correctly', () => {
    const hierarchies = [
      { id: '1', name: 'Parent', hierarchies: [{ id: '2', name: 'Child', hierarchies: [{ id: '3', name: 'Grandchild' }] }] },
    ];
    render(<EditableHierarchyComponent {...defaultProps} hierarchies={hierarchies} />);
    expect(screen.getByDisplayValue('Parent')).toBeInTheDocument();
    expect(screen.getByDisplayValue('Child')).toBeInTheDocument();
    expect(screen.getByDisplayValue('Grandchild')).toBeInTheDocument();
  });

  it('calls onChange when hierarchy name changes', () => {
    const hierarchies = [
      { id: '1', name: 'Parent' },
    ];
    render(<EditableHierarchyComponent {...defaultProps} hierarchies={hierarchies} />);
    const input = screen.getByDisplayValue('Parent');
    fireEvent.change(input, { target: { value: 'New Parent' } });
    expect(mockOnChange).toHaveBeenCalled();
  });

  it('calls onAdd when add button is clicked', () => {
    const hierarchies = [
      { id: '1', name: 'Parent' },
    ];
    render(<EditableHierarchyComponent {...defaultProps} hierarchies={hierarchies} />);
    fireEvent.click(screen.getByTestId('addRootButton'));
    expect(mockOnChange).toHaveBeenCalled();
  });

  it('calls onDelete when delete button is clicked', () => {
    const mockOnDelete = jest.fn();
    const hierarchies = [
      { id: '1', name: 'Parent', onDelete: mockOnDelete },
    ];
    render(<EditableHierarchyComponent {...defaultProps} hierarchies={hierarchies} />);
    fireEvent.mouseEnter(screen.getByDisplayValue('Parent'));
    fireEvent.click(screen.getByTestId('delete-button'));
    expect(mockOnDelete).toHaveBeenCalled();
  });

  it('removes hierarchy from the list and calls onChange when delete button is clicked', () => {
    const hierarchies = [
      { id: '1', name: 'Parent' },
      { id: '2', name: 'Child' },
    ];
    render(<EditableHierarchyComponent {...defaultProps} hierarchies={hierarchies} />);
    fireEvent.mouseEnter(screen.getByDisplayValue('Parent'));
    fireEvent.click(screen.getByTestId('delete-button'));
    expect(screen.queryByDisplayValue('Parent')).not.toBeInTheDocument();
    expect(mockOnChange).toHaveBeenCalled();
  });

  it('calls onAdd when hierarchy onAdd is defined', () => {
    const mockOnAdd = jest.fn();
    const hierarchies = [
      { id: '1', name: 'Parent', onAdd: mockOnAdd },
    ];
    render(<EditableHierarchyComponent {...defaultProps} hierarchies={hierarchies} />);
    fireEvent.mouseEnter(screen.getByDisplayValue('Parent'));
    fireEvent.click(screen.getByTestId('add-button'));
    expect(mockOnAdd).toHaveBeenCalled();
  });

  it('adds a new hierarchy when onAdd is not defined', () => {
    const hierarchies = [
      { id: '1', name: 'Parent', hierarchies: [] },
    ];
    render(<EditableHierarchyComponent {...defaultProps} hierarchies={hierarchies} />);
    fireEvent.mouseEnter(screen.getByDisplayValue('Parent'));
    fireEvent.click(screen.getByTestId('add-button'));
    expect(screen.getAllByDisplayValue('')).toHaveLength(1);
    expect(mockOnChange).toHaveBeenCalled();
  });

  it('initializes newParent.hierarchies as an empty array when onAdd is not defined', () => {
    const hierarchies = [
      { id: '1', name: 'Parent' },
    ];
    render(<EditableHierarchyComponent {...defaultProps} hierarchies={hierarchies} />);
    fireEvent.mouseEnter(screen.getByDisplayValue('Parent'));
    fireEvent.click(screen.getByTestId('add-button'));
    expect(mockOnChange).toHaveBeenCalled();
  });

  it('renders hierarchy.child with correct properties', () => {
    const ChildComponent = ({ hierarchy }: { hierarchy: EditableHierarchy }) => (
      <div data-testid="child-component">{hierarchy.name}</div>
    );

    const hierarchies = [
      {
        id: '1', name: 'Parent', child: ChildComponent, properties: { customProp: 'customValue' },
      },
    ];

    render(<EditableHierarchyComponent {...defaultProps} hierarchies={hierarchies} />);
    expect(screen.getByTestId('child-component')).toBeInTheDocument();
    expect(screen.getByTestId('child-component')).toHaveTextContent('Parent');
  });

  it('does not call onDelete when disableDelete is true', () => {
    const mockOnDelete = jest.fn();
    const hierarchies = [
      {
        id: '1', name: 'Parent', onDelete: mockOnDelete, disableDelete: true,
      },
    ];
    render(<EditableHierarchyComponent {...defaultProps} hierarchies={hierarchies} />);
    fireEvent.mouseEnter(screen.getByDisplayValue('Parent'));
    expect(mockOnDelete).not.toHaveBeenCalled();
  });

  it('does not call onAdd when disableAdd is true', () => {
    const mockOnAdd = jest.fn();
    const hierarchies = [
      {
        id: '1', name: 'Parent', onAdd: mockOnAdd, disableAdd: true,
      },
    ];
    render(<EditableHierarchyComponent {...defaultProps} hierarchies={hierarchies} />);
    fireEvent.mouseEnter(screen.getByDisplayValue('Parent'));
    fireEvent.click(screen.getByTestId('addRootButton'));
    expect(mockOnAdd).not.toHaveBeenCalled();
  });

  it('calls onRootAdd when provided instead of creating a new hierarchy', () => {
    const mockOnRootAdd = jest.fn();
    const mockOnChangeT2 = jest.fn();

    render(<EditableHierarchyComponent
      hierarchies={[]}
      labelProps={{ title: 'Test Label' }}
      onChange={mockOnChangeT2}
      onRootAdd={mockOnRootAdd}
    />);

    fireEvent.click(screen.getByTestId('addRootButton'));

    expect(mockOnRootAdd).toHaveBeenCalled();
    expect(mockOnChangeT2).not.toHaveBeenCalled();
  });
});
