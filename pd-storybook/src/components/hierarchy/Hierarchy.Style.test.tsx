import { render } from '@testing-library/react';

import {
  StyledEditableHierarchy,
  StyledEditableHierarchyWrapper,
  StyledEditableValuesContainer,
  StyledEditionBoxContainer,
  StyledFlexCenterContainer,
  StyledFlexColumnGap2,
  StyledHeader,
  StyledHierarchy,
  StyledHierarchyContainer,
  StyledHierarchyInput,
  StyledHierarchyList,
  StyledHierarchyWrapper,
  StyledIconButton,
  StyledModalBody,
  StyledModalCloseButton,
  StyledModalContent,
  StyledModalOverlay,
  StyledTextSmall,
  StyledTitle,
  StyledValueContainer,
  StyledValuesContainer,
  ValidationErrorItem,
  ValidationErrorList,
} from './Hierarchy.Style';

describe('Hierarchy Style Snapshots', () => {
  it('StyledHierarchy renders correctly', () => {
    const { asFragment } = render(<StyledHierarchy />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('StyledModalOverlay renders correctly', () => {
    const { asFragment } = render(<StyledModalOverlay />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('StyledHeader renders correctly', () => {
    const { asFragment } = render(<StyledHeader />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('StyledTitle renders correctly', () => {
    const { asFragment } = render(<StyledTitle />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('StyledHierarchyList renders correctly', () => {
    const { asFragment } = render(<StyledHierarchyList />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('StyledEditableHierarchy renders correctly', () => {
    const { asFragment } = render(<StyledEditableHierarchy />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('StyledEditionBoxContainer renders correctly', () => {
    const { asFragment } = render(<StyledEditionBoxContainer />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('StyledHierarchyInput renders correctly', () => {
    const { asFragment } = render(<StyledHierarchyInput level={0} />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('StyledModalBody renders correctly', () => {
    const { asFragment } = render(<StyledModalBody />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('StyledModalContent renders correctly', () => {
    const { asFragment } = render(<StyledModalContent />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('StyledTextSmall renders correctly', () => {
    const { asFragment } = render(<StyledTextSmall />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('StyledHierarchyContainer renders correctly', () => {
    const { asFragment } = render(<StyledHierarchyContainer level={0} />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('StyledHierarchyWrapper renders correctly', () => {
    const { asFragment } = render(<StyledHierarchyWrapper />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('StyledEditableHierarchyWrapper renders correctly', () => {
    const { asFragment } = render(<StyledEditableHierarchyWrapper />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('StyledValuesContainer renders correctly', () => {
    const { asFragment } = render(<StyledValuesContainer />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('StyledEditableValuesContainer renders correctly', () => {
    const { asFragment } = render(<StyledEditableValuesContainer />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('StyledFlexColumnGap2 renders correctly', () => {
    const { asFragment } = render(<StyledFlexColumnGap2 />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('StyledModalCloseButton renders correctly', () => {
    const { asFragment } = render(<StyledModalCloseButton />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('StyledValueContainer renders correctly', () => {
    const { asFragment } = render(<StyledValueContainer />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('StyledIconButton renders correctly', () => {
    const { asFragment } = render(<StyledIconButton />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('StyledFlexCenterContainer renders correctly', () => {
    const { asFragment } = render(<StyledFlexCenterContainer />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('ValidationErrorList renders correctly', () => {
    const { asFragment } = render(<ValidationErrorList />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('ValidationErrorItem renders correctly', () => {
    const { asFragment } = render(<ValidationErrorItem />);
    expect(asFragment()).toMatchSnapshot();
  });
});
