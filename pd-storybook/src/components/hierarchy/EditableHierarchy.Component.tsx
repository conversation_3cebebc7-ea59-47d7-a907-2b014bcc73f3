import React, {
  useCallback, useEffect, useMemo,
  useState,
} from 'react';

import { generateId } from '../../utils/GenerateID.Util';
import { FormLabel, FormLabelProps } from '../form/FormLabel.Component';
import { IconImporter } from '../iconImporter/IconImporter.Component';

import { Hierarchy } from './Hierarchy.Component';
import { hierarchyConstants } from './Hierarchy.Constants';
import {
  StyledEditableHierarchy,
  StyledEditableHierarchyWrapper,
  StyledFlexColumnGap2,
  StyledHierarchy,
  StyledHierarchyList,
  StyledTextSmall,
} from './Hierarchy.Style';
import { HierarchyInputComponent } from './HierarchyInput.Component';

export interface EditableHierarchy extends Hierarchy {
  id: string;
  title?: string;
  disableAdd?: boolean;
  disableDelete?: boolean;
  onAdd?: ((draftHierarchy: EditableHierarchy) => void);
  onDelete?: () => void;
  validator?: (value: string) => string | undefined;
  hierarchies?: EditableHierarchy[];
  child?: React.FC<Record<string, unknown> & {hierarchy: EditableHierarchy}>;
  properties?: Record<string, unknown>;
}

interface EditableHierarchyComponentProps {
  hierarchies: EditableHierarchy[];
  labelProps: FormLabelProps;
  onChange?: (draft: EditableHierarchy[]) => void;
  onRootAdd?: () => void;
  className?: string;
}

export const EditableHierarchyComponent = (
  props: EditableHierarchyComponentProps,
) => {
  const {
    hierarchies,
    labelProps,
    onChange,
    onRootAdd,
    className = '',
  } = props;

  const [draftHierarchies, setDraftHierarchies] = useState<EditableHierarchy[]>([]);

  useEffect(() => {
    const deepCopy = (h: EditableHierarchy[]): EditableHierarchy[] => h.map((hierarchy) => ({
      ...hierarchy,
      hierarchies: hierarchy.hierarchies ? deepCopy(hierarchy.hierarchies) : undefined,
    }));

    setDraftHierarchies(deepCopy(hierarchies));
  }, [hierarchies]);

  const deleteHandle = useCallback((
    parentArray: EditableHierarchy[],
    index: number,
  ) => {
    if (parentArray[index].onDelete) return parentArray[index].onDelete();

    parentArray.splice(index, 1);

    setDraftHierarchies([...draftHierarchies]);

    onChange?.(draftHierarchies);

    return undefined;
  }, [draftHierarchies]);

  const addHandle = useCallback((hierarchy: EditableHierarchy) => {
    if (hierarchy.onAdd) return hierarchy.onAdd(hierarchy);

    const newParent = hierarchy;

    if (!newParent.hierarchies) newParent.hierarchies = [];

    newParent.hierarchies.unshift({ name: '', id: generateId() });

    setDraftHierarchies([...draftHierarchies]);
    onChange?.(draftHierarchies);

    return undefined;
  }, [draftHierarchies]);

  const changeHandle = useCallback((value: string, hierarchy: EditableHierarchy) => {
    const newHierarchy = hierarchy;

    newHierarchy.name = value;

    setDraftHierarchies([...draftHierarchies]);

    onChange?.(draftHierarchies);
  }, [draftHierarchies]);

  const rootAddHandle = useCallback(() => {
    if (onRootAdd) return onRootAdd();

    const newHierarchy: EditableHierarchy = {
      name: '',
      id: generateId(),
    };

    draftHierarchies.unshift(newHierarchy);

    setDraftHierarchies([...draftHierarchies]);

    onChange?.(draftHierarchies);

    return undefined;
  }, [draftHierarchies]);

  const renderCategories = (
    hierarchiesToRender: EditableHierarchy[],
    level = 0,
  ): React.ReactNode => hierarchiesToRender.map((hierarchy, catIndex) => {
    const {
      id, title, name, backgroundColor, hierarchies: nextHierarchies, disableDelete = false, disableAdd = false, validator,
    } = hierarchy;

    return (
      <StyledEditableHierarchyWrapper key={id} showDashed={nextHierarchies && nextHierarchies.length > 0}>
        <StyledFlexColumnGap2>
          <StyledTextSmall>
            {title === '' ? title : title || hierarchyConstants.HIERARCHY_LABEL}
          </StyledTextSmall>
          <HierarchyInputComponent
            value={name}
            level={level}
            onDelete={disableDelete ? undefined : () => deleteHandle(hierarchiesToRender, catIndex)}
            onChange={(newValue) => changeHandle(newValue, hierarchy)}
            validator={validator}
            backgroundColor={backgroundColor}
            onAdd={disableAdd ? undefined : () => addHandle(hierarchy)}
          />
          {hierarchy.child && <hierarchy.child { ...{ ...hierarchy.properties, hierarchy, level }} />}
          {nextHierarchies && renderCategories(nextHierarchies, level + 1)}
        </StyledFlexColumnGap2>
      </StyledEditableHierarchyWrapper>
    );
  });

  const renderedAttributes: React.ReactNode = useMemo(() => {
    if (draftHierarchies.length === 0) {
      return (
        <StyledTextSmall>
          {hierarchyConstants.EMPTY_HIERARCHY_LABEL}
        </StyledTextSmall>
      );
    }

    return (renderCategories(draftHierarchies));
  }, [draftHierarchies]);

  return (
    <StyledHierarchy
      data-testid="hierarchy-container"
      className={className}
      >
      <div className="pd-flex pd-gap-3 pd-items-center">
        <FormLabel {...labelProps}
          />
        <button data-testid="addRootButton" onClick={() => rootAddHandle()}>
          <IconImporter
            size={24}
            name="plus"
            className="pd-text-dark-400 hover:pd-text-dark-700 pd-transition-all pd-ease-in-out"
        />
        </button>
      </div>

      <StyledHierarchyList>
        <StyledEditableHierarchy>{renderedAttributes}</StyledEditableHierarchy>
      </StyledHierarchyList>
    </StyledHierarchy>
  );
};
