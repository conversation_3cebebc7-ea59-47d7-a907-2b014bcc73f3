// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Hierarchy Style Snapshots StyledEditableHierarchy renders correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  overflow-y: auto;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Hierarchy Style Snapshots StyledEditableHierarchyWrapper renders correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 0.5rem;
  padding-left: 3px;
  margin-top: 0.2rem;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  padding-left: 0.5rem;
  position: relative;
  gap: 0rem;
  margin-top: 0.5rem;
  margin-left: 1rem;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Hierarchy Style Snapshots StyledEditableValuesContainer renders correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-left: 1.5rem;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Hierarchy Style Snapshots StyledEditionBoxContainer renders correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 0.5rem;
  margin-left: '1rem';
  position: relative;
  padding: 1px;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Hierarchy Style Snapshots StyledFlexCenterContainer renders correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 0.5rem;
  margin-top: 0.5rem;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Hierarchy Style Snapshots StyledFlexColumnGap2 renders correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: 0.5rem;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Hierarchy Style Snapshots StyledHeader renders correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -webkit-justify-content: space-between;
  justify-content: space-between;
  margin-bottom: 1rem;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Hierarchy Style Snapshots StyledHierarchy renders correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  width: 100%;
  height: 100%;
  padding: 1.5rem;
  background-color: #FFFFFF;
  position: relative;
  color: #1D1F24;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Hierarchy Style Snapshots StyledHierarchyContainer renders correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  border-radius: 12px;
  font-size: 13px;
  line-height: 18px;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  padding: 4px 0;
  background-color: #FFFFFF;
  color: #3A3D44;
  font-weight: 600;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Hierarchy Style Snapshots StyledHierarchyInput renders correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  border-radius: 0.75rem;
  height: 2.25rem;
  padding: 0.5rem;
  border: 1px solid #E0E3E8;
  width: 66.6667%;
  font-size: 13px;
  line-height: 18px;
  background-color: #FFFFFF;
}

.emotion-0::-webkit-input-placeholder {
  color: #3A3D44;
}

.emotion-0::-moz-placeholder {
  color: #3A3D44;
}

.emotion-0:-ms-input-placeholder {
  color: #3A3D44;
}

.emotion-0::placeholder {
  color: #3A3D44;
}

.emotion-0:disabled {
  background-color: #FFFFFF;
}

.emotion-0:focus-visible {
  outline: 1px solid #5D923D;
}

.emotion-0::selection {
  background-color: #CEE4F8;
}

<input
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Hierarchy Style Snapshots StyledHierarchyList renders correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: 1.5rem;
  border-radius: 0.75rem;
  margin-top: 0.5rem;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Hierarchy Style Snapshots StyledHierarchyWrapper renders correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 0.5rem;
  padding-left: 3px;
  margin-top: 0.2rem;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Hierarchy Style Snapshots StyledIconButton renders correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  cursor: pointer;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  padding: 0.25rem;
  border: none;
  background: none;
}

.emotion-0:hover {
  opacity: 0.8;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Hierarchy Style Snapshots StyledModalBody renders correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  width: 18rem;
}

.emotion-0>p {
  -webkit-align-self: center;
  -ms-flex-item-align: center;
  align-self: center;
  text-align: center;
}

.emotion-0>div {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  gap: 0.5rem;
  margin-top: 0.5rem;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Hierarchy Style Snapshots StyledModalCloseButton renders correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin: 0.5rem;
  cursor: pointer;
}

<button
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Hierarchy Style Snapshots StyledModalContent renders correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  background-color: #FFFFFF;
  border: 1px solid #E0E3E8;
  border-radius: 0.75rem;
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1),0 4px 6px -4px rgb(0 0 0 / 0.1);
  z-index: 20;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  max-width: 18rem;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Hierarchy Style Snapshots StyledModalOverlay renders correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: absolute;
  inset: 0;
  background-color: #3A3D44;
  opacity: 0.5;
  z-index: 10;
  border-radius: 0.75rem;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Hierarchy Style Snapshots StyledTextSmall renders correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-size: 12px;
  line-height: 16px;
  color: #6B6E75;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Hierarchy Style Snapshots StyledTitle renders correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-size: 12px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 0.25rem;
}

<h2
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Hierarchy Style Snapshots StyledValueContainer renders correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-size: 13px;
  line-height: 18px;
  margin-left: 2px;
  padding: 4px 12px;
  background-color: #F9FAC2;
  border-radius: 12px;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Hierarchy Style Snapshots StyledValuesContainer renders correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-wrap: wrap;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  gap: 0.5rem;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Hierarchy Style Snapshots ValidationErrorItem renders correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  margin-bottom: 1rem;
  font-weight: semi-bold;
}

<li
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`Hierarchy Style Snapshots ValidationErrorList renders correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  padding: 0;
  list-style-type: none;
}

<ul
    class="emotion-0"
  />
</DocumentFragment>
`;
