import {
  ChangeEvent,
  useState,
} from 'react';

import { StyledErrorMessages } from '../form/Form.Style';
import { IconImporter } from '../iconImporter/IconImporter.Component';

import { hierarchyConstants } from './Hierarchy.Constants';
import {
  StyledEditionBoxContainer,
  StyledHierarchyInput,
  StyledIconButton,
} from './Hierarchy.Style';

interface hierarchyInputProps {
  value: string;
  level: number;
  onDelete ?: () => void;
  onAdd?: () => void;
  onChange?: (newValue: string) => void;
  backgroundColor?: string;
  validator?: (value: string) => string | undefined;
}

export const HierarchyInputComponent = ({
  value = '',
  level,
  onDelete,
  onAdd,
  onChange,
  validator,
  backgroundColor,
}: hierarchyInputProps) => {
  const [inputValue, setInputValue] = useState(value);
  const [showButtons, setShowButtons] = useState(false);
  const [validationMessage, setValidationMessage] = useState<string | undefined>(validator?.(value));

  const onInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    const { value: changeValue } = e.target;
    setInputValue(changeValue);

    setValidationMessage(validator?.(changeValue));

    onChange?.(changeValue);
  };

  const buttons = showButtons && (
    <>
      { onAdd && (
      <StyledIconButton
        data-testid="add-button"
        onClick={() => onAdd?.()}
        >
        <IconImporter
          name="plus"
          size={24}
          className="pd-text-dark-400 hover:pd-text-dark-700 pd-transition-all pd-ease-in-out"
          />
      </StyledIconButton>
      )}
      { onDelete && (
      <StyledIconButton data-testid="delete-button" onClick={() => onDelete()}>
        <IconImporter
          name="x"
          size={24}
          className="pd-text-dark-400 hover:pd-text-dark-700 pd-transition-all pd-ease-in-out"
            />
      </StyledIconButton>
      )}
    </>
  );

  return (
    <>
      <StyledEditionBoxContainer
        onMouseEnter={() => setShowButtons(true)}
        onMouseLeave={() => setShowButtons(false)}
      >
        <StyledHierarchyInput
          data-testid={`key-${level}`}
          placeholder={hierarchyConstants.PLACEHOLDER}
          value={inputValue}
          onChange={onInputChange}
          backgroundColor={backgroundColor}
          level={level}
        />
        {buttons}
      </StyledEditionBoxContainer>
      {validationMessage && <StyledErrorMessages className='pd-text-negative pd-text-xxsm pd-mt-0 pd-ml-2'>
        {validationMessage}
      </StyledErrorMessages>}

    </>
  );
};
