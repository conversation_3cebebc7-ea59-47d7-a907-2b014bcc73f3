import { useMemo } from 'react';

import { FormLabel, FormLabelProps } from '../form/FormLabel.Component';

import { hierarchyConstants } from './Hierarchy.Constants';
import {
  StyledEditableHierarchy,
  StyledHierarchy,
  StyledHierarchyContainer,
  StyledHierarchyList,
  StyledHierarchyWrapper,
  StyledTextSmall,
} from './Hierarchy.Style';

export interface Hierarchy {
  name: string;
  backgroundColor?: string;
  hierarchies?: Hierarchy[];
}

interface HierarchyComponentProps {
  hierarchies: Hierarchy[];
  labelProps: FormLabelProps;
  className?: string;
}

export const HierarchyComponent = (
  props: HierarchyComponentProps,
) => {
  const { hierarchies, labelProps, className } = props;

  const renderHierarchies = (
    hierarchiesToRender: Hierarchy[],
    level = 0,
  ): React.ReactNode => hierarchiesToRender.map((hierarchy, index) => {
    const { name, hierarchies: childHierarchies, backgroundColor } = hierarchy;
    const hasGrandCHildren = childHierarchies && childHierarchies.some((child) => child.hierarchies);

    return (
      <StyledHierarchyWrapper key={`${name}-${index}`}>
        <StyledHierarchyContainer level={level} backgroundColor={backgroundColor}>
          {name}
        </StyledHierarchyContainer>
        <div className ={`pd-ml-4 pd-flex pd-flex-wrap pd-flex-${hasGrandCHildren ? 'col' : 'row'}`}>
          {childHierarchies && renderHierarchies(childHierarchies, level + 1)}
        </div>
      </StyledHierarchyWrapper>
    );
  });

  const renderedAttributes: React.ReactNode = useMemo(() => {
    if (hierarchies.length === 0) {
      return (
        <StyledTextSmall>
          {hierarchyConstants.EMPTY_HIERARCHY_LABEL}
        </StyledTextSmall>
      );
    }
    return renderHierarchies(hierarchies);
  }, [hierarchies]);

  return (
    <StyledHierarchy data-testid="hierarchy-container" className={className}>
      <FormLabel {...labelProps}/>

      <StyledHierarchyList>
        <StyledEditableHierarchy>{renderedAttributes}</StyledEditableHierarchy>
      </StyledHierarchyList>
    </StyledHierarchy>
  );
};
