import styled from '@emotion/styled';

import Theme from '../../configurations/Theme.Configuration';

export const getLevelBackground = (level: number) => {
  const backgrounds = {
    0: Theme.colors.white,
    1: Theme.colors.fadedGreen,
    2: Theme.colors.fadedRed,
    3: Theme.colors.fadedViolet,
    4: Theme.colors.fadedPeach,
    5: Theme.colors.fadedYellow,
    6: Theme.colors.fadedBlue,
    7: Theme.colors.fadedGray,
  };

  const backgroundColor = backgrounds[level as keyof typeof backgrounds];
  return backgroundColor;
};

export const StyledHierarchy = styled.div`
  width: 100%;
  height: 100%;
  padding: 1.5rem; 
  background-color: ${Theme.colors.white};
  position: relative;
  color: ${Theme.colors.dark[700]};
`;

export const StyledModalOverlay = styled.div`
  position: absolute;
  inset: 0;
  background-color: ${Theme.colors.dark[600]};
  opacity: 0.5;
  z-index: 10;
  border-radius: 0.75rem;
`;

export const StyledHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
`;

export const StyledTitle = styled.h2`
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 0.25rem;
`;

export const StyledHierarchyList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  border-radius: 0.75rem;
  margin-top: 0.5rem;
`;

export const StyledEditableHierarchy = styled.div`
  display: flex;
  flex-direction: column;
  overflow-y: auto;
`;

export const StyledEditionBoxContainer = styled.div`
  display: flex;
  gap: 0.5rem;
  margin-left: '1rem';
  position: relative;
  padding: 1px;
`;

export const StyledHierarchyInput = styled.input<{ level: number; backgroundColor?: string}>`
  border-radius: 0.75rem;
  height: 2.25rem;
  padding: 0.5rem;
  border: 1px solid ${Theme.colors.line};
  width: 66.6667%;
  font-size: ${Theme.fontSize.xsm};
  ::placeholder {
    color: ${Theme.colors.dark[600]};
  }
  background-color: ${({ level, backgroundColor }) => backgroundColor || getLevelBackground(level)};
  &:disabled {
    background-color: ${({ level, backgroundColor }) => backgroundColor || getLevelBackground(level)};
  }

  &:focus-visible {
    outline: 1px solid ${Theme.colors.positive};
  }

  &::selection {
    background-color: ${Theme.colors.fadedBlue};
  }
`;

export const StyledModalBody = styled.div`
  width: 18rem;

  > p {
    align-self: ${({ align }: {align?: string}) => align || 'center'} ;
    text-align: ${({ align }: {align?: string}) => align || 'center'} ;
  }

  > div {
    display: flex;
    gap: 0.5rem;
    margin-top: 0.5rem;
    flex-wrap: wrap;
    justify-content: center;
  }
`;

export const StyledModalContent = styled.div`
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: ${Theme.colors.white};
  border: 1px solid ${Theme.colors.line};
  border-radius: 0.75rem;
  box-shadow: ${Theme.shadow.lg};
  z-index: 20;
  display: flex;
  flex-direction: column;
  max-width: 18rem;
`;

export const StyledTextSmall = styled.div`
  font-size: ${Theme.fontSize.xxsm};
  color: ${Theme.colors.dark[500]};
`;

export const StyledHierarchyContainer = styled.div<{ level: number, backgroundColor?: string }>`
  border-radius: ${Theme.borderRadius.xl};
  font-size: ${Theme.fontSize.xsm};
  width: fit-content;
  padding: ${({ level }) => (level === 0 ? '4px 0' : '4px 12px')};
  background-color: ${({ level, backgroundColor }) => backgroundColor || getLevelBackground(level)};
  ${({ level }) => level === 0 && `
    color: ${Theme.colors.dark[600]};
    font-weight:${Theme.fontWeight.semiBold};
  `}
`;

export const StyledHierarchyWrapper = styled.div`
  display: flex;
  flex-direction: column;
  flex-wrap: wrap;
  gap: 0.5rem;
  padding-left: 3px;
  margin-top: 0.2rem;
`;

export const StyledEditableHierarchyWrapper = styled(StyledHierarchyWrapper)<{ showDashed?: boolean }>`
  flex-direction: column;
  padding-left: 0.5rem;
  border-left: ${({ showDashed }) => (showDashed ? `1px dashed ${Theme.colors.line}` : '')};
  position: relative;
  gap: 0rem;
  margin-top: 0.5rem;
  margin-left: 1rem;

  ${({ showDashed }) => showDashed && `
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 5px;
      height: 5px;
      background-color: ${Theme.colors.line};
      border-radius: 50%;
      transform: translateX(-50%);
    }
  `}
`;

export const StyledValuesContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
`;

export const StyledEditableValuesContainer = styled(StyledValuesContainer)`
  margin-left: 1.5rem;
  flex-direction: column;
`;

export const StyledFlexColumnGap2 = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;

export const StyledModalCloseButton = styled.button`
  margin: 0.5rem;
  cursor: pointer;
`;

export const StyledValueContainer = styled.div`
  font-size: ${Theme.fontSize.xsm};
  margin-left: 2px;
  padding: 4px 12px;
  background-color: ${Theme.colors.fadedYellow};
  border-radius: ${Theme.borderRadius.xl};
`;

export const StyledIconButton = styled.button`
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.25rem;
  border: none;
  background: none;
  
  &:hover {
    opacity: 0.8;
  }
`;

export const StyledFlexCenterContainer = styled.div`
  display: flex;
  gap: 0.5rem;
  margin-top: 0.5rem;
  flex-wrap: wrap;
  justify-content: center;
`;

export const ValidationErrorList = styled.ul`
  padding: 0;
  list-style-type: none;
`;

export const ValidationErrorItem = styled.li`
  margin-bottom: 1rem;
  font-weight: semi-bold;
`;
