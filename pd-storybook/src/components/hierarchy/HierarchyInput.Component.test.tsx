import { fireEvent, render, screen } from '@testing-library/react';

import { hierarchyConstants } from './Hierarchy.Constants';
import { HierarchyInputComponent } from './HierarchyInput.Component';

describe('HierarchyInputComponent', () => {
  const mockOnChange = jest.fn();
  const mockOnAdd = jest.fn();
  const mockOnDelete = jest.fn();
  const mockValidator = jest.fn();

  const defaultProps = {
    value: '',
    level: 1,
    onChange: mockOnChange,
    onAdd: mockOnAdd,
    onDelete: mockOnDelete,
    validator: mockValidator,
    backgroundColor: 'white',
  };

  it('renders correctly with default props', () => {
    render(<HierarchyInputComponent {...defaultProps} />);
    expect(screen.getByPlaceholderText(hierarchyConstants.PLACEHOLDER)).toBeInTheDocument();
  });

  it('calls onChange when input value changes', () => {
    render(<HierarchyInputComponent {...defaultProps} />);
    const input = screen.getByPlaceholderText(hierarchyConstants.PLACEHOLDER);
    fireEvent.change(input, { target: { value: 'New Value' } });
    expect(mockOnChange).toHaveBeenCalledWith('New Value');
  });

  it('shows validation message when input is invalid', () => {
    mockValidator.mockReturnValue('Invalid value');
    render(<HierarchyInputComponent {...defaultProps} />);
    const input = screen.getByPlaceholderText(hierarchyConstants.PLACEHOLDER);
    fireEvent.change(input, { target: { value: 'Invalid' } });
    expect(screen.getByText('Invalid value')).toBeInTheDocument();
  });

  it('calls onAdd when add button is clicked', () => {
    render(<HierarchyInputComponent {...defaultProps} />);
    fireEvent.mouseEnter(screen.getByTestId('key-1'));
    fireEvent.click(screen.getByTestId('add-button'));
    expect(mockOnAdd).toHaveBeenCalled();
  });

  it('calls onDelete when delete button is clicked', () => {
    render(<HierarchyInputComponent {...defaultProps} />);
    fireEvent.mouseEnter(screen.getByTestId('key-1'));
    fireEvent.click(screen.getByTestId('delete-button'));
    expect(mockOnDelete).toHaveBeenCalled();
  });

  it('hides buttons when mouse leaves the container', () => {
    render(<HierarchyInputComponent {...defaultProps} />);
    fireEvent.mouseEnter(screen.getByTestId('key-1'));
    expect(screen.getByTestId('add-button')).toBeInTheDocument();
    fireEvent.mouseLeave(screen.getByTestId('key-1'));
    expect(screen.queryByTestId('add-button')).not.toBeInTheDocument();
  });
});
