import { render, screen } from '@testing-library/react';

import { Hierarchy, HierarchyComponent } from './Hierarchy.Component';
import { hierarchyConstants } from './Hierarchy.Constants';

describe('HierarchyComponent', () => {
  const defaultProps = {
    hierarchies: [] as Hierarchy[],
    labelProps: { title: 'Test Label' },
  };

  it('renders correctly with default props', () => {
    render(<HierarchyComponent {...defaultProps} />);
    expect(screen.getByText('Test Label')).toBeInTheDocument();
    expect(screen.getByText(hierarchyConstants.EMPTY_HIERARCHY_LABEL)).toBeInTheDocument();
  });

  it('renders hierarchies correctly', () => {
    const hierarchies = [
      { name: 'Parent', hierarchies: [{ name: 'Child' }] },
    ];
    render(<HierarchyComponent {...defaultProps} hierarchies={hierarchies} />);
    expect(screen.getByText('Parent')).toBeInTheDocument();
    expect(screen.getByText('Child')).toBeInTheDocument();
  });

  it('renders nested hierarchies correctly', () => {
    const hierarchies = [
      { name: 'Parent', hierarchies: [{ name: 'Child', hierarchies: [{ name: 'Grandchild' }] }] },
    ];
    render(<HierarchyComponent {...defaultProps} hierarchies={hierarchies} />);
    expect(screen.getByText('Parent')).toBeInTheDocument();
    expect(screen.getByText('Child')).toBeInTheDocument();
    expect(screen.getByText('Grandchild')).toBeInTheDocument();
  });

  it('applies background color correctly', () => {
    const hierarchies = [
      { name: 'Parent', backgroundColor: 'red' },
    ];
    render(<HierarchyComponent {...defaultProps} hierarchies={hierarchies} />);
    const parentElement = screen.getByText('Parent');
    expect(parentElement).toHaveStyle('background-color: red');
  });
});
