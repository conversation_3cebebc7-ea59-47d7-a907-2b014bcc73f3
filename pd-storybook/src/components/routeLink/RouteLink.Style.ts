import { css } from '@emotion/react';
import styled from '@emotion/styled';
import { HtmlHTMLAttributes } from 'react';

import theme from '../../configurations/Theme.Configuration';
import { StyledGlobalTextStyles } from '../../styles/Global.Style';
import { CommonTextProps } from '../../types/StyledGlobalText.Type';

export type StyledRouteLinkProps = CommonTextProps & {
    href?: string;
    target?: string;
    to?: string;
} & React.PropsWithChildren & HtmlHTMLAttributes<HTMLAnchorElement>;

export const StyledRouteLink = styled.a<StyledRouteLinkProps>`
    ${(props) => css`
        ${StyledGlobalTextStyles(props)}
        font-size: ${props.size || theme.fontSize.base};
        color: ${props.color || theme.colors.primary};
        cursor: pointer;
        transition: all 0.2s;

        &:visited {
            opacity: 0.8;
        }
    `}
`;
