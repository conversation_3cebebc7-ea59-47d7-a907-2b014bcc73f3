import { render, screen } from '@testing-library/react';
import { BrowserRouter, useOutletContext } from 'react-router-dom';

import { RouteLink } from './RouteLink.Component';

jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useOutletContext: jest.fn(),
}));

describe('RouteLink Component', () => {
  const defaultProps = {
    to: '/test',
    className: 'test-class',
    children: 'Test Link',
    target: '_self',
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render a Link when outletContext is present', () => {
    (useOutletContext as jest.Mock).mockReturnValue(true);

    render(
      <BrowserRouter>
        <RouteLink {...defaultProps} />
      </BrowserRouter>,
    );

    const linkElement = screen.getByText('Test Link');
    expect(linkElement.tagName).toBe('A');
    expect(linkElement).toHaveAttribute('href', '/test');
    expect(linkElement).toHaveAttribute('target', '_self');
    expect(linkElement).toHaveClass('test-class');
  });

  it('should render an anchor tag when outletContext is not present', () => {
    (useOutletContext as jest.Mock).mockReturnValue(null);

    render(<RouteLink {...defaultProps} />);

    const linkElement = screen.getByText('Test Link');
    expect(linkElement.tagName).toBe('A');
    expect(linkElement).toHaveAttribute('href', '/test');
    expect(linkElement).toHaveClass('test-class');
  });

  it('should render children correctly', () => {
    (useOutletContext as jest.Mock).mockReturnValue(true);

    render(
      <BrowserRouter>
        <RouteLink {...defaultProps}>
          <span data-testid="child">Child Element</span>
        </RouteLink>
      </BrowserRouter>,
    );

    expect(screen.getByTestId('child')).toBeInTheDocument();
  });

  it('should set target and rel attributes when external is true', () => {
    (useOutletContext as jest.Mock).mockReturnValue(null);

    render(<RouteLink {...defaultProps} />);

    const linkElement = screen.getByText('Test Link');
    expect(linkElement).toHaveAttribute('target', '_self');
    expect(linkElement).toHaveAttribute('rel', 'noopener noreferrer');
  });

  it('should pass additional props correctly', () => {
    (useOutletContext as jest.Mock).mockReturnValue(null);

    render(<RouteLink {...defaultProps} data-testid="route-link" />);

    const linkElement = screen.getByTestId('route-link');
    expect(linkElement).toBeInTheDocument();
  });
});
