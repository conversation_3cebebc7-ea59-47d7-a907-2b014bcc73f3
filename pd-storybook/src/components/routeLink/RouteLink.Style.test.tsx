import { render } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';

import { FontSize, FontWeight } from '../../types/Global.Type';

import { StyledRouteLink, StyledRouteLinkProps } from './RouteLink.Style';

describe('StyledRouteLink Snapshots', () => {
  const defaultProps: StyledRouteLinkProps = {
    size: 'base',
    weight: 'regular',
    underline: false,
    color: '',
  };

  it('renders correctly with default props', () => {
    const { asFragment } = render(
      <BrowserRouter>
        <StyledRouteLink {...defaultProps}>Default Link</StyledRouteLink>
      </BrowserRouter>,
    );
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders correctly as a anchor element', () => {
    const { asFragment } = render(
      <StyledRouteLink {...defaultProps}>Default Link</StyledRouteLink>,
    );
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders correctly with underline', () => {
    const { asFragment } = render(
      <BrowserRouter>
        <StyledRouteLink {...defaultProps} underline>
          Underlined Link
        </StyledRouteLink>
      </BrowserRouter>,
    );
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders correctly with custom font color', () => {
    const { asFragment } = render(
      <BrowserRouter>
        <StyledRouteLink {...defaultProps} color="red">
          Custom Color Link
        </StyledRouteLink>
      </BrowserRouter>,
    );
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders correctly with different sizes', () => {
    const sizes: FontSize[] = ['xxsm', 'xsm', 'sm', 'base', 'mdPlus', 'lg', 'lgPlus', 'xlg', 'xxlg'];
    sizes.forEach((size) => {
      const { asFragment } = render(
        <BrowserRouter>
          <StyledRouteLink {...defaultProps} size={size}>
            {size} Link
          </StyledRouteLink>
        </BrowserRouter>,
      );
      expect(asFragment()).toMatchSnapshot();
    });
  });

  it('renders correctly with different weights', () => {
    const weights: FontWeight[] = ['light', 'regular', 'medium', 'semiBold', 'bold'];
    weights.forEach((weight) => {
      const { asFragment } = render(
        <BrowserRouter>
          <StyledRouteLink {...defaultProps} weight={weight}>
            {weight} Link
          </StyledRouteLink>
        </BrowserRouter>,
      );
      expect(asFragment()).toMatchSnapshot();
    });
  });

  it('renders correctly with custom className', () => {
    const { asFragment } = render(
      <BrowserRouter>
        <StyledRouteLink {...defaultProps} className="custom-class">
          Custom Class Link
        </StyledRouteLink>
      </BrowserRouter>,
    );
    expect(asFragment()).toMatchSnapshot();
  });
});
