import React from 'react';

import { StyledRouteLink, StyledRouteLinkProps } from './RouteLink.Style';

export type RouteLinkProps = StyledRouteLinkProps & {
  to: string;
  as?: React.ElementType;
  className?: string;
};

export const RouteLink: React.FC<RouteLinkProps> = ({
  to,
  className,
  children,
  as,
  target = '_self',
  ...props
}) => (
  <StyledRouteLink
    target = {target}
    as={as}
    rel='noopener noreferrer'
    className={className}
    href={to}
    to={to}
    {...props}>
    {children}
  </StyledRouteLink>
);
