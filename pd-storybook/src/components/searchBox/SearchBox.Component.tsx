import React, { ReactNode, useEffect, useState } from 'react';

import OutsideClick from '../../utils/OutsideClick';
import { IconImporter } from '../iconImporter/IconImporter.Component';

import {
  StyledPressEnterText,
  StyledSearchBoxIconWrapper,
  StyledSearchBoxInput,
  StyledSearchBoxInputWrapper,
  StyledSearchBoxLoadingMessage,
  StyledSearchBoxSuggestionItem,
  StyledSearchBoxSuggestionList,
} from './SearchBox.Style';

type SearchSugestionRenderer = (value: unknown) => ReactNode;

export interface SearchSuggestion {
  id: string | number;
  title: string;
  renderer?: SearchSugestionRenderer;
}

export interface SearchBoxProps {
  placeholder?: string;
  suggestions?: SearchSuggestion[];
  onValueChange?: (value: string) => void;
  onSearchChange?: (suggestion?: SearchSuggestion) => void;
  isLoading?: boolean;
  searchValue?: string;
  className?: string;
  loadingLabel?: string;
  pressEnterLabel?: string;
  disabled?:boolean;
  onOutsideClick?: () => void;
  onKeyDown?:(event: React.KeyboardEvent<HTMLInputElement>) => void;
}

const RenderResult = (suggestion: SearchSuggestion) => (
  <>
    {suggestion.renderer ? suggestion.renderer(suggestion) : <div>{suggestion.title}</div>}
  </>
);

const LOADING_LABEL = 'Loading...';

export const SearchBox: React.FC<SearchBoxProps> = ({
  placeholder = 'Search...',
  suggestions = [],
  onValueChange,
  onSearchChange = () => {},
  isLoading = false,
  className,
  searchValue,
  loadingLabel,
  pressEnterLabel,
  disabled = false,
  onOutsideClick,
  ...props
}) => {
  const [inputValue, setInputValue] = useState(searchValue || '');
  const [showSuggestions, setShowSuggestions] = useState(false);

  useEffect(() => {
    const shouldShowSuggestions = inputValue.trim() !== ''
      && !!suggestions
      && (suggestions.length > 0 || isLoading);
    setShowSuggestions(shouldShowSuggestions);
  }, [inputValue, suggestions, isLoading]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;
    setInputValue(value);
    if (onValueChange) {
      onValueChange(value);
    }
  };

  const handleSuggestionClick = (suggestion: SearchSuggestion) => {
    setInputValue('');
    setShowSuggestions(false);
    onSearchChange(suggestion);
  };

  return (
    <StyledSearchBoxInputWrapper {...props}>
      {!inputValue && (
        <StyledSearchBoxIconWrapper>
          <IconImporter name="search" />
        </StyledSearchBoxIconWrapper>
      )}
      {inputValue && pressEnterLabel && (
        <StyledPressEnterText>
          {pressEnterLabel}
        </StyledPressEnterText>
      )}
      <StyledSearchBoxInput
        data-testid="searchBoxInput"
        type="search"
        value={inputValue}
        onChange={handleInputChange}
        placeholder={placeholder}
        className={className}
        hasValue={!!inputValue}
        disabled={disabled}
      />
      {suggestions && showSuggestions && (
        <OutsideClick onOutsideClick={() => {
          if (onOutsideClick) return onOutsideClick();

          return setShowSuggestions(false);
        }}>
          <StyledSearchBoxSuggestionList>
            {isLoading ? (
              <StyledSearchBoxLoadingMessage>
                {loadingLabel || LOADING_LABEL}
              </StyledSearchBoxLoadingMessage>
            ) : (
              suggestions.map((suggestion) => (
                <StyledSearchBoxSuggestionItem
                  key={suggestion.id}
                  onClick={() => handleSuggestionClick(suggestion)}
                >
                  <RenderResult {...suggestion} />
                </StyledSearchBoxSuggestionItem>
              ))
            )}
          </StyledSearchBoxSuggestionList>
        </OutsideClick>
      )}
    </StyledSearchBoxInputWrapper>
  );
};
