// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`StyledSearchBox Snapshots renders StyledSearchBoxIconWrapper correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: absolute;
  left: 0.5rem;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  color: #A3A5AB;
  pointer-events: none;
}

<span
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`StyledSearchBox Snapshots renders StyledSearchBoxInput correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  width: 100%;
  padding: 0.5rem;
  padding-left: 2rem;
  border-radius: 8px;
  font-size: 14px;
  line-height: 20px;
  border: none;
  background-color: transparent;
  border: 1px solid #E0E3E8;
}

.emotion-0:disabled {
  background-color: #FAFAFA;
  color: #A3A5AB;
  cursor: not-allowed;
}

.emotion-0:active,
.emotion-0:focus,
.emotion-0::selection,
.emotion-0:focus-visible {
  outline: 1px solid #5D923D;
}

.emotion-0::selection {
  background-color: #CEE4F8;
}

.emotion-0::-webkit-search-cancel-button {
  -webkit-appearance: none;
  height: 1rem;
  width: 1rem;
  cursor: pointer;
  background: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999999'><path d='M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z'/></svg>") no-repeat center center/contain;
}

<input
    class="emotion-0"
    placeholder="Buscar..."
  />
</DocumentFragment>
`;

exports[`StyledSearchBox Snapshots renders StyledSearchBoxInputWrapper correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: relative;
  width: auto;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`StyledSearchBox Snapshots renders StyledSearchBoxLoadingMessage correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  padding: 0.5rem;
  color: #A3A5AB;
  font-style: italic;
}

<div
    class="emotion-0"
  >
    Loading...
  </div>
</DocumentFragment>
`;

exports[`StyledSearchBox Snapshots renders StyledSearchBoxSuggestionItem correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  padding: 0.5rem;
  cursor: pointer;
}

.emotion-0:hover {
  background-color: #E0E3E8;
}

<li
    class="emotion-0"
  >
    Sugerencia
  </li>
</DocumentFragment>
`;

exports[`StyledSearchBox Snapshots renders StyledSearchBoxSuggestionList correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  position: absolute;
  left: 0;
  right: 0;
  z-index: 10;
  background-color: #FFFFFF;
  border: 1px solid #E0E3E8;
  border-radius: 8px;
  list-style-type: none;
  max-height: 12.5rem;
  overflow-y: auto;
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1),0 4px 6px -4px rgb(0 0 0 / 0.1);
}

<ul
    class="emotion-0"
  />
</DocumentFragment>
`;
