import { render } from '@testing-library/react';

import {
  StyledSearchBoxIconWrapper,
  StyledSearchBoxInput,
  StyledSearchBoxInputWrapper,
  StyledSearchBoxLoadingMessage,
  StyledSearchBoxSuggestionItem,
  StyledSearchBoxSuggestionList,
} from './SearchBox.Style';

describe('StyledSearchBox Snapshots', () => {
  it('renders StyledSearchBoxInputWrapper correctly', () => {
    const { asFragment } = render(<StyledSearchBoxInputWrapper />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders StyledSearchBoxInput correctly', () => {
    const { asFragment } = render(
      <StyledSearchBoxInput placeholder="Buscar..." />,
    );
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders StyledSearchBoxIconWrapper correctly', () => {
    const { asFragment } = render(<StyledSearchBoxIconWrapper />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders StyledSearchBoxSuggestionList correctly', () => {
    const { asFragment } = render(<StyledSearchBoxSuggestionList />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders StyledSearchBoxSuggestionItem correctly', () => {
    const { asFragment } = render(
      <StyledSearchBoxSuggestionItem>Sugerencia</StyledSearchBoxSuggestionItem>,
    );
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders StyledSearchBoxLoadingMessage correctly', () => {
    const { asFragment } = render(
      <StyledSearchBoxLoadingMessage>Loading...</StyledSearchBoxLoadingMessage>,
    );
    expect(asFragment()).toMatchSnapshot();
  });
});
