/* eslint-disable max-len */
import styled from '@emotion/styled';

import Theme from '../../configurations/Theme.Configuration';

export const StyledSearchBoxInputWrapper = styled.div`
  position: relative;
  width: auto;
  flex-grow: 1;
`;

export const StyledSearchBoxInput = styled.input<{ hasValue?: boolean }>`
  width: 100%;
  padding: 0.5rem;
  padding-left: ${(props) => (props.hasValue ? '0.5rem' : '2rem')};
  border-radius: ${Theme.borderRadius.DEFAULT};
  font-size: ${Theme.fontSize.sm};
  border: none;
  background-color: ${Theme.colors.transparent};
  border: 1px solid ${Theme.colors.line};

  &:disabled {
    background-color: ${Theme.colors.lightGray};
    color: ${Theme.colors.dark[400]};
    cursor: not-allowed;
  }

  &:active, &:focus, &::selection, &:focus-visible {
    outline: 1px solid ${Theme.colors.positive};
  }
  &::selection {
    background-color: ${Theme.colors.fadedBlue};
  }

  &::-webkit-search-cancel-button {
    -webkit-appearance: none;
    height: 1rem;
    width: 1rem;
    cursor: pointer;
    background: 
      url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23999999'><path d='M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z'/></svg>") 
      no-repeat center center / contain;
  }
`;

export const StyledPressEnterText = styled.span`
  position: absolute;
  right: 2rem;
  top: 50%;
  transform: translateY(-50%);
  color: ${Theme.colors.dark[400]};
  font-size: 8px;
  pointer-events: none;
  font-style: italic;
  padding: 0.25rem 0.5rem;
  border-radius: ${Theme.borderRadius.DEFAULT};
  background-color: ${Theme.colors.white};
  max-width: 4rem;
  max-height: 2rem;
  text-align: center;
  overflow: hidden;
`;

export const StyledSearchBoxIconWrapper = styled.span`
  position: absolute;
  left: 0.5rem;
  top: 50%;
  transform: translateY(-50%);
  color: ${Theme.colors.dark[400]};
  pointer-events: none;
`;

export const StyledSearchBoxSuggestionList = styled.ul`
  position: absolute;
  left: 0;
  right: 0;
  z-index: 10;
  background-color: ${Theme.colors.white};
  border: 1px solid ${Theme.colors.line};
  border-radius: ${Theme.borderRadius.DEFAULT};
  list-style-type: none;
  max-height: 12.5rem;
  overflow-y: auto;
  box-shadow: ${Theme.shadow.lg};
`;

export const StyledSearchBoxSuggestionItem = styled.li`
  padding: 0.5rem;
  cursor: pointer;
  &:hover {
    background-color: ${Theme.colors.line};
  }
`;

export const StyledSearchBoxLoadingMessage = styled.div`
  padding: 0.5rem;
  color: ${Theme.colors.dark[400]};
  font-style: italic;
`;
