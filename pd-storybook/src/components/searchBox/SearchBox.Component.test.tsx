import {
  act,
  fireEvent,
  render,
  screen,
} from '@testing-library/react';

import '@testing-library/jest-dom';
import { SearchBox, SearchBoxProps } from './SearchBox.Component';

jest.mock('../iconImporter/IconImporter.Component', () => ({
  IconImporter: ({ name }: { name: string }) => (
    <div data-testid={`icon-${name}`} />
  ),
}));

const mockSuggestions = [
  { id: 1, title: 'Apple' },
  { id: 2, title: 'Banana' },
  { id: 3, title: 'Cherry' },
];

const mockRenderer = jest.fn((suggestion) => <div data-testid="custom-render">{suggestion.title.toUpperCase()}</div>);

const mockSuggestionsWithRenderer = [
  { id: 1, title: 'Apple', renderer: mockRenderer },
  { id: 2, title: 'Banana' },
  { id: 3, title: 'Cherry' },
];

const defaultProps: SearchBoxProps = {
  placeholder: 'Search...',
  suggestions: [],
  onValueChange: jest.fn(),
  onSearchChange: jest.fn(),
  isLoading: false,
};

describe('SearchBox Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders correctly with default props', () => {
    render(<SearchBox {...defaultProps} />);
    expect(screen.getByPlaceholderText('Search...')).toBeInTheDocument();
    expect(screen.getByTestId('icon-search')).toBeInTheDocument();
  });

  it('handles input change', () => {
    render(<SearchBox {...defaultProps} />);
    const input = screen.getByPlaceholderText('Search...');
    fireEvent.change(input, { target: { value: 'test' } });
    expect(defaultProps.onValueChange).toHaveBeenCalledWith('test');
  });

  it('shows and hides suggestions based on input and suggestions prop', () => {
    const { rerender } = render(<SearchBox {...defaultProps} />);
    const input = screen.getByPlaceholderText('Search...');

    expect(screen.queryByRole('list')).not.toBeInTheDocument();

    fireEvent.change(input, { target: { value: 'a' } });
    expect(screen.queryByRole('list')).not.toBeInTheDocument();

    rerender(<SearchBox {...defaultProps} suggestions={mockSuggestions} />);
    expect(screen.getByRole('list')).toBeInTheDocument();
    expect(screen.getByText('Apple')).toBeInTheDocument();

    fireEvent.change(input, { target: { value: '' } });
    expect(screen.queryByRole('list')).not.toBeInTheDocument();
  });

  it('shows loading message when isLoading is true', () => {
    const { rerender } = render(<SearchBox {...defaultProps} />);
    const input = screen.getByPlaceholderText('Search...');

    fireEvent.change(input, { target: { value: 'a' } });
    rerender(<SearchBox {...defaultProps} isLoading={true} />);

    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  it('applies custom className', () => {
    render(<SearchBox {...defaultProps} className="custom-class" />);
    expect(screen.getByPlaceholderText('Search...')).toHaveClass(
      'custom-class',
    );
  });

  it('updates suggestions visibility when suggestions prop changes', () => {
    const { rerender } = render(<SearchBox {...defaultProps} />);
    const input = screen.getByPlaceholderText('Search...');

    fireEvent.change(input, { target: { value: 'a' } });
    expect(screen.queryByRole('list')).not.toBeInTheDocument();

    rerender(<SearchBox {...defaultProps} suggestions={mockSuggestions} />);
    expect(screen.getByRole('list')).toBeInTheDocument();

    rerender(<SearchBox {...defaultProps} suggestions={[]} />);
    expect(screen.queryByRole('list')).not.toBeInTheDocument();
  });

  it('handles edge cases with empty string and whitespace', () => {
    render(
      <SearchBox {...defaultProps} suggestions={mockSuggestions} />,
    );
    const input = screen.getByPlaceholderText('Search...');

    fireEvent.change(input, { target: { value: ' ' } });
    expect(screen.queryByRole('list')).not.toBeInTheDocument();

    fireEvent.change(input, { target: { value: 'a' } });
    expect(screen.getByRole('list')).toBeInTheDocument();

    fireEvent.change(input, { target: { value: '' } });
    expect(screen.queryByRole('list')).not.toBeInTheDocument();
  });

  it('handles rapid input changes', () => {
    jest.useFakeTimers();
    render(<SearchBox {...defaultProps} suggestions={mockSuggestions} />);
    const input = screen.getByPlaceholderText('Search...');

    fireEvent.change(input, { target: { value: 'a' } });
    fireEvent.change(input, { target: { value: 'ap' } });
    fireEvent.change(input, { target: { value: 'app' } });

    act(() => {
      jest.runAllTimers();
    });

    expect(screen.getByText('Apple')).toBeInTheDocument();
    jest.useRealTimers();
  });

  it('does not show suggestions when input is empty and no suggestions are provided', () => {
    render(<SearchBox {...defaultProps} />);
    const input = screen.getByPlaceholderText('Search...');

    fireEvent.change(input, { target: { value: '' } });
    expect(screen.queryByRole('list')).not.toBeInTheDocument();
  });

  it('uses the default placeholder when none is provided', () => {
    render(
      <SearchBox
        {...defaultProps}
        placeholder={undefined}
      />,
    );
    expect(screen.getByPlaceholderText('Search...')).toBeInTheDocument();
  });

  it('handles suggestion click correctly', () => {
    render(<SearchBox {...defaultProps} suggestions={mockSuggestions} />);
    const input = screen.getByPlaceholderText('Search...');

    fireEvent.change(input, { target: { value: 'a' } });
    expect(screen.getByText('Apple')).toBeInTheDocument();

    fireEvent.click(screen.getByText('Apple'));

    expect(defaultProps.onSearchChange).toHaveBeenCalledWith(mockSuggestions[0]);
  });

  it('uses default suggestions when none are provided', () => {
    render(<SearchBox placeholder="Search..." />);
    const input = screen.getByPlaceholderText('Search...');
    fireEvent.change(input, { target: { value: 'a' } });
    expect(screen.queryByRole('list')).not.toBeInTheDocument();
  });

  it('calls default onSearchChange when none is provided', () => {
    render(<SearchBox placeholder="Search..." />);
    const input = screen.getByPlaceholderText('Search...');
    fireEvent.change(input, { target: { value: 'test' } });
    fireEvent.keyDown(input, { key: 'Enter', code: 'Enter' });
  });

  it('defaults isLoading to false when not provided', () => {
    render(<SearchBox placeholder="Search..." suggestions={mockSuggestions} />);
    const input = screen.getByPlaceholderText('Search...');
    fireEvent.change(input, { target: { value: 'a' } });
    expect(screen.queryByText('Loading...')).not.toBeInTheDocument();
  });

  it('handles undefined onValueChange without errors', () => {
    render(<SearchBox placeholder="Search..." />);
    const input = screen.getByPlaceholderText('Search...');
    fireEvent.change(input, { target: { value: 'test' } });
  });

  it('hides suggestions when clicking outside', () => {
    render(<SearchBox {...defaultProps} suggestions={mockSuggestions} />);
    const input = screen.getByPlaceholderText('Search...');

    fireEvent.change(input, { target: { value: 'a' } });
    expect(screen.getByText('Apple')).toBeInTheDocument();

    fireEvent.mouseDown(document.body);

    expect(screen.queryByText('Apple')).not.toBeInTheDocument();
  });

  it('uses custom renderer when provided in suggestion', () => {
    render(<SearchBox {...defaultProps} suggestions={mockSuggestionsWithRenderer} />);
    const input = screen.getByPlaceholderText('Search...');

    fireEvent.change(input, { target: { value: 'a' } });

    expect(mockRenderer).toHaveBeenCalledWith(mockSuggestionsWithRenderer[0]);

    expect(screen.getByTestId('custom-render')).toBeInTheDocument();
    expect(screen.getByText('APPLE')).toBeInTheDocument();

    expect(screen.getByText('Banana')).toBeInTheDocument();
  });

  it('shows press enter text when pressEnterLabel is provided and input has value', () => {
    render(
      <SearchBox
        {...defaultProps}
        pressEnterLabel="Press Enter to search"
      />,
    );
    const input = screen.getByPlaceholderText('Search...');

    expect(screen.queryByText('Press Enter to search')).not.toBeInTheDocument();

    fireEvent.change(input, { target: { value: 'test' } });
    expect(screen.getByText('Press Enter to search')).toBeInTheDocument();

    fireEvent.change(input, { target: { value: '' } });
    expect(screen.queryByText('Press Enter to search')).not.toBeInTheDocument();
  });
});
