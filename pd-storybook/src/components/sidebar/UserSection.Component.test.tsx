import {
  fireEvent,
  render,
  screen,
  within,
} from '@testing-library/react';

import { UserMenuItem, UserSection } from './UserSection.Component';

describe('UserSection', () => {
  const mockProfileUserInfo = {
    userName: '<PERSON>',
    userRol: 'Admin',
    userProfilePhoto: 'https://example.com/photo.jpg',
  };

  const mockItems: UserMenuItem[] = [
    {
      id: '1',
      label: 'Profile',
      icon: 'user',
      onClick: jest.fn(),
    },
    {
      id: '2',
      label: 'Settings',
      icon: 'gear',
      onClick: jest.fn(),
    },
  ];

  const mockLogoutButton = {
    children: 'Sign Out',
    onClick: jest.fn(),
  };

  it('should render with default values when no props are provided', () => {
    render(<UserSection />);
    expect(screen.getByText('User Name')).toBeInTheDocument();
    expect(screen.getByText('User Role')).toBeInTheDocument();
  });

  it('should render with provided profileUserInfo', () => {
    render(<UserSection profileUserInfo={mockProfileUserInfo} />);
    expect(screen.getByText('Juan Rodriguez')).toBeInTheDocument();
    expect(screen.getByText('Admin')).toBeInTheDocument();
    const avatar = screen.getByRole('img');
    expect(avatar).toHaveAttribute('src', mockProfileUserInfo.userProfilePhoto);
  });

  it('should toggle user menu on button click', () => {
    render(<UserSection />);
    const userMenuButton = screen.getByRole('button', { name: /User Name/ });

    // Menu should be closed initially
    expect(screen.queryByText('Log out')).not.toBeInTheDocument();

    // Open menu
    fireEvent.click(userMenuButton);
    expect(screen.getByText('Log out')).toBeInTheDocument();

    // Close menu
    fireEvent.click(userMenuButton);
    expect(screen.queryByText('Log out')).not.toBeInTheDocument();
  });

  it('should render menu items when provided', () => {
    render(<UserSection items={mockItems} />);
    const userMenuButton = screen.getByRole('button', { name: /User Name/ });
    fireEvent.click(userMenuButton);

    mockItems.forEach((item) => {
      expect(screen.getByText(item.label)).toBeInTheDocument();
    });
  });

  it('should call onClick for menu items when clicked', () => {
    render(<UserSection items={mockItems} />);
    const userMenuButton = screen.getByRole('button', { name: /User Name/ });
    fireEvent.click(userMenuButton);

    const profileButton = screen.getByText('Profile');
    fireEvent.click(profileButton);
    expect(mockItems[0].onClick).toHaveBeenCalled();
  });

  it('should call onClick for logout button when clicked', () => {
    render(<UserSection logoutButton={mockLogoutButton} />);
    const userMenuButton = screen.getByRole('button', { name: /User Name/ });
    fireEvent.click(userMenuButton);

    const logoutButton = screen.getByText('Sign Out');
    fireEvent.click(logoutButton);
    expect(mockLogoutButton.onClick).toHaveBeenCalled();
  });

  it('should close the menu when clicking outside', () => {
    render(
      <div>
        <div data-testid="outside-element">Outside</div>
        <UserSection />
      </div>,
    );
    const userMenuButton = screen.getByRole('button', { name: /User Name/ });
    fireEvent.click(userMenuButton);

    // Menu is open
    expect(screen.getByText('Log out')).toBeInTheDocument();

    // Click outside
    fireEvent.mouseDown(screen.getByTestId('outside-element'));

    // Menu should be closed
    expect(screen.queryByText('Log out')).not.toBeInTheDocument();
  });

  it('should display user info in the expanded menu', () => {
    render(<UserSection profileUserInfo={mockProfileUserInfo} />);
    const userMenuButton = screen.getByRole('button', { name: /Juan Rodriguez/ });
    fireEvent.click(userMenuButton);

    const menu = screen.getByRole('list').parentElement;
    expect(menu).not.toBeNull();

    if (menu) {
      expect(within(menu).getByText(mockProfileUserInfo.userName)).toBeInTheDocument();
      // The email is hardcoded in the component, so we test for it directly
      expect(within(menu).getByText('<EMAIL>')).toBeInTheDocument();
    }
  });

  it('should rotate caret icon when menu is opened', () => {
    render(<UserSection />);
    const userMenuButton = screen.getByRole('button', { name: /User Name/ });
    const caretIcon = userMenuButton.querySelector('svg'); // Assuming IconImporter renders an svg

    expect(caretIcon).not.toHaveClass('pd-rotate-180');

    fireEvent.click(userMenuButton);
    expect(caretIcon).toHaveClass('pd-rotate-180');

    fireEvent.click(userMenuButton);
    expect(caretIcon).not.toHaveClass('pd-rotate-180');
  });

  it('should render with empty items array', () => {
    render(<UserSection items={[]} />);
    const userMenuButton = screen.getByRole('button', { name: /User Name/ });
    fireEvent.click(userMenuButton);

    // Should show logout button but no menu items
    expect(screen.getByText('Log out')).toBeInTheDocument();
    // Should not show the separator line when no items
    expect(screen.queryByRole('separator')).not.toBeInTheDocument();
  });

  it('should render separator when items exist', () => {
    render(<UserSection items={mockItems} />);
    const userMenuButton = screen.getByRole('button', { name: /User Name/ });
    fireEvent.click(userMenuButton);

    // Should show separator when items exist
    const menu = screen.getByRole('list');
    const separator = menu.querySelector('.pd-border-t');
    expect(separator).toBeInTheDocument();
  });

  it('should handle logoutButton with custom children', () => {
    const customLogoutButton = {
      children: 'Custom Sign Out Text',
      onClick: jest.fn(),
    };
    render(<UserSection logoutButton={customLogoutButton} />);
    const userMenuButton = screen.getByRole('button', { name: /User Name/ });
    fireEvent.click(userMenuButton);

    expect(screen.getByText('Custom Sign Out Text')).toBeInTheDocument();
    expect(screen.queryByText('Log out')).not.toBeInTheDocument();
  });

  it('should handle logoutButton without children (default text)', () => {
    const logoutButtonWithoutChildren = {
      onClick: jest.fn(),
    };
    render(<UserSection logoutButton={logoutButtonWithoutChildren} />);
    const userMenuButton = screen.getByRole('button', { name: /User Name/ });
    fireEvent.click(userMenuButton);

    expect(screen.getByText('Log out')).toBeInTheDocument();
  });

  it('should handle logoutButton as undefined (default behavior)', () => {
    render(<UserSection logoutButton={undefined} />);
    const userMenuButton = screen.getByRole('button', { name: /User Name/ });
    fireEvent.click(userMenuButton);

    const logoutButton = screen.getByText('Log out');
    fireEvent.click(logoutButton);
    // Should not throw error when onClick is undefined
  });

  it('should handle profileUserInfo with userEmail', () => {
    const profileWithEmail = {
      userName: 'John Doe',
      userRol: 'Developer',
      userEmail: '<EMAIL>',
      userProfilePhoto: 'https://example.com/john.jpg',
    };
    render(<UserSection profileUserInfo={profileWithEmail} />);
    const userMenuButton = screen.getByRole('button', { name: /John Doe/ });
    fireEvent.click(userMenuButton);

    // Should still show hardcoded email (component doesn't use userEmail prop)
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
  });

  it('should prevent event propagation on button click', () => {
    const parentClickHandler = jest.fn();
    render(
      <div onClick={parentClickHandler}>
        <UserSection />
      </div>,
    );

    const userMenuButton = screen.getByRole('button', { name: /User Name/ });
    fireEvent.click(userMenuButton);

    // Parent click handler should not be called due to stopPropagation
    expect(parentClickHandler).not.toHaveBeenCalled();
  });

  it('should handle click outside when userMenuRef.current is null', () => {
    const { unmount } = render(<UserSection />);
    const userMenuButton = screen.getByRole('button', { name: /User Name/ });

    // Open menu
    fireEvent.click(userMenuButton);
    expect(screen.getByText('Log out')).toBeInTheDocument();

    // Unmount component to make ref.current null
    unmount();

    // Simulate click outside - should not throw error
    fireEvent.mouseDown(document.body);
    // No assertion needed - just ensuring no error is thrown
  });

  it('should handle items as null/undefined vs empty array', () => {
    // Test with null items
    const { rerender } = render(<UserSection items={null as unknown as UserMenuItem[]} />);
    let userMenuButton = screen.getByRole('button', { name: /User Name/ });
    fireEvent.click(userMenuButton);

    // Should show logout button but no separator
    expect(screen.getByText('Log out')).toBeInTheDocument();
    expect(screen.queryByRole('separator')).not.toBeInTheDocument();

    // Close menu
    fireEvent.click(userMenuButton);

    // Test with undefined items
    rerender(<UserSection items={undefined} />);
    userMenuButton = screen.getByRole('button', { name: /User Name/ });
    fireEvent.click(userMenuButton);

    // Should show logout button but no separator
    expect(screen.getByText('Log out')).toBeInTheDocument();
    expect(screen.queryByRole('separator')).not.toBeInTheDocument();

    // Close menu
    fireEvent.click(userMenuButton);

    // Test with empty array
    rerender(<UserSection items={[]} />);
    userMenuButton = screen.getByRole('button', { name: /User Name/ });
    fireEvent.click(userMenuButton);

    // Should show logout button but no separator (length is 0)
    expect(screen.getByText('Log out')).toBeInTheDocument();
    expect(screen.queryByRole('separator')).not.toBeInTheDocument();
  });

  it('should handle event.target as non-Node in click outside handler', () => {
    render(<UserSection />);
    const userMenuButton = screen.getByRole('button', { name: /User Name/ });

    // Open menu
    fireEvent.click(userMenuButton);
    expect(screen.getByText('Log out')).toBeInTheDocument();

    // Create a mock event with target that's not a Node
    const mockEvent = new MouseEvent('mousedown', { bubbles: true });
    Object.defineProperty(mockEvent, 'target', {
      value: null,
      writable: false,
    });

    // Dispatch the event - should not throw error
    document.dispatchEvent(mockEvent);

    // Menu should still be open since target is not a valid Node
    expect(screen.getByText('Log out')).toBeInTheDocument();
  });

  it('should handle cleanup of event listener on unmount', () => {
    const removeEventListenerSpy = jest.spyOn(document, 'removeEventListener');
    const { unmount } = render(<UserSection />);

    // Unmount component
    unmount();

    // Should have called removeEventListener
    expect(removeEventListenerSpy).toHaveBeenCalledWith('mousedown', expect.any(Function));

    removeEventListenerSpy.mockRestore();
  });
});
