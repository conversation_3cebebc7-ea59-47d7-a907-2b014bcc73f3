import {
  useEffect, useRef, useState,
} from 'react';

import { Avatar } from '../avatar/Avatar.Component';
import { IconImporter } from '../iconImporter/IconImporter.Component';
import { IconName } from '../iconImporter/IconMap.Component';
import { Title } from '../title/Title.Component';

interface ProfileUserInfo {
  userProfilePhoto?: string;
  userName?: string;
  userRol?: string;
  userEmail?: string;
}

export interface UserMenuItem {
  id: string;
  label: string;
  icon: IconName;
  onClick: () => void;
}

interface UserSectionProps {
  profileUserInfo?: ProfileUserInfo;
  logoutButton?: any;
  items?: UserMenuItem[];
}

export function UserSection({ profileUserInfo, logoutButton, items }: UserSectionProps) {
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);
  const userMenuRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (userMenuRef.current && !userMenuRef.current.contains(event.target as Node)) {
        setIsUserMenuOpen(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div className="pd-mt-auto pd-relative pd-px-2 pd-py-3 pd-border-t" ref={userMenuRef}>
      <button
        className="pd-flex pd-items-center pd-gap-3 pd-w-full"
        onClick={(e) => { e.stopPropagation(); setIsUserMenuOpen((v) => !v); }}
        id="sidebar-user-menu"
        type="button"
      >
        <Avatar src={profileUserInfo?.userProfilePhoto || ''} size={32} />
        <div className="pd-flex pd-flex-col pd-text-left">
          <Title as="h5" size="xsm">{profileUserInfo?.userName || 'User Name'}</Title>
          <Title as="h6" size="xxsm" weight="light" className="!pd-text-dark-400">
            {profileUserInfo?.userRol || 'User Role'}
          </Title>
        </div>
        <IconImporter name="caretDown" className={`pd-ml-auto pd-transition-transform ${isUserMenuOpen ? 'pd-rotate-180' : ''}`} />
      </button>
      {isUserMenuOpen && (
        <div
          className="pd-absolute pd-bottom-full pd-mb-2 pd-w-full pd-bg-white pd-rounded-lg pd-shadow-lg pd-border pd-z-10"
        >
          <div className="pd-flex pd-items-center pd-gap-3 pd-p-4 pd-border-b">
            <Avatar src={profileUserInfo?.userProfilePhoto || ''} size={48} />
            <div>
              <span className="pd-block pd-text-sm pd-font-semibold">{profileUserInfo?.userName || 'User Name'}</span>
              <span className="pd-block pd-text-xs pd-text-gray-500"><EMAIL></span>
            </div>
          </div>
          <ul>
            {items?.map((item) => (
              <li key={item.id}>
                <button
                  type="button"
                  className="pd-w-full pd-flex pd-items-center pd-gap-2 pd-px-4 pd-py-2 pd-text-sm pd-hover:bg-gray-200"
                  onClick={item.onClick}
                >
                  <IconImporter name={item.icon} />
                  {item.label}
                </button>
              </li>
            ))}
            {items && items.length > 0 && <div className="pd-border-t pd-my-1" />}
            <li>
              <button
                type="button"
                className="pd-w-full pd-flex pd-items-center pd-gap-2 pd-px-4 pd-py-2 pd-text-sm pd-hover:bg-gray-200 pd-text-red-600"
                onClick={logoutButton?.onClick}
              >
                <IconImporter name="userMinus" />
                {' '}
                {logoutButton?.children || 'Log out'}
              </button>
            </li>
          </ul>
        </div>
      )}
    </div>
  );
}
