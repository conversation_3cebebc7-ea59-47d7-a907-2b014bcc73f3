import {
  cleanup,
  fireEvent,
  render, screen,
} from '@testing-library/react';
import React from 'react';

import '@testing-library/jest-dom';
import { CarouselComponent, ImageData } from './Carousel.Component';

// Mocks
jest.mock('../../utils/Cn.Util', () => ({
  cn: (...args: (string | undefined | null | false)[]) => args.filter(Boolean).join(' '),
}));

jest.mock('../iconImporter/IconImporter.Component', () => ({
  IconImporter: ({ name, className }: { name: string; className?: string }) => (
    <span data-testid={`icon-${name}`} className={className}>{name}</span>
  ),
}));

jest.mock('../loaders/CircleLoader.Component', () => ({
  CircleLoader: ({ className, size }: { className?: string; size?: string }) => (
    <div data-testid="circle-loader" className={className} data-size={size}>Loading...</div>
  ),
}));

afterEach(() => {
  cleanup();
  jest.clearAllMocks();
});

describe('CarouselComponent', () => {
  const mockImages: ImageData[] = [
    { id: '1', url: 'https://example.com/image1.jpg', name: 'Image 1' },
    { id: '2', url: 'https://example.com/image2.jpg', name: 'Image 2' },
    { id: '3', url: 'https://example.com/image3.jpg', name: 'Image 3' },
  ];

  const processingImage: ImageData = {
    id: '4',
    url: '',
    name: 'Processing Image',
    processing: true,
  };

  test('renders the carousel with default classes', () => {
    render(<CarouselComponent images={mockImages} />);

    const carousel = screen.getByRole('img', { name: 'Image 1' });
    expect(carousel).toBeInTheDocument();

    const mainContainer = screen.getByText('Seleccionar').closest('div[class*="pd-flex pd-flex-col pd-gap-4"]');
    expect(mainContainer).toHaveClass('pd-flex', 'pd-flex-col', 'pd-gap-4', 'pd-flex-1', 'pd-w-full');
  });

  test('renders the carousel with custom className', () => {
    render(<CarouselComponent images={mockImages} className="custom-class" />);

    const mainContainer = screen.getByText('Seleccionar').closest('div[class*="pd-flex pd-flex-col pd-gap-4"]');
    expect(mainContainer).toHaveClass('custom-class');
  });

  test('renders navigation buttons when canScrollLeft and canScrollRight are true', () => {
    render(
      <CarouselComponent
        images={mockImages}
        canScrollLeft={true}
        canScrollRight={true}
      />,
    );

    expect(screen.getByRole('button', { name: 'Scroll left' })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Scroll right' })).toBeInTheDocument();
  });

  test('does not render navigation buttons when canScrollLeft and canScrollRight are false', () => {
    render(
      <CarouselComponent
        images={mockImages}
        canScrollLeft={false}
        canScrollRight={false}
      />,
    );

    expect(screen.queryByRole('button', { name: 'Scroll left' })).not.toBeInTheDocument();
    expect(screen.queryByRole('button', { name: 'Scroll right' })).not.toBeInTheDocument();
  });

  test('renders the correct number of images', () => {
    render(<CarouselComponent images={mockImages} />);

    const imageElements = screen.getAllByRole('img');
    expect(imageElements).toHaveLength(mockImages.length);
  });

  test('calls onScrollLeft when left button is clicked', () => {
    const handleScrollLeft = jest.fn();
    render(
      <CarouselComponent
        images={mockImages}
        canScrollLeft={true}
        onScrollLeft={handleScrollLeft}
      />,
    );

    fireEvent.click(screen.getByRole('button', { name: 'Scroll left' }));
    expect(handleScrollLeft).toHaveBeenCalledTimes(1);
  });

  test('calls onScrollRight when right button is clicked', () => {
    const handleScrollRight = jest.fn();
    render(
      <CarouselComponent
        images={mockImages}
        canScrollRight={true}
        onScrollRight={handleScrollRight}
      />,
    );

    fireEvent.click(screen.getByRole('button', { name: 'Scroll right' }));
    expect(handleScrollRight).toHaveBeenCalledTimes(1);
  });

  test('renders processing state for images', () => {
    render(<CarouselComponent images={[...mockImages, processingImage]} />);

    expect(screen.getByTestId('circle-loader')).toBeInTheDocument();
    expect(screen.getByText('Procesando...')).toBeInTheDocument();
  });

  test('does not render "Seleccionar" button when addImageEnabled is false', () => {
    render(<CarouselComponent images={mockImages} addImageEnabled={false} />);

    expect(screen.queryByText('Seleccionar')).not.toBeInTheDocument();
  });

  test('calls onAddImages when "Seleccionar" button is clicked', () => {
    const handleAddImages = jest.fn();
    render(
      <CarouselComponent
        images={mockImages}
        onAddImages={handleAddImages}
      />,
    );

    fireEvent.click(screen.getByText('Seleccionar'));
    expect(handleAddImages).toHaveBeenCalledTimes(1);
  });

  test('calls onRemoveImage when remove button is clicked', () => {
    const handleRemoveImage = jest.fn();
    render(
      <CarouselComponent
        images={mockImages}
        onRemoveImage={handleRemoveImage}
      />,
    );

    // Find the first remove button (should be associated with the first image)
    const removeButtons = screen.getAllByTestId('icon-x');
    expect(removeButtons).toHaveLength(mockImages.length);

    // Click the first remove button
    fireEvent.click(removeButtons[0].closest('button')!);
    expect(handleRemoveImage).toHaveBeenCalledWith(mockImages[0].id);
  });

  test('renders "Portada" label on the first image', () => {
    render(<CarouselComponent images={mockImages} />);

    const portadaLabels = screen.getAllByText('Portada');
    expect(portadaLabels).toHaveLength(1);
  });

  test('handles empty images array gracefully', () => {
    render(<CarouselComponent images={[]} />);

    expect(screen.getByText('Seleccionar')).toBeInTheDocument();
    expect(screen.queryByRole('img')).not.toBeInTheDocument();
  });

  test('uses provided containerRef if supplied', () => {
    const customRef = React.createRef<HTMLDivElement>();
    render(<CarouselComponent images={mockImages} containerRef={customRef} />);

    // The test passes if it renders without errors
    expect(screen.getAllByRole('img')).toHaveLength(mockImages.length);
  });
});
