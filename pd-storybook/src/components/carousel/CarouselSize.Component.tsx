'use client';

import { CaretLeft, CaretRight } from '@phosphor-icons/react';
import { cva, VariantProps } from 'class-variance-authority';
import useEmblaCarousel, {
  type UseEmblaCarouselType,
} from 'embla-carousel-react';
import * as React from 'react';

import { cn } from '../../utils/Cn.Util';

type CarouselApi = UseEmblaCarouselType[1]
type UseCarouselParameters = Parameters<typeof useEmblaCarousel>
type CarouselOptions = UseCarouselParameters[0]
type CarouselPlugin = UseCarouselParameters[1]

type CarouselProps = {
  opts?: CarouselOptions
  plugins?: CarouselPlugin
  orientation?: 'horizontal' | 'vertical'
  setApi?: (api: CarouselApi) => void
}

type CarouselContextProps = {
  carouselRef: ReturnType<typeof useEmblaCarousel>[0]
  api: ReturnType<typeof useEmblaCarousel>[1]
  scrollPrev: () => void
  scrollNext: () => void
  canScrollPrev: boolean
  canScrollNext: boolean
} & CarouselProps

const CarouselContext = React.createContext<CarouselContextProps | null>(null);

function useCarousel() {
  const context = React.useContext(CarouselContext);

  if (!context) {
    throw new Error('useCarousel must be used within a <Carousel />');
  }

  return context;
}

const buttonVariants = cva(
  // eslint-disable-next-line max-len
  'pd-inline-flex pd-items-center pd-justify-center pd-gap-2 pd-whitespace-nowrap pd-rounded-md pd-text-sm pd-font-medium pd-transition-colors focus-visible:pd-outline-none focus-visible:pd-ring-1 focus-visible:pd-ring-ring disabled:pd-pointer-events-none disabled:pd-opacity-50 [&_svg]:pd-pointer-events-none [&_svg]:pd-size-4 [&_svg]:pd-shrink-0',
  {
    variants: {
      variant: {
        default:
          'pd-bg-primary pd-text-primary-foreground pd-shadow hover:pd-bg-primary/90',
        destructive:
          'pd-bg-destructive pd-text-destructive-foreground pd-shadow-sm hover:pd-bg-destructive/90',
        outline:
          'pd-border pd-border-input pd-bg-background pd-shadow-sm hover:pd-bg-accent hover:pd-text-accent-foreground',
        secondary:
          'pd-bg-secondary pd-text-secondary-foreground pd-shadow-sm hover:pd-bg-secondary/80',
        ghost: 'hover:pd-bg-accent hover:pd-text-accent-foreground',
        link: 'pd-text-primary pd-underline-offset-4 hover:pd-underline',
      },
      size: {
        default: 'pd-h-9 pd-px-4 pd-py-2',
        sm: 'pd-h-8 pd-rounded-md pd-px-3 pd-text-xs',
        lg: 'pd-h-10 pd-rounded-md pd-px-8',
        icon: 'pd-h-9 pd-w-9',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  },
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
  VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({
    className, variant, size, ...props
  }, ref) => {
    const Comp = 'button';
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    );
  },
);
Button.displayName = 'Button';

const Carousel = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & CarouselProps
>(
  (
    {
      orientation = 'horizontal',
      opts,
      setApi,
      plugins,
      className,
      children,
      ...props
    },
    ref,
  ) => {
    const [carouselRef, api] = useEmblaCarousel(
      {
        ...opts,
        axis: orientation === 'horizontal' ? 'x' : 'y',
      },
      plugins,
    );
    const [canScrollPrev, setCanScrollPrev] = React.useState(false);
    const [canScrollNext, setCanScrollNext] = React.useState(false);

    const onSelect = React.useCallback((carouselApi: CarouselApi) => {
      if (!carouselApi) {
        return;
      }

      setCanScrollPrev(carouselApi.canScrollPrev());
      setCanScrollNext(carouselApi.canScrollNext());
    }, []);

    const scrollPrev = React.useCallback(() => {
      api?.scrollPrev();
    }, [api]);

    const scrollNext = React.useCallback(() => {
      api?.scrollNext();
    }, [api]);

    const handleKeyDown = React.useCallback(
      (event: React.KeyboardEvent<HTMLDivElement>) => {
        if (event.key === 'ArrowLeft') {
          event.preventDefault();
          scrollPrev();
        } else if (event.key === 'ArrowRight') {
          event.preventDefault();
          scrollNext();
        }
      },
      [scrollPrev, scrollNext],
    );

    React.useEffect(() => {
      if (!api || !setApi) {
        return;
      }

      setApi(api);
    }, [api, setApi]);

    React.useEffect(() => {
      if (!api) {
        return undefined;
      }

      onSelect(api);
      api.on('reInit', onSelect);
      api.on('select', onSelect);

      return () => {
        api?.off('select', onSelect);
      };
    }, [api, onSelect]);

    return (
      <CarouselContext.Provider
        value={{
          carouselRef,
          api,
          opts,
          orientation:
            orientation || (opts?.axis === 'y' ? 'vertical' : 'horizontal'),
          scrollPrev,
          scrollNext,
          canScrollPrev,
          canScrollNext,
        }}
      >
        <div
          ref={ref}
          onKeyDownCapture={handleKeyDown}
          className={cn('pd-relative', className)}
          role="region"
          aria-roledescription="carousel"
          {...props}
        >
          {children}
        </div>
      </CarouselContext.Provider>
    );
  },
);
Carousel.displayName = 'Carousel';

const CarouselContent = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => {
  const { carouselRef, orientation } = useCarousel();

  return (
    <div ref={carouselRef} className="pd-overflow-hidden">
      <div
        ref={ref}
        className={cn(
          'pd-flex pd-gap-4',
          orientation === 'horizontal' ? '-pd-ml-4' : '-pd-mt-4 pd-flex-col',
          className,
        )}
        {...props}
      />
    </div>
  );
});
CarouselContent.displayName = 'CarouselContent';

const CarouselItem = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => {
  const { orientation } = useCarousel();

  return (
    <div
      ref={ref}
      role="group"
      aria-roledescription="slide"
      className={cn(
        'pd-min-w-0 pd-shrink-0 pd-grow-0 ',
        orientation === 'horizontal' ? 'pd-pl-4' : 'pd-pt-4',
        className,
      )}
      {...props}
    />
  );
});
CarouselItem.displayName = 'CarouselItem';

const CarouselPrevious = React.forwardRef<
  HTMLButtonElement,
  React.ComponentProps<typeof Button>
>(({
  className, variant = 'outline', size = 'icon', ...props
}, ref) => {
  const { orientation, scrollPrev, canScrollPrev } = useCarousel();

  return (
    <Button
      ref={ref}
      variant={variant}
      size={size}
      className={cn(
        'pd-rounded-full pd-p-2',
        orientation === 'horizontal'
          ? '-pd-left-12 pd-top-1/2'
          : '-pd-top-12 pd-left-1/2 -pd-translate-x-1/2 pd-rotate-90',
        className,
      )}
      disabled={!canScrollPrev}
      onClick={(e: React.MouseEvent<HTMLButtonElement>) => {
        e?.preventDefault();
        scrollPrev();
      }}
      {...props}
    >
      <CaretLeft className="pd-h-4 pd-w-4" />
      {/* <span className="pd-sr-only">Previous slide</span> */}
    </Button>
  );
});
CarouselPrevious.displayName = 'CarouselPrevious';

const CarouselNext = React.forwardRef<
  HTMLButtonElement,
  React.ComponentProps<typeof Button>
>(({
  className, variant = 'outline', size = 'icon', ...props
}, ref) => {
  const { orientation, scrollNext, canScrollNext } = useCarousel();

  return (
    <Button
      ref={ref}
      variant={variant}
      size={size}
      className={cn(
        'pd-rounded-full pd-p-2',
        orientation === 'horizontal'
          ? '-pd-right-12 pd-top-1/2'
          : '-pd-bottom-12 pd-left-1/2 -pd-translate-x-1/2 pd-rotate-90',
        className,
      )}
      disabled={!canScrollNext}
      onClick={(e: React.MouseEvent<HTMLButtonElement>) => {
        e.preventDefault();
        scrollNext();
      }}
      {...props}
    >
      <CaretRight className="pd-h-4 pd-w-4" />
      {/* <span className="pd-sr-only">Next slide</span> */}
    </Button>
  );
});
CarouselNext.displayName = 'CarouselNext';

const Card = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn(
      'pd-rounded-xl pd-border pd-bg-card pd-text-card-foreground pd-shadow',
      className,
    )}
    {...props}
  />
));
Card.displayName = 'Card';

const CardContent = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div ref={ref} className={cn('pd-pt-0', className)} {...props} />
));
CardContent.displayName = 'CardContent';

export {
  // eslint-disable-next-line comma-dangle
  Card, CardContent, Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious, type CarouselApi
};
