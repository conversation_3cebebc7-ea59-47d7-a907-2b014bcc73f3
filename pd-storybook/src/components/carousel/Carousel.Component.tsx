import React, { useRef } from 'react';

import { cn } from '../../utils/Cn.Util';
import { IconImporter } from '../iconImporter/IconImporter.Component';
import { CircleLoader } from '../loaders/CircleLoader.Component';

export interface ImageData {
  id: string;
  url: string;
  name: string;
  processing?: boolean;
}

interface CarouselComponentProps {
  images: ImageData[];
  className?: string;
  onRemoveImage?: (id: string) => void;
  onAddImages?: () => void;
  canScrollLeft?: boolean;
  canScrollRight?: boolean;
  onScrollLeft?: () => void;
  onScrollRight?: () => void;
  addImageEnabled?: boolean;
  containerRef?: React.RefObject<HTMLDivElement>;
}

export function CarouselComponent({
  images = [],
  className,
  onRemoveImage,
  onAddImages,
  canScrollLeft = false,
  canScrollRight = false,
  onScrollLeft,
  onScrollRight,
  addImageEnabled = true,
  containerRef,
}: CarouselComponentProps) {
  const defaultRef = useRef<HTMLDivElement>(null);
  const scrollContainerRef = containerRef || defaultRef;

  return (
    <div className={cn('pd-flex pd-flex-col pd-gap-4 pd-flex-1 pd-w-full', className)}>
      <div className="pd-flex pd-items-center pd-gap-2 pd-w-full">
        {addImageEnabled && (
          <div
            className={`
              pd-w-24 pd-h-24 pd-border-2 pd-border-dashed pd-border-[#DFE3E8] pd-rounded-lg 
              pd-flex pd-items-center pd-justify-center pd-text-xs pd-cursor-pointer 
              pd-flex-shrink-0 hover:pd-text-[#0077B6] hover:pd-border-[#0077B6] 
              pd-transition-all pd-ease-in-out pd-duration-200
            `}
            onClick={onAddImages}
          >
            <span>Seleccionar</span>
          </div>
        )}

        <div className="pd-flex-1 pd-relative pd-overflow-hidden">
          {canScrollLeft && (
            <button
              type="button"
              onClick={onScrollLeft}
              className={`
                pd-absolute pd-left-0 pd-top-1/2 pd--translate-y-1/2 pd-z-10 
                pd-bg-white pd-bg-opacity-75 hover:pd-bg-opacity-100 pd-p-1 
                pd-rounded-full pd-shadow-lg pd-cursor-pointer pd-border pd-border-[#DFE3E8]
              `}
              aria-label="Scroll left"
            >
              <IconImporter name="caretLeft" className="pd-w-5 pd-h-5 pd-text-[#323D52]" />
            </button>
          )}
          <div
            ref={scrollContainerRef}
            className="pd-flex pd-gap-2 pd-pb-0.5 pd-overflow-x-auto pd-hide-scrollbar"
          >
            {images.map((img, index) => (
              <div key={img.id} className="pd-relative pd-w-24 pd-h-24 pd-rounded-lg pd-overflow-hidden pd-flex-shrink-0">
                {img.processing ? (
                  <div className='pd-bg-white pd-w-full pd-h-full pd-flex pd-flex-col pd-gap-2 pd-items-center pd-justify-center'>
                    <CircleLoader size='md' className='!pd-w-8 !pd-h-8'/>
                    <div className='pd-text-[10px] pd-text-[#505D75]'>Procesando...</div>
                  </div>
                ) : (
                  <>
                    <img src={img.url} alt={img.name} className="pd-w-full pd-h-full pd-object-cover" />
                    {onRemoveImage && (
                      <button
                        onClick={() => onRemoveImage(img.id)}
                        className={`
                          pd-absolute pd-top-0.5 pd-right-0.5 pd-bg-black pd-bg-opacity-60 
                          pd-text-white pd-border-none pd-rounded-full pd-w-5 pd-h-5 pd-text-sm 
                          pd-leading-none pd-cursor-pointer pd-flex pd-items-center pd-justify-center
                        `}
                      >
                        <IconImporter name='x' className="pd-w-3 pd-h-3" />
                      </button>
                    )}
                    {index === 0 && (
                      <div className='pd-bg-[#0077B6] pd-opacity-90 pd-text-white pd-text-xs pd-px-3 pd-py-1
                        pd-rounded-xl pd-absolute pd-bottom-1 pd-left-1/2 pd-transform pd--translate-x-1/2'>
                        Portada
                      </div>
                    )}
                  </>
                )}
              </div>
            ))}
          </div>
          {canScrollRight && (
            <button
              type="button"
              onClick={onScrollRight}
              className={`
                pd-absolute pd-right-0 pd-top-1/2 pd--translate-y-1/2 pd-z-10 
                pd-bg-white pd-bg-opacity-75 hover:pd-bg-opacity-100 pd-p-1 
                pd-rounded-full pd-shadow-lg pd-cursor-pointer pd-border pd-border-[#DFE3E8]
              `}
              aria-label="Scroll right"
            >
              <IconImporter name="caretRight" className="pd-w-5 pd-h-5 pd-text-[#323D52]" />
            </button>
          )}
        </div>
      </div>
    </div>
  );
}
