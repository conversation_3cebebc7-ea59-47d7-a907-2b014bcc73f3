import React from 'react';

interface ThreeDotsLoaderProps {
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export const ThreeDotsLoader: React.FC<ThreeDotsLoaderProps> = ({ size = 'md', className = '' }) => {
  const sizeClasses = {
    sm: 'pd-w-1 pd-h-1',
    md: 'pd-w-2 pd-h-2',
    lg: 'pd-w-4 pd-h-4',
  };

  return (
    <div className={`pd-flex pd-flex-row pd-min-h-4 pd-items-end ${size === 'lg' ? 'pd-gap-2' : 'pd-gap-1'} ${className}`}>
      <div className={`${sizeClasses[size]} pd-rounded-full pd-bg-dark-400 pd-animate-bounce [animation-delay:.7s]`}></div>
      <div className={`${sizeClasses[size]} pd-rounded-full pd-bg-dark-400 pd-animate-bounce [animation-delay:.3s]`}></div>
      <div className={`${sizeClasses[size]} pd-rounded-full pd-bg-dark-400 pd-animate-bounce [animation-delay:.7s]`}></div>
    </div>
  );
};
