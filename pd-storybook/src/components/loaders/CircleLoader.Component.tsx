import React from 'react';

interface CircleLoaderProps {
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export const CircleLoader: React.FC<CircleLoaderProps> = ({ size = 'md', className = '' }) => {
  const sizeClasses = {
    sm: 'pd-w-4 pd-h-4',
    md: 'pd-w-10 pd-h-10',
    lg: 'pd-w-16 pd-h-16',
  };

  return (
    <div className={`${sizeClasses[size]} pd-border-4 pd-border-t-primary pd-border-dark-300 pd-rounded-full pd-animate-spin ${className}`} />
  );
};
