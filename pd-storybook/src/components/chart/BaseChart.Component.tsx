import * as React from 'react';
import * as RechartsPrimitive from 'recharts';

import { cn } from '../../utils/Cn.Util';

const THEMES = { light: '', dark: '.dark' } as const;

export type ChartConfig = {
    [k in string]: {
        label?: React.ReactNode
        icon?: React.ComponentType
    } & (
        | { color?: string; theme?: never }
        | { color?: never; theme: Record<keyof typeof THEMES, string> }
    )
}

type ChartContextProps = {
    config: ChartConfig
}

const ChartContext = React.createContext<ChartContextProps | null>(null);

function useChart() {
  const context = React.useContext(ChartContext);

  if (!context) {
    throw new Error('useChart must be used within a <ChartContainer />');
  }

  return context;
}

function getPayloadConfigFromPayload(
  config: ChartConfig,
  payload: unknown,
  key: string,
) {
  if (typeof payload !== 'object' || payload === null) {
    return undefined;
  }

  const payloadPayload = 'payload' in payload
    && typeof payload.payload === 'object'
    && payload.payload !== null
    ? payload.payload
    : undefined;

  let configLabelKey: string = key;

  if (
    key in payload
    && typeof payload[key as keyof typeof payload] === 'string'
  ) {
    configLabelKey = payload[key as keyof typeof payload] as string;
  } else if (
    payloadPayload
    && key in payloadPayload
    && typeof payloadPayload[key as keyof typeof payloadPayload] === 'string'
  ) {
    configLabelKey = payloadPayload[
      key as keyof typeof payloadPayload
    ] as string;
  }

  return configLabelKey in config
    ? config[configLabelKey]
    : config[key as keyof typeof config];
}

const ChartStyle = ({ id, config }: { id: string; config: ChartConfig }) => {
  const colorConfig = Object.entries(config).filter(
    ([, conf]) => conf.theme || conf.color,
  );

  if (!colorConfig.length) {
    return null;
  }

  return (
    <style
      dangerouslySetInnerHTML={{
        __html: Object.entries(THEMES)
          .map(
            ([theme, prefix]) => `
              ${prefix} [data-chart=${id}] {
              ${colorConfig
              .map(([key, itemConfig]) => {
                const color = itemConfig.theme?.[theme as keyof typeof itemConfig.theme] || itemConfig.color;
                return color ? `  --color-${key}: ${color};` : null;
              })
              .join('\n')}
            }`,
          )
          .join('\n'),
      }}
    />
  );
};

const ChartContainer = React.forwardRef<
    HTMLDivElement,
    React.ComponentProps<'div'> & {
        config: ChartConfig
        children: React.ComponentProps<
            typeof RechartsPrimitive.ResponsiveContainer
        >['children']
    }
>(({
  id, className, children, config, ...props
}, ref) => {
  const uniqueId = React.useId();
  const chartId = `chart-${id || uniqueId.replace(/:/g, '')}`;

  return (
    <ChartContext.Provider value={{ config }}>
      <div
        data-chart={chartId}
        ref={ref}
        className={cn(
          // eslint-disable-next-line max-len
          "pd-flex pd-aspect-video pd-justify-center pd-text-xs [&_.recharts-cartesian-axis-tick_text]:pd-fill-muted-foreground [&_.recharts-cartesian-grid_line[stroke='#ccc']]:pd-stroke-border/50 [&_.recharts-curve.recharts-tooltip-cursor]:pd-stroke-border [&_.recharts-dot[stroke='#fff']]:pd-stroke-transparent [&_.recharts-layer]:pd-outline-none [&_.recharts-polar-grid_[stroke='#ccc']]:pd-stroke-border [&_.recharts-radial-bar-background-sector]:pd-fill-muted [&_.recharts-rectangle.recharts-tooltip-cursor]:pd-fill-muted [&_.recharts-reference-line_[stroke='#ccc']]:pd-stroke-border [&_.recharts-sector[stroke='#fff']]:pd-stroke-transparent [&_.recharts-sector]:pd-outline-none [&_.recharts-surface]:pd-outline-none",
          className,
        )}
        {...props}
            >
        <ChartStyle id={chartId} config={config} />
        <RechartsPrimitive.ResponsiveContainer>
          {children}
        </RechartsPrimitive.ResponsiveContainer>
      </div>
    </ChartContext.Provider>
  );
});
ChartContainer.displayName = 'Chart';

const ChartTooltip = RechartsPrimitive.Tooltip;

const ChartTooltipContent = React.forwardRef<
    HTMLDivElement,
    React.ComponentProps<typeof RechartsPrimitive.Tooltip> &
    React.ComponentProps<'div'> & {
        hideLabel?: boolean
        hideIndicator?: boolean
        indicator?: 'line' | 'dot' | 'dashed'
        nameKey?: string
        labelKey?: string
    }
>(
  (
    {
      active,
      payload,
      className,
      indicator = 'dot',
      hideLabel = false,
      hideIndicator = false,
      label,
      labelFormatter,
      labelClassName,
      formatter,
      color,
      nameKey,
      labelKey,
    },
    ref,
  ) => {
    const { config } = useChart();

    const tooltipLabel = React.useMemo(() => {
      if (hideLabel || !payload?.length) {
        return null;
      }

      const [item] = payload;
      const key = `${labelKey || item?.dataKey || item?.name || 'value'}`;
      const itemConfig = getPayloadConfigFromPayload(config, item, key);
      const value = !labelKey && typeof label === 'string'
        ? config[label as keyof typeof config]?.label || label
        : itemConfig?.label;

      if (labelFormatter) {
        return (
          <div className={cn('pd-font-medium', labelClassName)}>
            {labelFormatter(value, payload)}
          </div>
        );
      }

      if (!value) {
        return null;
      }

      return <div className={cn('pd-font-medium', labelClassName)}>{value}</div>;
    }, [
      label,
      labelFormatter,
      payload,
      hideLabel,
      labelClassName,
      config,
      labelKey,
    ]);

    if (!active || !payload?.length) {
      return null;
    }

    const nestLabel = payload.length === 1 && indicator !== 'dot';

    return (
      <div
        ref={ref}
        className={cn(
          'pd-grid pd-min-w-[8rem] pd-items-start pd-gap-1.5 pd-rounded-lg pd-border pd-border-border/50 pd-bg-white pd-px-2.5 pd-py-1.5 pd-text-xs pd-shadow-xl',
          className,
        )}
            >
        {!nestLabel ? tooltipLabel : null}
        <div className="pd-grid pd-gap-1.5">
          {payload.map((item, index) => {
            const key = `${nameKey || item.name || item.dataKey || 'value'}`;
            const itemConfig = getPayloadConfigFromPayload(config, item, key);
            const indicatorColor = color || item.payload.fill || item.color;

            return (
              <div
                key={item.dataKey}
                className={cn(
                  'pd-flex pd-w-full pd-flex-wrap pd-items-stretch pd-gap-2 [&>svg]:pd-h-2.5 [&>svg]:pd-w-2.5 [&>svg]:pd-text-muted-foreground',
                  indicator === 'dot' && 'pd-items-center',
                )}
                >
                {formatter && item?.value !== undefined && item.name ? (
                  formatter(item.value, item.name, item, index, item.payload)
                ) : (
                  <>
                    {itemConfig?.icon ? (
                      <itemConfig.icon />
                    ) : (
                      !hideIndicator && (
                      <div
                        className={cn(
                          'pd-shrink-0 pd-rounded-[2px] pd-border-[--color-border] pd-bg-[--color-bg]',
                          {
                            'pd-h-2.5 pd-w-2.5': indicator === 'dot',
                            'pd-w-1': indicator === 'line',
                            'pd-w-0 pd-border-[1.5px] pd-border-dashed pd-bg-transparent':
                            indicator === 'dashed',
                            'pd-my-0.5': nestLabel && indicator === 'dashed',
                          },
                        )}
                        style={
                                {
                                  '--color-bg': indicatorColor,
                                  '--color-border': indicatorColor,
                                } as React.CSSProperties
                            }
                        />
                      )
                    )}
                    <div
                      className={cn(
                        'pd-flex pd-flex-1 pd-justify-between pd-leading-none',
                        nestLabel ? 'pd-items-end' : 'pd-items-center',
                      )}
                        >
                      <div className="pd-grid pd-gap-1.5">
                        {nestLabel ? tooltipLabel : null}
                        <span className="pd-text-muted-foreground">
                          {itemConfig?.label || item.name}
                        </span>
                      </div>
                      {item.value && (
                      <span className="pd-font-mono pd-font-medium pd-tabular-nums pd-text-foreground">
                        {item.value.toLocaleString()}
                      </span>
                      )}
                    </div>
                  </>
                )}
              </div>
            );
          })}
        </div>
      </div>
    );
  },
);
ChartTooltipContent.displayName = 'ChartTooltip';

const ChartLegend = RechartsPrimitive.Legend;

const ChartLegendContent = React.forwardRef<
    HTMLDivElement,
    React.ComponentProps<'div'> &
    Pick<RechartsPrimitive.LegendProps, 'payload' | 'verticalAlign'> & {
        hideIcon?: boolean
        nameKey?: string
    }
>(
  (
    {
      className, hideIcon = false, payload, verticalAlign = 'bottom', nameKey,
    },
    ref,
  ) => {
    const { config } = useChart();

    if (!payload?.length) {
      return null;
    }

    return (
      <div
        ref={ref}
        className={cn(
          'pd-flex pd-items-center pd-justify-center pd-gap-4',
          verticalAlign === 'top' ? 'pd-pb-3' : 'pd-pt-3',
          className,
        )}
            >
        {payload.map((item) => {
          const key = `${nameKey || item.dataKey || 'value'}`;
          const itemConfig = getPayloadConfigFromPayload(config, item, key);

          return (
            <div
              key={item.value}
              className={cn(
                'pd-flex pd-items-center pd-gap-1.5 [&>svg]:pd-h-3 [&>svg]:pd-w-3 [&>svg]:pd-text-muted-foreground',
              )}
                        >
              {itemConfig?.icon && !hideIcon ? (
                <itemConfig.icon />
              ) : (
                <div
                  className="pd-h-2 pd-w-2 pd-shrink-0 pd-rounded-[2px]"
                  style={{
                    backgroundColor: item.color,
                  }}
                                />
              )}
              {itemConfig?.label}
            </div>
          );
        })}
      </div>
    );
  },
);
ChartLegendContent.displayName = 'ChartLegend';

export {
  ChartContainer,
  ChartLegend,
  ChartLegendContent,
  ChartStyle,
  ChartTooltip,
  ChartTooltipContent,
};
