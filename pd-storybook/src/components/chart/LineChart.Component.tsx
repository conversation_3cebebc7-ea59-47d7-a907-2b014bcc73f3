import { useMemo } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Line, LineChart, XAxis,
} from 'recharts';

import Theme from '../../configurations/Theme.Configuration';

import {
  ChartConfig,
  ChartContainer, ChartTooltip, ChartTooltipContent,
} from './BaseChart.Component';

interface LineSeries {
    key: string;
    label: string;
    color: string;
}

interface LineChartComponentProps {
    data: Record<string, number | string>[];
    series: LineSeries[];
    lineType?: 'natural' | 'linear' | 'step' | 'monotone';
    lineDot?: boolean;
    dotLabel?: boolean;
    dotLabelPosition?: 'top' | 'bottom' | 'left' | 'right';
    dotLabelOffset?: number;
    dotLabelFontSize?: number;
    xAxisKey?: string;
    showXAxis?: boolean;
    tickStyles?: Record<string, string | number>;
    cartesianGrid?: boolean;
    tickLength?: number;
    interval?: number;
    marginRight?: number;
    marginLeft?: number;
    marginTop?: number;
    marginBottom?: number;
    className?: string;
    isAnimationActive?: boolean;
}

export const LineChartComponent: React.FC<LineChartComponentProps> = (props) => {
  const {
    data,
    series,
    lineType = 'natural',
    lineDot = false,
    dotLabel = false,
    dotLabelPosition = 'top',
    dotLabelOffset = 12,
    dotLabelFontSize = 12,
    xAxisKey = '',
    showXAxis = true,
    tickStyles,
    cartesianGrid = false,
    tickLength,
    interval,
    marginRight = 0,
    marginLeft = 0,
    marginTop = 0,
    marginBottom = 0,
    className = '',
    isAnimationActive = true,
  } = props;

  const chartConfig = useMemo(() => Object.fromEntries(
    series.map((s) => [
      s.key,
      { label: s.label, color: s.color || Theme.colors.primary },
    ]),
  ) as ChartConfig, [series]);

  const containerClassName = useMemo(() => `pd-min-h-[200px] pd-w-full ${className}`, [className]);

  return (
    <ChartContainer
      config={chartConfig}
      className={containerClassName}
      >
      <LineChart
        accessibilityLayer
        data={data}
        margin={{
          left: marginLeft,
          right: marginRight,
          top: marginTop,
          bottom: marginBottom,
        }}
        >

        {cartesianGrid && (<CartesianGrid vertical={false} />)}

        {showXAxis && <XAxis
          dataKey={xAxisKey}
          tickLine={false}
          axisLine={false}
          tickMargin={8}
          minTickGap={2}
          tickFormatter={(value) => (typeof value === 'string' && tickLength
            ? value.slice(0, tickLength)
            : value)}
          tick={tickStyles}
          interval={interval}
              />}

        <ChartTooltip
          cursor={false}
          content={<ChartTooltipContent hideLabel />}
                />
        {series.map((s) => (
          <Line
            key={s.key}
            dataKey={s.key}
            type={lineType}
            stroke={s.color}
            strokeWidth={2}
            dot={lineDot}
            isAnimationActive={isAnimationActive}
            activeDot={{
              r: 6,
            }}
            >
            {dotLabel && <LabelList
              position={dotLabelPosition}
              offset={dotLabelOffset}
              className="fill-foreground"
              fontSize={dotLabelFontSize}
            /> }
          </Line>
        ))}
      </LineChart>
    </ChartContainer>
  );
};
