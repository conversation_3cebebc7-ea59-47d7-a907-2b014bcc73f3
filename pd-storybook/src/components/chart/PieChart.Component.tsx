import React, { useMemo } from 'react';
import {
  <PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
} from 'recharts';

import Theme from '../../configurations/Theme.Configuration';

import {
  ChartConfig,
  ChartContainer,
  ChartLegendContent,
  ChartTooltip,
  ChartTooltipContent,
} from './BaseChart.Component';

interface PieSeries {
  key: string;
  label?: string;
  color?: string;
}

interface PieChartComponentProps {
  data: Record<string, number | string>[];
  series: PieSeries[];
  dataKey?: string;
  nameKey?: string;
  innerRadius?: number | string;
  outerRadius?: number | string;
  showLegend?: boolean;
  legendVerticalAlign?: 'top' | 'bottom';
  className?: string;
  marginRight?: number;
  marginLeft?: number;
  marginTop?: number;
  marginBottom?: number;
  showLabel?: boolean;
  showLabelLine?: boolean;
  isAnimationActive?: boolean;
}

export const PieChartComponent: React.FC<PieChartComponentProps> = (props) => {
  const {
    data,
    series,
    dataKey = 'value',
    nameKey = 'name',
    innerRadius = 0,
    outerRadius = '80%',
    showLegend = false,
    legendVerticalAlign = 'bottom',
    className = '',
    marginRight = 0,
    marginLeft = 0,
    marginTop = 0,
    marginBottom = 0,
    showLabel = false,
    showLabelLine = false,
    isAnimationActive = true,
  } = props;

  const chartConfig = useMemo(() => Object.fromEntries(
    series.map((s) => [
      s.key,
      { label: s.label, color: s.color || Theme.colors.primary },
    ]),
  ) as ChartConfig, [series]);

  const containerClassName = useMemo(() => `pd-min-h-[200px] pd-w-full ${className}`, [className]);

  return (
    <ChartContainer config={chartConfig} className={containerClassName}>
      <PieChart
        margin={{
          left: marginLeft,
          right: marginRight,
          top: marginTop,
          bottom: marginBottom,
        }}
      >
        <Pie
          data={data}
          dataKey={dataKey}
          nameKey={nameKey}
          innerRadius={innerRadius}
          outerRadius={outerRadius}
          labelLine={showLabelLine}
          label={showLabel}
          isAnimationActive={isAnimationActive}
        >
          {data.map((_, idx) => {
            const s = series[idx % series.length];
            return (
              <Cell
                key={`cell-${idx}`}
                fill={s.color || Theme.colors.primary}
              />
            );
          })}
        </Pie>
        <ChartTooltip cursor={false} content={<ChartTooltipContent nameKey={nameKey} />} />
        {showLegend && (
          <Legend
            verticalAlign={legendVerticalAlign}
            content={<ChartLegendContent nameKey={nameKey} />}
          />
        )}
      </PieChart>
    </ChartContainer>
  );
};
