import { useMemo } from 'react';
import {
  Area,
  AreaChart,
  CartesianGrid,
  LabelList,
  XAxis, YAxis,
} from 'recharts';

import Theme from '../../configurations/Theme.Configuration';

import {
  ChartConfig, ChartContainer, ChartTooltip, ChartTooltipContent,
} from './BaseChart.Component';

interface AreaSeries {
  key: string;
  label: string;
  color?: string;
  fillOpacity?: number;
}

interface AreaChartComponentProps {
  data: Record<string, number | string>[];
  series: AreaSeries[];
  xAxisKey?: string;
  yAxisKey?: string;
  tickLength?: number;
  tickStyles?: Record<string, string | number>;
  tickWidth?: number;
  cartesianGrid?: boolean;
  showXAxis?: boolean;
  showYAxis?: boolean;
  className?: string;
  marginRight?: number;
  marginLeft?: number;
  marginTop?: number;
  marginBottom?: number;
  areaType?: 'natural' | 'linear' | 'step' | 'monotone';
  showDot?: boolean;
  dotLabel?: boolean;
  dotLabelPosition?: 'top' | 'bottom' | 'left' | 'right';
  dotLabelOffset?: number;
  dotLabelFontSize?: number;
  isAnimationActive?: boolean;
}

export const AreaChartComponent: React.FC<AreaChartComponentProps> = (props) => {
  const {
    data,
    series,
    xAxisKey = '',
    yAxisKey,
    tickLength,
    tickStyles,
    tickWidth,
    cartesianGrid = false,
    showXAxis = true,
    showYAxis = true,
    className = '',
    marginRight = 0,
    marginLeft = 0,
    marginTop = 0,
    marginBottom = 0,
    areaType = 'monotone',
    showDot = false,
    dotLabel = false,
    dotLabelPosition = 'top',
    dotLabelOffset = 12,
    dotLabelFontSize = 12,
    isAnimationActive = true,
  } = props;

  const chartConfig = useMemo(() => Object.fromEntries(
    series.map((s) => [
      s.key,
      { label: s.label, color: s.color || Theme.colors.primary },
    ]),
  ) as ChartConfig, [series]);

  const containerClassName = useMemo(
    () => `pd-min-h-[200px] pd-w-full ${className}`,
    [className],
  );

  return (
    <ChartContainer config={chartConfig} className={containerClassName}>
      <AreaChart
        data={data}
        margin={{
          left: marginLeft,
          right: marginRight,
          top: marginTop,
          bottom: marginBottom,
        }}
      >
        {cartesianGrid && (<CartesianGrid vertical={false} />)}
        {showXAxis && (
          <XAxis
            dataKey={xAxisKey}
            tickLine={false}
            axisLine={false}
            tickMargin={8}
            minTickGap={2}
            tickFormatter={(value) => (typeof value === 'string' && tickLength
              ? value.slice(0, tickLength)
              : value)}
            width={tickWidth || undefined}
            tick={tickStyles}
          />
        )}
        {showYAxis && (
          <YAxis
            dataKey={yAxisKey}
            tickLine={false}
            axisLine={false}
            tick={tickStyles}
          />
        )}
        <ChartTooltip cursor={false} content={<ChartTooltipContent hideLabel />} />
        {series.map((s) => (
          <Area
            key={s.key}
            dataKey={s.key}
            stroke={s.color || Theme.colors.primary}
            fill={s.color || Theme.colors.primary}
            fillOpacity={s.fillOpacity ?? 0.2}
            type={areaType}
            strokeWidth={2}
            dot={showDot}
            activeDot={{
              r: 6,
            }}
            isAnimationActive={isAnimationActive}
          >
            {dotLabel && (
              <LabelList
                position={dotLabelPosition}
                offset={dotLabelOffset}
                className="fill-foreground"
                fontSize={dotLabelFontSize}
              />
            )}
          </Area>
        ))}
      </AreaChart>
    </ChartContainer>
  );
};
