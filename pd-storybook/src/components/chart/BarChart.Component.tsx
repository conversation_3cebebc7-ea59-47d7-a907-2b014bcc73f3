import { useMemo } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>, CartesianGrid,
  XAxis,
  YAxis,
} from 'recharts';

import Theme from '../../configurations/Theme.Configuration';

import {
  ChartConfig, ChartContainer, ChartTooltip, ChartTooltipContent,
} from './BaseChart.Component';

interface BarSeries {
    key: string;
    label: string;
    color?: string;
}

interface BarChartComponentProps {
    data: Record<string, number | string>[];
    series: BarSeries[];
    xAxisKey?: string;
    yAxisKey?: string;
    tickLength?: number;
    tickStyles?: Record<string, string | number>;
    tickWidth?: number;
    cartesianGrid?:boolean;
    vertical?: boolean;
    isAnimationActive?: boolean;
    className?: string;
}

export const BarChartComponent: React.FC<BarChartComponentProps> = (props) => {
  const {
    data,
    series,
    xAxisKey,
    yAxisKey,
    tickLength,
    tickStyles,
    tickWidth,
    cartesianGrid = false,
    vertical = false,
    isAnimationActive = true,
    className = '',
  } = props;

  const chartConfig = useMemo(() => Object.fromEntries(
    series.map((s) => [
      s.key,
      { label: s.label, color: s.color },
    ]),
  ) as ChartConfig, [series]);

  const containerClassName = useMemo(() => `pd-min-h-[200px] pd-w-full ${className}`, [className]);

  return (
    <ChartContainer config={chartConfig} className={containerClassName}>
      <BarChart
        accessibilityLayer
        data={data}
        layout={vertical ? 'vertical' : undefined}
            >
        {cartesianGrid && (<CartesianGrid vertical={false} />)}
        {vertical ? (
          <>
            <XAxis type="number" dataKey={xAxisKey} hide />
            <YAxis
              dataKey={yAxisKey}
              type="category"
              tickLine={false}
              tickMargin={10}
              axisLine={false}
              tickFormatter={(value) => (typeof value === 'string' && tickLength
                ? value.slice(0, tickLength)
                : value)}
              width={tickWidth || undefined}
              tick={tickStyles}
            />
          </>
        ) : (
          <>
            <XAxis
              type="category"
              dataKey={xAxisKey}
              tickLine={false}
              tickMargin={10}
              axisLine={false}
              tickFormatter={(value) => (typeof value === 'string' && tickLength
                ? value.slice(0, tickLength)
                : value)}
              width={tickWidth || undefined}
              tick={tickStyles}
            />
            <YAxis type="number" dataKey={yAxisKey} hide />
          </>
        )}
        <ChartTooltip
          cursor={false}
          content={<ChartTooltipContent hideLabel />}
          />
        {series.map((s) => (
          <Bar isAnimationActive={isAnimationActive} dataKey={s.key} fill={s.color || Theme.colors.primary} key={s.key} radius={5} />
        ))}
      </BarChart>
    </ChartContainer>
  );
};
