import { fireEvent, render, screen } from '@testing-library/react';
import React from 'react';

import { ForwardedTextAreaInput, TextAreaInput, TextAreaProps } from './TextAreaInput.Component';

describe('TextAreaInput Component', () => {
  const defaultProps: TextAreaProps = {
    name: 'text-area',
    inputClassName: 'custom-text-area',
    placeholder: 'Enter text',
    isRequired: true,
    onChange: jest.fn(),
  };

  it('must to render correctly with the default propertires', () => {
    render(<TextAreaInput {...defaultProps} />);

    const textAreaElement = screen.getByPlaceholderText('Enter text');
    expect(textAreaElement).toBeInTheDocument();
    expect(textAreaElement).toHaveClass('custom-text-area');
    expect(textAreaElement).toHaveAttribute('name', 'text-area');
    expect(textAreaElement).toBeRequired();
  });

  it('must to call the onChange function when the textarea value change', () => {
    const handleChange = jest.fn();
    render(<TextAreaInput {...defaultProps} onChange={handleChange} />);

    const textAreaElement = screen.getByPlaceholderText('Enter text');
    fireEvent.change(textAreaElement, { target: { value: 'new value' } });
    expect(handleChange).toHaveBeenCalled();
    expect(textAreaElement).toHaveValue('new value');
  });

  it('must to apply the custom classes', () => {
    render(<TextAreaInput {...defaultProps} inputClassName="custom-class" />);

    const textAreaElement = screen.getByPlaceholderText('Enter text');
    expect(textAreaElement).toHaveClass('custom-class');
  });

  it('must to handle the additional props', () => {
    render(
      <TextAreaInput
        {...defaultProps}
        data-testid="custom-text-area"
        aria-label="Text Area"
      />,
    );

    const textAreaElement = screen.getByTestId('custom-text-area');
    expect(textAreaElement).toBeInTheDocument();
    expect(textAreaElement).toHaveAttribute('aria-label', 'Text Area');
  });

  it('must apply rows and columns properties correctly', () => {
    render(
      <TextAreaInput
        {...defaultProps}
        rows={5}
      />,
    );

    const textAreaElement = screen.getByPlaceholderText('Enter text');
    expect(textAreaElement).toHaveAttribute('rows', '5');
  });

  it('must render as non-required when isRequired is false', () => {
    render(<TextAreaInput {...defaultProps} isRequired={false} />);

    const textAreaElement = screen.getByPlaceholderText('Enter text');
    expect(textAreaElement).not.toBeRequired();
  });

  it('must handle disabled state correctly', () => {
    render(<TextAreaInput {...defaultProps} disabled />);

    const textAreaElement = screen.getByPlaceholderText('Enter text');
    expect(textAreaElement).toBeDisabled();
  });
});

describe('ForwardedTextAreaInput Component', () => {
  const defaultProps: TextAreaProps = {
    name: 'forwarded-text-area',
    inputClassName: 'custom-text-area',
    placeholder: 'Enter text here',
    isRequired: true,
    onChange: jest.fn(),
  };

  it('must render correctly with a forwarded ref', () => {
    const ref = React.createRef<HTMLTextAreaElement>();
    render(<ForwardedTextAreaInput {...defaultProps} ref={ref} />);

    const textAreaElement = screen.getByPlaceholderText('Enter text here');
    expect(textAreaElement).toBeInTheDocument();
    expect(ref.current).toBe(textAreaElement);
  });

  it('must apply all props correctly', () => {
    render(
      <ForwardedTextAreaInput
        {...defaultProps}
        data-testid="forwarded-textarea"
        rows={3}
      />,
    );

    const textAreaElement = screen.getByTestId('forwarded-textarea');
    expect(textAreaElement).toHaveAttribute('rows', '3');
    expect(textAreaElement).toBeRequired();
    expect(textAreaElement).toHaveClass('custom-text-area');
  });
});
