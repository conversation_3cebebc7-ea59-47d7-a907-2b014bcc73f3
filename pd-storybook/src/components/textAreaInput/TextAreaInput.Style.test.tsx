import React from 'react';
import { render } from '@testing-library/react';

import { StyledTextArea } from './TextAreaInput.Style';

describe('StyledTextArea Snapshots', () => {
  it('renders StyledTextArea correctly', () => {
    const { asFragment } = render(<StyledTextArea />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders StyledTextArea with custom className correctly', () => {
    const { asFragment } = render(<StyledTextArea className="custom-text-area-class" />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders StyledTextArea with additional props correctly', () => {
    const { asFragment } = render(<StyledTextArea data-testid="custom-text-area" aria-label="Text Area" />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders StyledTextArea with error state correctly', () => {
    const { asFragment } = render(<StyledTextArea error />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders StyledTextArea without error state correctly', () => {
    const { asFragment } = render(<StyledTextArea error={false} />);
    expect(asFragment()).toMatchSnapshot();
  });
});
