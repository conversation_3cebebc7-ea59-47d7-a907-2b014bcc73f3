import React from 'react';

import { InputProps } from '../input/Input.Component';

import { StyledTextArea } from './TextAreaInput.Style';

export interface TextAreaProps extends InputProps {
    columns?: number;
    rows?: number;
}

export const ForwardedTextAreaInput = React.forwardRef<HTMLTextAreaElement, TextAreaProps>(({
  inputClassName,
  name,
  placeholder,
  isRequired,
  ...props
}, ref) => (
  <StyledTextArea
    className={inputClassName}
    name={name}
    placeholder={placeholder}
    required={isRequired}
    ref={ref}
    {...props}
    />
));

ForwardedTextAreaInput.displayName = 'Textarea';

export const TextAreaInput: React.FC<TextAreaProps> = ({
  inputClassName,
  name,
  placeholder,
  isRequired,
  ...props
}) => (
  <StyledTextArea
    className={inputClassName}
    name={name}
    placeholder={placeholder}
    required={isRequired}
    {...props}
    />
);
