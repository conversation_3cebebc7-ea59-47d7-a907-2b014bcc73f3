// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`StyledTextArea Snapshots renders StyledTextArea correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  height: 38px;
  width: 100%;
  border-radius: 8px;
  font-size: 13px;
  line-height: 18px;
  color: #1D1F24;
  padding: 0px 12px;
  border: 1px solid #E0E3E8;
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
  text-align: left;
  min-height: 76px;
  padding: 12px;
  resize: none;
  display: block;
  background-color: #FFFFFF;
  border: 1px solid #E0E3E8;
}

.emotion-0:active,
.emotion-0:focus,
.emotion-0::selection,
.emotion-0:focus-visible {
  outline: 1px solid #5D923D;
}

.emotion-0:disabled {
  background-color: #FAFAFA;
  color: #3A3D44;
  cursor: not-allowed;
  opacity: 1;
}

.emotion-0::selection {
  background-color: #CEE4F8;
}

.emotion-0:active,
.emotion-0:focus,
.emotion-0::selection,
.emotion-0:focus-visible {
  outline: 1px solid #0A88ED;
}

<textarea
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`StyledTextArea Snapshots renders StyledTextArea with additional props correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  height: 38px;
  width: 100%;
  border-radius: 8px;
  font-size: 13px;
  line-height: 18px;
  color: #1D1F24;
  padding: 0px 12px;
  border: 1px solid #E0E3E8;
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
  text-align: left;
  min-height: 76px;
  padding: 12px;
  resize: none;
  display: block;
  background-color: #FFFFFF;
  border: 1px solid #E0E3E8;
}

.emotion-0:active,
.emotion-0:focus,
.emotion-0::selection,
.emotion-0:focus-visible {
  outline: 1px solid #5D923D;
}

.emotion-0:disabled {
  background-color: #FAFAFA;
  color: #3A3D44;
  cursor: not-allowed;
  opacity: 1;
}

.emotion-0::selection {
  background-color: #CEE4F8;
}

.emotion-0:active,
.emotion-0:focus,
.emotion-0::selection,
.emotion-0:focus-visible {
  outline: 1px solid #0A88ED;
}

<textarea
    aria-label="Text Area"
    class="emotion-0"
    data-testid="custom-text-area"
  />
</DocumentFragment>
`;

exports[`StyledTextArea Snapshots renders StyledTextArea with custom className correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  height: 38px;
  width: 100%;
  border-radius: 8px;
  font-size: 13px;
  line-height: 18px;
  color: #1D1F24;
  padding: 0px 12px;
  border: 1px solid #E0E3E8;
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
  text-align: left;
  min-height: 76px;
  padding: 12px;
  resize: none;
  display: block;
  background-color: #FFFFFF;
  border: 1px solid #E0E3E8;
}

.emotion-0:active,
.emotion-0:focus,
.emotion-0::selection,
.emotion-0:focus-visible {
  outline: 1px solid #5D923D;
}

.emotion-0:disabled {
  background-color: #FAFAFA;
  color: #3A3D44;
  cursor: not-allowed;
  opacity: 1;
}

.emotion-0::selection {
  background-color: #CEE4F8;
}

.emotion-0:active,
.emotion-0:focus,
.emotion-0::selection,
.emotion-0:focus-visible {
  outline: 1px solid #0A88ED;
}

<textarea
    class="custom-text-area-class emotion-0"
  />
</DocumentFragment>
`;

exports[`StyledTextArea Snapshots renders StyledTextArea with error state correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  height: 38px;
  width: 100%;
  border-radius: 8px;
  font-size: 13px;
  line-height: 18px;
  color: #1D1F24;
  padding: 0px 12px;
  border: 1px solid #E0E3E8;
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
  text-align: left;
  min-height: 76px;
  padding: 12px;
  resize: none;
  display: block;
  background-color: #FFFFFF;
  border: 1px solid #E55D57;
}

.emotion-0:active,
.emotion-0:focus,
.emotion-0::selection,
.emotion-0:focus-visible {
  outline: 1px solid #5D923D;
}

.emotion-0:disabled {
  background-color: #FAFAFA;
  color: #3A3D44;
  cursor: not-allowed;
  opacity: 1;
}

.emotion-0::selection {
  background-color: #CEE4F8;
}

.emotion-0:active,
.emotion-0:focus,
.emotion-0::selection,
.emotion-0:focus-visible {
  outline: 1px solid #E55D57;
}

<textarea
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`StyledTextArea Snapshots renders StyledTextArea without error state correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  height: 38px;
  width: 100%;
  border-radius: 8px;
  font-size: 13px;
  line-height: 18px;
  color: #1D1F24;
  padding: 0px 12px;
  border: 1px solid #E0E3E8;
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
  text-align: left;
  min-height: 76px;
  padding: 12px;
  resize: none;
  display: block;
  background-color: #FFFFFF;
  border: 1px solid #E0E3E8;
}

.emotion-0:active,
.emotion-0:focus,
.emotion-0::selection,
.emotion-0:focus-visible {
  outline: 1px solid #5D923D;
}

.emotion-0:disabled {
  background-color: #FAFAFA;
  color: #3A3D44;
  cursor: not-allowed;
  opacity: 1;
}

.emotion-0::selection {
  background-color: #CEE4F8;
}

.emotion-0:active,
.emotion-0:focus,
.emotion-0::selection,
.emotion-0:focus-visible {
  outline: 1px solid #0A88ED;
}

<textarea
    class="emotion-0"
  />
</DocumentFragment>
`;
