import styled from '@emotion/styled';

import Theme from '../../configurations/Theme.Configuration';
import { BaseInput, StyledInputProps } from '../input/Input.Style';

export const StyledTextArea = styled(BaseInput.withComponent('textarea')) <StyledInputProps>`
  min-height: 76px;
  padding: 12px;
  resize: none;
  display: block;
  background-color: ${Theme.colors.white};
  border: 1px solid ${(props) => (!props.error ? Theme.colors.line : Theme.colors.negative)};

  &:active, &:focus, &::selection, &:focus-visible {
        outline: 1px solid ${(props) => (!props.error ? Theme.colors.secondary : Theme.colors.negative)};
`;
