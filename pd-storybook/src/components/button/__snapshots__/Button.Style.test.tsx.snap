// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`StyledButton renders correctly when disabled 1`] = `
<DocumentFragment>
  .emotion-0 {
  border-radius: 8px;
  letter-spacing: 1px;
  font-weight: 300;
  height: -webkit-fit-content;
  height: -moz-fit-content;
  height: fit-content;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
  border: none;
  font-size: 14px;
  line-height: 20px;
  padding: 7px 14px;
  background-color: #17448d;
  color: #FFFFFF;
}

.emotion-0:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.emotion-0:hover:not(:disabled) {
  background-color: #2B5EC9;
}

<button
    class="emotion-0"
    disabled=""
  >
    Disabled <PERSON><PERSON>
  </button>
</DocumentFragment>
`;

exports[`StyledButton renders correctly with large size 1`] = `
<DocumentFragment>
  .emotion-0 {
  border-radius: 8px;
  letter-spacing: 1px;
  font-weight: 300;
  height: -webkit-fit-content;
  height: -moz-fit-content;
  height: fit-content;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
  border: none;
  font-size: 14px;
  line-height: 20px;
  padding: 12px 22px;
  background-color: #17448d;
  color: #FFFFFF;
}

.emotion-0:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.emotion-0:hover:not(:disabled) {
  background-color: #2B5EC9;
}

<button
    class="emotion-0"
  >
    Large Button
  </button>
</DocumentFragment>
`;

exports[`StyledButton renders correctly with medium size 1`] = `
<DocumentFragment>
  .emotion-0 {
  border-radius: 8px;
  letter-spacing: 1px;
  font-weight: 300;
  height: -webkit-fit-content;
  height: -moz-fit-content;
  height: fit-content;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
  border: none;
  font-size: 14px;
  line-height: 20px;
  padding: 10px 14px;
  background-color: #17448d;
  color: #FFFFFF;
}

.emotion-0:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.emotion-0:hover:not(:disabled) {
  background-color: #2B5EC9;
}

<button
    class="emotion-0"
  >
    Medium Button
  </button>
</DocumentFragment>
`;

exports[`StyledButton renders correctly with outlined variant 1`] = `
<DocumentFragment>
  .emotion-0 {
  border-radius: 8px;
  letter-spacing: 1px;
  font-weight: 300;
  height: -webkit-fit-content;
  height: -moz-fit-content;
  height: fit-content;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
  border: none;
  font-size: 14px;
  line-height: 20px;
  padding: 7px 14px;
  background-color: transparent;
  color: [object Object];
  border: 1px solid #0A88ED;
}

.emotion-0:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.emotion-0:hover:not(:disabled) {
  background-color: #17448d;
  color: #FFFFFF;
}

<button
    class="emotion-0"
  >
    Outlined Button
  </button>
</DocumentFragment>
`;

exports[`StyledButton renders correctly with primary variant 1`] = `
<DocumentFragment>
  .emotion-0 {
  border-radius: 8px;
  letter-spacing: 1px;
  font-weight: 300;
  height: -webkit-fit-content;
  height: -moz-fit-content;
  height: fit-content;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
  border: none;
  font-size: 14px;
  line-height: 20px;
  padding: 7px 14px;
  background-color: #17448d;
  color: #FFFFFF;
}

.emotion-0:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.emotion-0:hover:not(:disabled) {
  background-color: #2B5EC9;
}

<button
    class="emotion-0"
  >
    Primary Button
  </button>
</DocumentFragment>
`;

exports[`StyledButton renders correctly with small size 1`] = `
<DocumentFragment>
  .emotion-0 {
  border-radius: 8px;
  letter-spacing: 1px;
  font-weight: 300;
  height: -webkit-fit-content;
  height: -moz-fit-content;
  height: fit-content;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  -webkit-transition: all 0.3s ease;
  transition: all 0.3s ease;
  border: none;
  font-size: 14px;
  line-height: 20px;
  padding: 7px 14px;
  background-color: #17448d;
  color: #FFFFFF;
}

.emotion-0:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.emotion-0:hover:not(:disabled) {
  background-color: #2B5EC9;
}

<button
    class="emotion-0"
  >
    Small Button
  </button>
</DocumentFragment>
`;
