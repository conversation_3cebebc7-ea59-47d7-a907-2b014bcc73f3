import { render } from '@testing-library/react';

import { StyledButton } from './Button.Style';

describe('StyledButton', () => {
  it('renders correctly with primary variant', () => {
    const { asFragment } = render(<StyledButton variant="primary">Primary Button</StyledButton>);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders correctly with outlined variant', () => {
    const { asFragment } = render(<StyledButton variant="outlined">Outlined Button</StyledButton>);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders correctly with small size', () => {
    const { asFragment } = render(<StyledButton size="small">Small Button</StyledButton>);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders correctly with medium size', () => {
    const { asFragment } = render(<StyledButton size="medium">Medium Button</StyledButton>);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders correctly with large size', () => {
    const { asFragment } = render(<StyledButton size="large">Large Button</StyledButton>);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders correctly when disabled', () => {
    const { asFragment } = render(<StyledButton disabled>Disabled Button</StyledButton>);
    expect(asFragment()).toMatchSnapshot();
  });
});
