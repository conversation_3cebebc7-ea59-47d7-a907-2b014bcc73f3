import React from 'react';

import { StyledButton, StyledButtonProps } from './Button.Style';

type ButtonType = 'button' | 'submit';

export interface ButtonProps
  extends StyledButtonProps {
  disabled?: boolean;
  btnType?: ButtonType;
  className?: string;
}

export const Button: React.FC<ButtonProps> = ({
  children,
  disabled = false,
  btnType,
  className,
  ...props
}) => (
  <StyledButton
    className={className}
    type={btnType}
    disabled={disabled}
    {...props}
  >
    {children}
  </StyledButton>
);
