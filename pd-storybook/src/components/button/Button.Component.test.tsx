import { fireEvent, render } from '@testing-library/react';

import { Button } from './Button.Component';

describe('Button Component', () => {
  it('should calls onClick when clicked', () => {
    const handleClick = jest.fn();
    const { getByText } = render(<Button onClick={handleClick}>Click Me</Button>);

    fireEvent.click(getByText('Click Me'));

    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('Should render with the correct className', () => {
    const { getByText } = render(<Button className="test-class" onClick={() => {}}>Click Me</Button>);

    expect(getByText('Click Me')).toHaveClass('test-class');
  });

  it('Should render children correctly', () => {
    const { getByTestId } = render(<Button onClick={() => {}}><span data-testid="testId">Click Me</span></Button>);

    expect(getByTestId('testId')).toBeInTheDocument();
  });

  it('applies the correct type', () => {
    const { getByText } = render(<Button btnType="submit" onClick={() => {}}>Click Me</Button>);

    expect(getByText('Click Me')).toHaveAttribute('type', 'submit');
  });

  it('is disabled when the disabled prop is true', () => {
    const { getByText } = render(<Button disabled onClick={() => {}}>Click Me</Button>);

    expect(getByText('Click Me')).toBeDisabled();
  });
});
