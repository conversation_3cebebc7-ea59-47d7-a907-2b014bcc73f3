import { render, screen, fireEvent } from '@testing-library/react';

import { Tooltip } from './Tooltip.Component';

describe('Tooltip Component', () => {
  const defaultProps = {
    content: 'Tooltip content',
    children: <div>Hover me</div>,
  };

  it('renders correctly with default props', () => {
    render(<Tooltip {...defaultProps} />);
    expect(screen.getByText('Hover me')).toBeInTheDocument();
    expect(screen.getByText('Tooltip content')).toBeInTheDocument();
  });

  it('shows tooltip content on hover', () => {
    render(<Tooltip {...defaultProps} />);
    const container = screen.getByText('Hover me').parentElement;

    // Initial state
    expect(container).not.toHaveClass('hovered');

    // Hover state
    fireEvent.mouseEnter(container!);
    expect(container).toHaveClass('hovered');
    expect(screen.getByText('Tooltip content')).toHaveClass('visible');

    // Remove hover
    fireEvent.mouseLeave(container!);
    expect(container).not.toHaveClass('hovered');
    expect(screen.getByText('Tooltip content')).not.toHaveClass('visible');
  });

  it('applies custom className to tooltip content', () => {
    const className = 'custom-tooltip';
    render(<Tooltip {...defaultProps} className={className} />);
    expect(screen.getByText('Tooltip content')).toHaveClass(className);
  });

  it('renders children correctly', () => {
    const childText = 'Custom child content';
    render(
      <Tooltip content="Tooltip content">
        <span>{childText}</span>
      </Tooltip>,
    );
    expect(screen.getByText(childText)).toBeInTheDocument();
  });

  it('passes additional props to tooltip content', () => {
    render(<Tooltip {...defaultProps} data-testid="tooltip-content" />);
    expect(screen.getByText('Tooltip content')).toHaveAttribute('data-testid', 'tooltip-content');
  });

  it('does not render tooltip content when content is undefined', () => {
    render(<Tooltip>{defaultProps.children}</Tooltip>);
    expect(screen.queryByText('Tooltip content')).not.toBeInTheDocument();
  });
});
