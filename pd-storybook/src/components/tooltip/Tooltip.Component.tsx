import React, { useState } from 'react';

import { StyledTooltipProps, TooltipContainer, TooltipContent } from './Tooltip.Style';

export interface TooltipProps extends Omit<StyledTooltipProps, 'content'> {
  content?: React.ReactNode;
  children?: React.ReactNode;
  className?: string;
}

export const Tooltip: React.FC<TooltipProps> = ({
  content,
  children,
  className,
  ...props
}) => {
  const [isHovered, setIsHovered] = useState<boolean>(false);

  return (
    <TooltipContainer
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      className={isHovered ? 'hovered' : ''}
    >
      {children}
      <TooltipContent
        className={`${isHovered ? 'visible' : ''} ${className || ''}`}
        position={props.position || 'top'}
        {...props}
      >
        {content || ''}
      </TooltipContent>
    </TooltipContainer>
  );
};
