import { render } from '@testing-library/react';

import { TooltipContainer, TooltipContent } from './Tooltip.Style';

describe('Tooltip Style Snapshots', () => {
  it('renders TooltipContainer correctly', () => {
    const { asFragment } = render(<TooltipContainer>Tooltip Container</TooltipContainer>);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders TooltipContainer with hovered class correctly', () => {
    const { asFragment } = render(
      <TooltipContainer className="hovered">Tooltip Container</TooltipContainer>,
    );
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders TooltipContent with default props correctly', () => {
    const { asFragment } = render(<TooltipContent>Default Tooltip</TooltipContent>);
    expect(asFragment()).toMatchSnapshot();
  });

  describe('TooltipContent positions', () => {
    const positions = ['top', 'right', 'bottom', 'left'] as const;

    positions.forEach((position) => {
      it(`renders TooltipContent with ${position} position correctly`, () => {
        const { asFragment } = render(
          <TooltipContent position={position}>
            {position} positioned tooltip
          </TooltipContent>,
        );
        expect(asFragment()).toMatchSnapshot();
      });
    });
  });

  it('renders TooltipContent with custom colors correctly', () => {
    const { asFragment } = render(
      <TooltipContent bgColor="#FF5733" fontColor="#FFFFFF">
        Custom colored tooltip
      </TooltipContent>,
    );
    expect(asFragment()).toMatchSnapshot();
  });
});
