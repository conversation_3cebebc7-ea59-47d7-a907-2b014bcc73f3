import { render, screen } from '@testing-library/react';

import { TooltipProps } from '../tooltip/Tooltip.Component';

import { Avatar, BorderProps } from './Avatar.Component';
import { AvatarContainer, InitialsContainer } from './Avatar.Style';

interface MockedTooltipProps extends TooltipProps {
  children: React.ReactNode;
}

jest.mock('../tooltip/Tooltip.Component', () => ({
  Tooltip: ({ children, content, ...props }: MockedTooltipProps) => (
    <div data-testid="tooltip" {...props}>{children}</div>
  ),
}));

describe('Avatar Component', () => {
  it('should render initials when src is not provided', () => {
    render(<Avatar name="<PERSON>" />);
    expect(screen.getByText('JD')).toBeInTheDocument();
  });

  it('should render image when src is provided', () => {
    render(<Avatar src="https://example.com/avatar.jpg" name="<PERSON>" />);
    expect(screen.getByRole('img', { name: '<PERSON>' })).toBeVisible();
  });

  it('should render tooltip when tooltip prop is true', () => {
    render(<Avatar name="John Doe" tooltip />);
    expect(screen.getByTestId('tooltip')).toBeVisible();
  });

  it('should not render tooltip when tooltip prop is false', () => {
    render(<Avatar name="John Doe" tooltip={false} />);
    expect(screen.queryByTestId('tooltip')).not.toBeInTheDocument();
  });

  it('should apply size prop to the avatar container', () => {
    render(<Avatar name="John Doe" size={100} />);
    const avatarContainer = screen.getByTestId('avatar');
    expect(avatarContainer).toHaveStyle('width: 100px; height: 100px;');
  });

  it('should apply border prop to the avatar container', () => {
    render(<Avatar name="John Doe" border={{ color: 'red', width: '2px' }} />);
    const avatarContainer = screen.getByTestId('avatar');
    expect(avatarContainer).toHaveStyle('border-color: red; border-width: 2px;');
  });

  it('should apply shape prop to the avatar container', () => {
    render(<Avatar name="John Doe" shape="square" />);
    const avatarContainer = screen.getByTestId('avatar');
    expect(avatarContainer).toHaveStyle('border-radius: 0;');
  });

  it('should render a default avatar image when name is not provided', () => {
    render(<Avatar src="https://example.com/avatar.jpg" />);
    const avatarImage = screen.getByRole('img');
    expect(avatarImage).toBeVisible();
  });

  it('should call onClick function when avatar is clicked', () => {
    const onClick = jest.fn();
    render(<Avatar name="John Doe" onClick={onClick} />);
    const avatarContainer = screen.getByTestId('avatar');
    avatarContainer.click();
    expect(onClick).toHaveBeenCalledTimes(1);
  });

  it('should render initials with correct font size when size is a number', () => {
    render(<Avatar name="John Doe" size={80} />);
    const initialsContainer = screen.getByText('JD');
    expect(initialsContainer).toHaveStyle('font-size: 40px;');
  });

  it('should render initials with correct font size when size is a string', () => {
    render(<Avatar name="John Doe" size={60} />);
    const initialsContainer = screen.getByText('JD');
    expect(initialsContainer).toHaveStyle('font-size: 30px;');
  });

  it('should handle name with accents and special characters', () => {
    render(<Avatar name="José Pérez" />);
    expect(screen.getByText('JP')).toBeInTheDocument();
  });

  it('should handle name with multiple spaces', () => {
    render(<Avatar name="  John   Doe  " />);
    expect(screen.getByText('JD')).toBeInTheDocument();
  });

  it('should apply the "circle" shape by default', () => {
    render(<Avatar name="John Doe" />);
    const avatarContainer = screen.getByTestId('avatar');
    expect(avatarContainer).toHaveStyle('border-radius: 50%;');
  });

  it('should calculate fontSize correctly when size is provided', () => {
    render(<Avatar name="John Doe" size={80} />);
    const initialsContainer = screen.getAllByTestId('initialsContainer')[0];
    expect(initialsContainer).toHaveStyle('font-size: 40px');
  });

  it('should use default fontSize when size is not provided', () => {
    render(<Avatar name="John Doe" />);
    const initialsContainer = screen.getAllByTestId('initialsContainer')[0];
    expect(initialsContainer).toHaveStyle('font-size: 25px');
  });

  it('should use default font size when name is empty', () => {
    render(<Avatar name="" />);
    const initialsContainer = screen.getAllByTestId('initialsContainer')[0];
    expect(initialsContainer).toHaveStyle('font-size: 25px');
  });

  it('should use default size when size is not provided', () => {
    render(<Avatar name="Jon Snow" />);
    expect(screen.getAllByTestId('avatar')[0]).toHaveStyle('width: 50px');
  });

  it('should call onImageError when image fails to load', () => {
    const onImageError = jest.fn();
    render(<Avatar src="broken-url.jpg" name="John Doe" onImageError={onImageError} />);
    const img = screen.getByRole('img');
    img.dispatchEvent(new Event('error'));
    expect(onImageError).toHaveBeenCalled();
  });

  it('should apply default border when border prop is not provided', () => {
    render(<Avatar name="John Doe" />);
    const avatarContainer = screen.getByTestId('avatar');
    expect(avatarContainer).toHaveStyle('border-color: white; border-width: 0;');
  });

  it('should apply flexShrink prop to the avatar container', () => {
    render(<Avatar name="John Doe" flexShrink={2} />);
    const avatarContainer = screen.getByTestId('avatar');
    expect(avatarContainer).toHaveStyle('flex-shrink: 2;');
  });

  it('should apply custom className to the avatar container', () => {
    render(<Avatar name="John Doe" className="custom-class" />);
    const avatarContainer = screen.getByTestId('avatar');
    expect(avatarContainer).toHaveClass('custom-class');
  });

  it('should not re-render if props are the same (memoization)', () => {
    const { rerender } = render(<Avatar name="John Doe" size={50} />);
    const avatarContainer = screen.getByTestId('avatar');
    rerender(<Avatar name="John Doe" size={50} />);
    expect(avatarContainer).toBeInTheDocument();
  });

  // Tests for uncovered branches
  it('should use default font size when size is 0', () => {
    render(<Avatar name="John Doe" size={0} />);
    const initialsContainer = screen.getByTestId('initialsContainer');
    expect(initialsContainer).toHaveStyle('font-size: 25px');
  });

  it('should handle undefined border in memoization comparison', () => {
    const { rerender } = render(<Avatar name="John Doe" />);
    const avatarContainer = screen.getByTestId('avatar');
    rerender(<Avatar name="John Doe" border={undefined} />);
    expect(avatarContainer).toBeInTheDocument();
  });

  it('should handle null border in memoization comparison', () => {
    const { rerender } = render(<Avatar name="John Doe" border={null as unknown as BorderProps} />);
    const avatarContainer = screen.getByTestId('avatar');
    rerender(<Avatar name="John Doe" border={null as unknown as BorderProps} />);
    expect(avatarContainer).toBeInTheDocument();
  });

  it('should handle border with undefined color and width', () => {
    render(<Avatar name="John Doe" border={{}} />);
    const avatarContainer = screen.getByTestId('avatar');
    expect(avatarContainer).toBeInTheDocument();
  });

  it('should use default background when initialsBackground is not provided', () => {
    render(<Avatar name="John Doe" />);
    const initialsContainer = screen.getByTestId('initialsContainer');
    // The background should be set by the getColors function, not the default fallback
    expect(initialsContainer).toBeInTheDocument();
  });

  it('should handle tooltipProps when tooltip is enabled', () => {
    const tooltipProps = { position: 'bottom' as const, bgColor: 'red' };
    render(<Avatar name="John Doe" tooltip tooltipProps={tooltipProps} />);
    const tooltip = screen.getByTestId('tooltip');
    expect(tooltip).toBeInTheDocument();
  });

  it('should handle different color variations based on name', () => {
    // Test different names to trigger different color indices
    const names = ['Alice', 'Bob', 'Charlie', 'Diana', 'Eve', 'Frank'];
    names.forEach((name) => {
      const { unmount } = render(<Avatar name={name} />);
      const initialsContainer = screen.getByTestId('initialsContainer');
      expect(initialsContainer).toBeInTheDocument();
      unmount();
    });
  });

  it('should re-render when border properties change', () => {
    const { rerender } = render(<Avatar name="John Doe" border={{ color: 'red', width: '1px' }} />);
    let avatarContainer = screen.getByTestId('avatar');
    expect(avatarContainer).toBeInTheDocument();

    rerender(<Avatar name="John Doe" border={{ color: 'blue', width: '1px' }} />);
    avatarContainer = screen.getByTestId('avatar');
    expect(avatarContainer).toBeInTheDocument();

    rerender(<Avatar name="John Doe" border={{ color: 'red', width: '2px' }} />);
    avatarContainer = screen.getByTestId('avatar');
    expect(avatarContainer).toBeInTheDocument();
  });

  it('should handle memoization when one border is undefined and other has width', () => {
    const { rerender } = render(<Avatar name="John Doe" />);
    let avatarContainer = screen.getByTestId('avatar');
    expect(avatarContainer).toBeInTheDocument();

    rerender(<Avatar name="John Doe" border={{ color: 'red', width: '1px' }} />);
    avatarContainer = screen.getByTestId('avatar');
    expect(avatarContainer).toBeInTheDocument();
  });

  it('should handle size as string in AvatarContainer styled component', () => {
    // Test the styled component directly with string size to cover lines 11-12
    const TestComponent = () => (
      <AvatarContainer size={'100px' as unknown as number} data-testid="string-size-avatar">
        Test
      </AvatarContainer>
    );
    render(<TestComponent />);
    const avatarContainer = screen.getByTestId('string-size-avatar');
    expect(avatarContainer).toBeInTheDocument();
  });

  it('should use fallback background in InitialsContainer when initialsBackground is undefined', () => {
    // Test InitialsContainer directly with undefined initialsBackground to cover line 35
    const TestComponent = () => (
      <InitialsContainer data-testid="undefined-bg-initials" fontSize="20px">
        AB
      </InitialsContainer>
    );
    render(<TestComponent />);
    const initialsContainer = screen.getByTestId('undefined-bg-initials');
    expect(initialsContainer).toBeInTheDocument();
    expect(initialsContainer).toHaveStyle('background: white');
  });

  it('should handle memoization when border width changes from undefined to defined', () => {
    // This test specifically targets line 111: prevProps.border?.width === nextProps.border?.width
    const { rerender } = render(<Avatar name="John Doe" border={{ color: 'red' }} />);
    let avatarContainer = screen.getByTestId('avatar');
    expect(avatarContainer).toBeInTheDocument();

    // Change to border with width - this should trigger the width comparison on line 111
    rerender(<Avatar name="John Doe" border={{ color: 'red', width: '2px' }} />);
    avatarContainer = screen.getByTestId('avatar');
    expect(avatarContainer).toBeInTheDocument();
  });
});
