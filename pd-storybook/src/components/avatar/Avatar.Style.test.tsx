import { render } from '@testing-library/react';

import { AvatarContainer, AvatarImage } from './Avatar.Style';

describe('Avatar Styles Snapshots', () => {
  it('renders AvatarContainer with default props correctly', () => {
    const { asFragment } = render(<AvatarContainer size={50} />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders AvatarContainer with custom size correctly', () => {
    const { asFragment } = render(<AvatarContainer size={100} />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders AvatarContainer with custom border color correctly', () => {
    const { asFragment } = render(<AvatarContainer size={50} />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders AvatarContainer with custom border width correctly', () => {
    const { asFragment } = render(<AvatarContainer size={50} />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders AvatarImage correctly', () => {
    const { asFragment } = render(<AvatarImage src="avatar.jpg" alt="Avatar" />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders AvatarContainer with numeric size correctly', () => {
    const { asFragment } = render(<AvatarContainer size={50} />);
    expect(asFragment()).toMatchSnapshot();
  });
});
