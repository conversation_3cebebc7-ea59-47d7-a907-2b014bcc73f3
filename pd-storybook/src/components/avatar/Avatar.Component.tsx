import React, { memo } from 'react';

import { Tooltip, TooltipProps } from '../tooltip/Tooltip.Component';

import { AvatarContainer, AvatarImage, InitialsContainer } from './Avatar.Style';

export type BorderProps = {
  color?: string;
  width?: string;
};

export interface BaseAvatarProps {
  src?: string;
  onImageError?: () => void;
  size?: number;
  border?: BorderProps;
  tooltip?: boolean;
  tooltipProps?: TooltipProps
  shape?: 'circle' | 'square';
  flexShrink?: number;
}

export interface AvatarProps extends BaseAvatarProps {
  id?: string;
  name?: string;
  onClick?: () => void;
  className?: string;
  // @deprecated
  initialsBackground?: string;
}

const getInitials = (name?: string) => {
  if (!name) return '';
  const trimmedName = name.trim().replace(/\s+/g, ' ');

  const nameParts = trimmedName.split(' ');
  const initials = nameParts.slice(0, 2).map((part) => part[0]);

  return initials.join('');
};

const getColors = (name: string) => {
  const colors = [
    { backgroundColor: 'blue', textColor: 'text-white' },
    { backgroundColor: 'green', textColor: 'text-white' },
    { backgroundColor: 'red', textColor: 'text-white' },
    { backgroundColor: 'yellow', textColor: 'text-black' },
    { backgroundColor: 'purple', textColor: 'text-white' },
  ];

  const index = name ? name.charCodeAt(0) % colors.length : 0;
  return colors[index];
};

const PureAvatar: React.FC<AvatarProps> = ({
  src,
  onImageError,
  size = 50,
  border = {
    color: 'white',
    width: '0',
  },
  name,
  tooltip,
  tooltipProps,
  shape = 'circle',
  className = '',
  flexShrink = 0,
  ...props
}) => {
  const AvatarContent = () => <AvatarContainer
    data-testid='avatar'
    size={size}
    border={border}
    shape={shape}
    className={className}
    flexShrink = {flexShrink }
    {...props}
>
    {!src ? (
      <InitialsContainer
        data-testid='initialsContainer'
        className={`initials ${getColors(name || '').textColor}`}
        fontSize= {size ? `${size / 2}px` : '25px'}
        initialsBackground={getColors(name || '').backgroundColor}
      >
        {getInitials(name)}
      </InitialsContainer>
    ) : (
      <AvatarImage src={src} alt={name} onError={onImageError} />
    )}
  </AvatarContainer>;

  return tooltip ? (
    <Tooltip
      data-testid="tooltip"
      content={name}
      {...tooltipProps}
    >
      <AvatarContent />

    </Tooltip>
  ) : <AvatarContent />;
};

export const Avatar = memo(PureAvatar, (prevProps, nextProps) => (
  prevProps.src === nextProps.src
    && prevProps.name === nextProps.name
    && prevProps.size === nextProps.size
    && prevProps.border?.color === nextProps.border?.color
    && prevProps.border?.width === nextProps.border?.width
    && prevProps.shape === nextProps.shape
    && prevProps.flexShrink === nextProps.flexShrink
));
