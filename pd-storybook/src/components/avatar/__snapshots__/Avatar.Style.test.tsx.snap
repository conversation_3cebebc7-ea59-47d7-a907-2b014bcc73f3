// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Avatar Styles Snapshots renders AvatarContainer with custom border color correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  width: 50px;
  height: 50px;
  border-radius: 0;
  overflow: hidden;
  display: inline-block;
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.1);
  border: solid;
}

<div
    class="emotion-0"
    size="50"
  />
</DocumentFragment>
`;

exports[`Avatar Styles Snapshots renders AvatarContainer with custom border width correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  width: 50px;
  height: 50px;
  border-radius: 0;
  overflow: hidden;
  display: inline-block;
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.1);
  border: solid;
}

<div
    class="emotion-0"
    size="50"
  />
</DocumentFragment>
`;

exports[`Avatar Styles Snapshots renders AvatarContainer with custom size correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  width: 100px;
  height: 100px;
  border-radius: 0;
  overflow: hidden;
  display: inline-block;
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.1);
  border: solid;
}

<div
    class="emotion-0"
    size="100"
  />
</DocumentFragment>
`;

exports[`Avatar Styles Snapshots renders AvatarContainer with default props correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  width: 50px;
  height: 50px;
  border-radius: 0;
  overflow: hidden;
  display: inline-block;
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.1);
  border: solid;
}

<div
    class="emotion-0"
    size="50"
  />
</DocumentFragment>
`;

exports[`Avatar Styles Snapshots renders AvatarContainer with numeric size correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  width: 50px;
  height: 50px;
  border-radius: 0;
  overflow: hidden;
  display: inline-block;
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.1);
  border: solid;
}

<div
    class="emotion-0"
    size="50"
  />
</DocumentFragment>
`;

exports[`Avatar Styles Snapshots renders AvatarImage correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

<img
    alt="Avatar"
    class="emotion-0"
    src="avatar.jpg"
  />
</DocumentFragment>
`;
