import styled from '@emotion/styled';

import { BorderProps } from './Avatar.Component';

export const AvatarContainer = styled.div<{
  size: number;
  border?: BorderProps;
  shape?: 'circle' | 'square';
  flexShrink?: number;
}>`
  width: ${(props) => (typeof props.size === 'number' ? `${props.size}px` : props.size)};
  height: ${(props) => (typeof props.size === 'number' ? `${props.size}px` : props.size)};
  border-radius: ${(props) => (props.shape === 'circle' ? '50%' : '0')};
  flex-shrink: ${(props) => props.flexShrink};
  overflow: hidden;
  display: inline-block;
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.1);
  border: ${(props) => props.border?.width} solid ${(props) => props.border?.color};
`;

export const AvatarImage = styled.img`
  width: 100%;
  height: 100%;
  object-fit: cover; /* Asegura que la imagen cubra todo el espacio disponible */
`;

export const InitialsContainer = styled.div<{ fontSize?: string; initialsBackground?: string }>`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  padding-top: 2px;
  font-size: ${({ fontSize }) => fontSize};
  background: ${({ initialsBackground }) => initialsBackground || 'white'};
`;
