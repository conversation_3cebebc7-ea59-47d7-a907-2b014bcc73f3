import { DatePicker, LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { esES } from '@mui/x-date-pickers/locales';
import dayjs, { Dayjs } from 'dayjs';
import 'dayjs/locale/es-mx';
import updateLocale from 'dayjs/plugin/updateLocale';
import React, {
  ChangeEvent,
  useEffect,
  useState,
} from 'react';

dayjs.extend(updateLocale);
dayjs.updateLocale('es-mx', {
  months: ['Enero', 'Febrero', '<PERSON><PERSON>', '<PERSON><PERSON>l', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', 'Agosto', 'Septiembre', 'Octubre', 'Noviembre', 'Diciembre'],
  monthsShort: ['Enero.', 'Feb.', 'Mar', 'Abr.', 'May', 'Jun', 'Jul.', 'Ago', 'Sept.', 'Oct.', 'Nov.', 'Dec.'],
  weekdays: ['<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>er<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>'],
  weekdaysShort: ['Dom.', 'Lun.', 'Mar.', 'Mier.', 'Jue.', 'Vier.', 'Sab.'],
  weekdaysMin: ['Do', 'Lu', 'Ma', 'Mi', 'Ju', 'Vi', 'Sa'],
});

interface DateInputProps {
  name: string;
  onChange?: (event: ChangeEvent<HTMLInputElement>) => void;
  className?: string;
  disabled?: boolean;
  value?: string;
  onError?: (error: string | null) => void;
}

const minDate = dayjs('1910-01-01');
const maxDate = dayjs('2100-12-31');

export const DateInputComponent: React.FC<DateInputProps> = ({
  name,
  value,
  disabled,
  className,
  onChange,
  onError,
}) => {
  const [dateValue, setDateValue] = useState<Dayjs | null>(value ? dayjs(value) : null);

  useEffect(() => {
    if (value) {
      setDateValue(dayjs(value));
    }
  }, [value]);

  const handleValueChange = (newValue: Dayjs | null) => {
    setDateValue(newValue);
    if (onChange) {
      const date = newValue && newValue.isValid() ? newValue.format() : null;
      onChange({ target: { value: date, name } } as ChangeEvent<HTMLInputElement>);
    }
  };

  return (

    <LocalizationProvider
      localeText={esES.components.MuiLocalizationProvider.defaultProps.localeText}
      dateAdapter={AdapterDayjs}
      adapterLocale='es-mx'>
      <DatePicker
        data-testid="test-date"
        value={dateValue}
        name={name}
        className='pd-w-full'
        onChange={handleValueChange}
        disabled={disabled}
        format='DD/MM/YYYY'
        minDate={minDate}
        maxDate={maxDate}
        onError={onError}
        slotProps={{
          day: {
            className: '[&.Mui-selected]:!pd-bg-primary [&.MuiPickersDay-today]:!pd-border-primary hover:!pd-bg-primary hover:!pd-text-white',
          },
          textField: {
            color: 'primary',
            inputProps: {
              'data-testid': `date-input-${name}`,
            },
            InputProps: {
              className: `
                !pd-h-[38px] 
                !pd-rounded-md 
                !pd-text-[13px] 
                !pd-text-dark-700
                focus:!pd-outline-1 
                [&.Mui-disabled]:!pd-bg-lightGray 
                [&.Mui-disabled]:!pd-text-dark-500 
                [&.Mui-disabled]:!pd-cursor-not-allowed 
                [&.Mui-focused:not(.Mui-error)>fieldset]:!pd-border-positive
                [&>fieldset]:!pd-border-1
                [&>fieldset]:!pd-border
                [&>fieldset]:!pd-border-line
                [&.Mui-error>fieldset]:!pd-border-negative
                ${className}`,
            },
          },
        }}
        />
    </LocalizationProvider>
  );
};
