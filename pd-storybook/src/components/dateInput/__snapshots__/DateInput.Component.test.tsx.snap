// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`DateInputComponent matches snapshot - default 1`] = `
<DocumentFragment>
  .emotion-0 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  position: relative;
  min-width: 0;
  padding: 0;
  margin: 0;
  border: 0;
  vertical-align: top;
}

.emotion-1 {
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-weight: 400;
  font-size: 1rem;
  line-height: 1.5;
  letter-spacing: 0.00938em;
  color: rgba(0, 0, 0, 0.87);
  cursor: text;
  padding: 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  position: relative;
  box-sizing: border-box;
  padding: 0 14px;
  border-radius: 4px;
}

.emotion-1:hover .MuiPickersOutlinedInput-notchedOutline {
  border-color: rgba(0, 0, 0, 0.87);
}

@media (hover: none) {
  .emotion-1:hover .MuiPickersOutlinedInput-notchedOutline {
    border-color: rgba(0, 0, 0, 0.23);
  }
}

.emotion-1.Mui-focused .MuiPickersOutlinedInput-notchedOutline {
  border-style: solid;
  border-width: 2px;
}

.emotion-1.Mui-disabled .MuiPickersOutlinedInput-notchedOutline {
  border-color: rgba(0, 0, 0, 0.26);
}

.emotion-1.Mui-disabled * {
  color: rgba(0, 0, 0, 0.26);
}

.emotion-1.Mui-error .MuiPickersOutlinedInput-notchedOutline {
  border-color: #d32f2f;
}

.emotion-1.Mui-focused:not(.Mui-error) .MuiPickersOutlinedInput-notchedOutline {
  border-color: #1976d2;
}

.emotion-2 {
  direction: ltr;
  outline: none;
  padding: 4px 0 5px;
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-size: inherit;
  line-height: 1.4375em;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  outline: none;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-wrap: nowrap;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  overflow: hidden;
  letter-spacing: inherit;
  width: 182px;
  color: currentColor;
  opacity: 0;
  opacity: 0.42;
  padding: 16.5px 0;
}

.emotion-3 {
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-size: inherit;
  letter-spacing: inherit;
  line-height: 1.4375em;
  display: inline-block;
  white-space: nowrap;
}

.emotion-4 {
  white-space: pre;
  white-space: pre;
  letter-spacing: inherit;
}

.emotion-5 {
  outline: none;
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  line-height: 1.4375em;
  letter-spacing: inherit;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  outline: none;
}

.emotion-15 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  max-height: 2em;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  white-space: nowrap;
  color: rgba(0, 0, 0, 0.54);
  margin-left: 8px;
}

.emotion-16 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  position: relative;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  background-color: transparent;
  outline: 0;
  border: 0;
  margin: 0;
  border-radius: 0;
  padding: 0;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  vertical-align: middle;
  -moz-appearance: none;
  -webkit-appearance: none;
  -webkit-text-decoration: none;
  text-decoration: none;
  color: inherit;
  text-align: center;
  -webkit-flex: 0 0 auto;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  font-size: 1.5rem;
  padding: 8px;
  border-radius: 50%;
  color: rgba(0, 0, 0, 0.54);
  -webkit-transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  --IconButton-hoverBg: rgba(0, 0, 0, 0.04);
  margin-right: -12px;
}

.emotion-16::-moz-focus-inner {
  border-style: none;
}

.emotion-16.Mui-disabled {
  pointer-events: none;
  cursor: default;
}

@media print {
  .emotion-16 {
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
}

.emotion-16:hover {
  background-color: var(--IconButton-hoverBg);
}

@media (hover: none) {
  .emotion-16:hover {
    background-color: transparent;
  }
}

.emotion-16.Mui-disabled {
  background-color: transparent;
  color: rgba(0, 0, 0, 0.26);
}

.emotion-16.MuiIconButton-loading {
  color: transparent;
}

.emotion-17 {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  width: 1em;
  height: 1em;
  display: inline-block;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  -webkit-transition: fill 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: fill 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  fill: currentColor;
  font-size: 1.5rem;
}

.emotion-18 {
  text-align: left;
  position: absolute;
  bottom: 0;
  right: 0;
  top: -5px;
  left: 0;
  margin: 0;
  padding: 0 8px;
  pointer-events: none;
  border-radius: inherit;
  border-style: solid;
  border-width: 1px;
  overflow: hidden;
  min-width: 0%;
  border-color: rgba(0, 0, 0, 0.23);
}

.emotion-19 {
  float: unset;
  width: auto;
  overflow: hidden;
  padding: 0;
  line-height: 11px;
  -webkit-transition: width 150ms cubic-bezier(0.0, 0, 0.2, 1) 0ms;
  transition: width 150ms cubic-bezier(0.0, 0, 0.2, 1) 0ms;
}

.emotion-20 {
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-size: inherit;
}

.emotion-21 {
  border: 0;
  clip: rect(0 0 0 0);
  height: 1px;
  margin: -1px;
  overflow: hidden;
  padding: 0;
  position: absolute;
  white-space: nowrap;
  width: 1px;
}

<div
    class="MuiFormControl-root MuiPickersTextField-root pd-w-full emotion-0"
  >
    <div
      aria-invalid="false"
      class="MuiPickersInputBase-root MuiPickersOutlinedInput-root MuiPickersInputBase-colorPrimary MuiPickersInputBase-adornedEnd 
 !pd-h-[38px] 
 !pd-rounded-md 
 !pd-text-[13px] 
 !pd-text-dark-700
 focus:!pd-outline-1 
 [&.Mui-disabled]:!pd-bg-lightGray 
 [&.Mui-disabled]:!pd-text-dark-500 
 [&.Mui-disabled]:!pd-cursor-not-allowed 
 [&.Mui-focused:not(.Mui-error)>fieldset]:!pd-border-positive
 [&>fieldset]:!pd-border-1
 [&>fieldset]:!pd-border
 [&>fieldset]:!pd-border-line
 [&.Mui-error>fieldset]:!pd-border-negative
 undefined emotion-1"
      role="group"
    >
      <div
        class="MuiPickersSectionList-root MuiPickersInputBase-sectionsContainer emotion-2"
        contenteditable="false"
        tabindex="0"
      >
        <span
          class="MuiPickersSectionList-section emotion-3"
          data-sectionindex="0"
        >
          <span
            class="MuiPickersInputBase-sectionBefore emotion-4"
          />
          <span
            aria-disabled="false"
            aria-label="Dia"
            aria-labelledby=":r1:-day"
            aria-readonly="false"
            aria-valuemax="31"
            aria-valuemin="1"
            aria-valuetext="Vacío"
            autocapitalize="off"
            autocorrect="off"
            class="MuiPickersSectionList-sectionContent MuiPickersInputBase-sectionContent emotion-5"
            contenteditable="true"
            enterkeyhint="next"
            id=":r1:-day"
            inputmode="numeric"
            role="spinbutton"
            spellcheck="false"
            tabindex="0"
          >
            DD
          </span>
          <span
            class="MuiPickersInputBase-sectionAfter emotion-4"
          >
            /
          </span>
        </span>
        <span
          class="MuiPickersSectionList-section emotion-3"
          data-sectionindex="1"
        >
          <span
            class="MuiPickersInputBase-sectionBefore emotion-4"
          />
          <span
            aria-disabled="false"
            aria-label="Mes"
            aria-labelledby=":r1:-month"
            aria-readonly="false"
            aria-valuemax="12"
            aria-valuemin="1"
            aria-valuetext="Vacío"
            autocapitalize="off"
            autocorrect="off"
            class="MuiPickersSectionList-sectionContent MuiPickersInputBase-sectionContent emotion-5"
            contenteditable="true"
            enterkeyhint="next"
            id=":r1:-month"
            inputmode="numeric"
            role="spinbutton"
            spellcheck="false"
            tabindex="-1"
          >
            MM
          </span>
          <span
            class="MuiPickersInputBase-sectionAfter emotion-4"
          >
            /
          </span>
        </span>
        <span
          class="MuiPickersSectionList-section emotion-3"
          data-sectionindex="2"
        >
          <span
            class="MuiPickersInputBase-sectionBefore emotion-4"
          />
          <span
            aria-disabled="false"
            aria-label="Año"
            aria-labelledby=":r1:-year"
            aria-readonly="false"
            aria-valuemax="9999"
            aria-valuemin="0"
            aria-valuetext="Vacío"
            autocapitalize="off"
            autocorrect="off"
            class="MuiPickersSectionList-sectionContent MuiPickersInputBase-sectionContent emotion-5"
            contenteditable="true"
            enterkeyhint="next"
            id=":r1:-year"
            inputmode="numeric"
            role="spinbutton"
            spellcheck="false"
            tabindex="-1"
          >
            AAAA
          </span>
          <span
            class="MuiPickersInputBase-sectionAfter emotion-4"
          />
        </span>
      </div>
      <div
        class="MuiInputAdornment-root MuiInputAdornment-positionEnd MuiInputAdornment-outlined MuiInputAdornment-sizeMedium emotion-15"
      >
        <button
          aria-label="Elige fecha"
          class="MuiButtonBase-root MuiIconButton-root MuiIconButton-edgeEnd MuiIconButton-sizeMedium emotion-16"
          tabindex="0"
          type="button"
        >
          <svg
            aria-hidden="true"
            class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium emotion-17"
            data-testid="CalendarIcon"
            focusable="false"
            viewBox="0 0 24 24"
          >
            <path
              d="M17 12h-5v5h5v-5zM16 1v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2h-1V1h-2zm3 18H5V8h14v11z"
            />
          </svg>
        </button>
      </div>
      <fieldset
        aria-hidden="true"
        class="MuiPickersOutlinedInput-notchedOutline emotion-18"
      >
        <legend
          class="emotion-19"
        >
          <span
            class="notranslate emotion-20"
          >
            ​
          </span>
        </legend>
      </fieldset>
      <input
        aria-hidden="true"
        class="MuiPickersInputBase-input MuiPickersOutlinedInput-input emotion-21"
        data-testid="date-input-test-date-snapshot"
        id=":r0:"
        name="test-date-snapshot"
        tabindex="-1"
        value=""
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`DateInputComponent matches snapshot - disabled 1`] = `
<DocumentFragment>
  .emotion-0 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  position: relative;
  min-width: 0;
  padding: 0;
  margin: 0;
  border: 0;
  vertical-align: top;
}

.emotion-1 {
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-weight: 400;
  font-size: 1rem;
  line-height: 1.5;
  letter-spacing: 0.00938em;
  color: rgba(0, 0, 0, 0.87);
  cursor: text;
  padding: 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  position: relative;
  box-sizing: border-box;
  padding: 0 14px;
  border-radius: 4px;
}

.emotion-1:hover .MuiPickersOutlinedInput-notchedOutline {
  border-color: rgba(0, 0, 0, 0.87);
}

@media (hover: none) {
  .emotion-1:hover .MuiPickersOutlinedInput-notchedOutline {
    border-color: rgba(0, 0, 0, 0.23);
  }
}

.emotion-1.Mui-focused .MuiPickersOutlinedInput-notchedOutline {
  border-style: solid;
  border-width: 2px;
}

.emotion-1.Mui-disabled .MuiPickersOutlinedInput-notchedOutline {
  border-color: rgba(0, 0, 0, 0.26);
}

.emotion-1.Mui-disabled * {
  color: rgba(0, 0, 0, 0.26);
}

.emotion-1.Mui-error .MuiPickersOutlinedInput-notchedOutline {
  border-color: #d32f2f;
}

.emotion-1.Mui-focused:not(.Mui-error) .MuiPickersOutlinedInput-notchedOutline {
  border-color: #1976d2;
}

.emotion-2 {
  direction: ltr;
  outline: none;
  padding: 4px 0 5px;
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-size: inherit;
  line-height: 1.4375em;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  outline: none;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-wrap: nowrap;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  overflow: hidden;
  letter-spacing: inherit;
  width: 182px;
  color: currentColor;
  opacity: 0;
  opacity: 0.42;
  padding: 16.5px 0;
}

.emotion-3 {
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-size: inherit;
  letter-spacing: inherit;
  line-height: 1.4375em;
  display: inline-block;
  white-space: nowrap;
}

.emotion-4 {
  white-space: pre;
  white-space: pre;
  letter-spacing: inherit;
}

.emotion-5 {
  outline: none;
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  line-height: 1.4375em;
  letter-spacing: inherit;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  outline: none;
}

.emotion-15 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  max-height: 2em;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  white-space: nowrap;
  color: rgba(0, 0, 0, 0.54);
  margin-left: 8px;
}

.emotion-16 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  position: relative;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  background-color: transparent;
  outline: 0;
  border: 0;
  margin: 0;
  border-radius: 0;
  padding: 0;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  vertical-align: middle;
  -moz-appearance: none;
  -webkit-appearance: none;
  -webkit-text-decoration: none;
  text-decoration: none;
  color: inherit;
  text-align: center;
  -webkit-flex: 0 0 auto;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  font-size: 1.5rem;
  padding: 8px;
  border-radius: 50%;
  color: rgba(0, 0, 0, 0.54);
  -webkit-transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  --IconButton-hoverBg: rgba(0, 0, 0, 0.04);
  margin-right: -12px;
}

.emotion-16::-moz-focus-inner {
  border-style: none;
}

.emotion-16.Mui-disabled {
  pointer-events: none;
  cursor: default;
}

@media print {
  .emotion-16 {
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
}

.emotion-16:hover {
  background-color: var(--IconButton-hoverBg);
}

@media (hover: none) {
  .emotion-16:hover {
    background-color: transparent;
  }
}

.emotion-16.Mui-disabled {
  background-color: transparent;
  color: rgba(0, 0, 0, 0.26);
}

.emotion-16.MuiIconButton-loading {
  color: transparent;
}

.emotion-17 {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  width: 1em;
  height: 1em;
  display: inline-block;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  -webkit-transition: fill 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: fill 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  fill: currentColor;
  font-size: 1.5rem;
}

.emotion-18 {
  text-align: left;
  position: absolute;
  bottom: 0;
  right: 0;
  top: -5px;
  left: 0;
  margin: 0;
  padding: 0 8px;
  pointer-events: none;
  border-radius: inherit;
  border-style: solid;
  border-width: 1px;
  overflow: hidden;
  min-width: 0%;
  border-color: rgba(0, 0, 0, 0.23);
}

.emotion-19 {
  float: unset;
  width: auto;
  overflow: hidden;
  padding: 0;
  line-height: 11px;
  -webkit-transition: width 150ms cubic-bezier(0.0, 0, 0.2, 1) 0ms;
  transition: width 150ms cubic-bezier(0.0, 0, 0.2, 1) 0ms;
}

.emotion-20 {
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-size: inherit;
}

.emotion-21 {
  border: 0;
  clip: rect(0 0 0 0);
  height: 1px;
  margin: -1px;
  overflow: hidden;
  padding: 0;
  position: absolute;
  white-space: nowrap;
  width: 1px;
}

<div
    class="MuiFormControl-root MuiPickersTextField-root Mui-disabled pd-w-full emotion-0"
  >
    <div
      aria-invalid="false"
      class="MuiPickersInputBase-root MuiPickersOutlinedInput-root Mui-disabled MuiPickersInputBase-colorPrimary MuiPickersInputBase-adornedEnd 
 !pd-h-[38px] 
 !pd-rounded-md 
 !pd-text-[13px] 
 !pd-text-dark-700
 focus:!pd-outline-1 
 [&.Mui-disabled]:!pd-bg-lightGray 
 [&.Mui-disabled]:!pd-text-dark-500 
 [&.Mui-disabled]:!pd-cursor-not-allowed 
 [&.Mui-focused:not(.Mui-error)>fieldset]:!pd-border-positive
 [&>fieldset]:!pd-border-1
 [&>fieldset]:!pd-border
 [&>fieldset]:!pd-border-line
 [&.Mui-error>fieldset]:!pd-border-negative
 undefined emotion-1"
      role="group"
    >
      <div
        class="MuiPickersSectionList-root MuiPickersInputBase-sectionsContainer emotion-2"
        contenteditable="false"
        tabindex="0"
      >
        <span
          class="MuiPickersSectionList-section emotion-3"
          data-sectionindex="0"
        >
          <span
            class="MuiPickersInputBase-sectionBefore emotion-4"
          />
          <span
            aria-disabled="true"
            aria-label="Dia"
            aria-labelledby=":r9:-day"
            aria-readonly="false"
            aria-valuemax="31"
            aria-valuemin="1"
            aria-valuetext="Vacío"
            class="MuiPickersSectionList-sectionContent MuiPickersInputBase-sectionContent emotion-5"
            contenteditable="false"
            id=":r9:-day"
            inputmode="numeric"
            role="spinbutton"
            tabindex="0"
          >
            DD
          </span>
          <span
            class="MuiPickersInputBase-sectionAfter emotion-4"
          >
            /
          </span>
        </span>
        <span
          class="MuiPickersSectionList-section emotion-3"
          data-sectionindex="1"
        >
          <span
            class="MuiPickersInputBase-sectionBefore emotion-4"
          />
          <span
            aria-disabled="true"
            aria-label="Mes"
            aria-labelledby=":r9:-month"
            aria-readonly="false"
            aria-valuemax="12"
            aria-valuemin="1"
            aria-valuetext="Vacío"
            class="MuiPickersSectionList-sectionContent MuiPickersInputBase-sectionContent emotion-5"
            contenteditable="false"
            id=":r9:-month"
            inputmode="numeric"
            role="spinbutton"
            tabindex="-1"
          >
            MM
          </span>
          <span
            class="MuiPickersInputBase-sectionAfter emotion-4"
          >
            /
          </span>
        </span>
        <span
          class="MuiPickersSectionList-section emotion-3"
          data-sectionindex="2"
        >
          <span
            class="MuiPickersInputBase-sectionBefore emotion-4"
          />
          <span
            aria-disabled="true"
            aria-label="Año"
            aria-labelledby=":r9:-year"
            aria-readonly="false"
            aria-valuemax="9999"
            aria-valuemin="0"
            aria-valuetext="Vacío"
            class="MuiPickersSectionList-sectionContent MuiPickersInputBase-sectionContent emotion-5"
            contenteditable="false"
            id=":r9:-year"
            inputmode="numeric"
            role="spinbutton"
            tabindex="-1"
          >
            AAAA
          </span>
          <span
            class="MuiPickersInputBase-sectionAfter emotion-4"
          />
        </span>
      </div>
      <div
        class="MuiInputAdornment-root MuiInputAdornment-positionEnd MuiInputAdornment-outlined MuiInputAdornment-sizeMedium emotion-15"
      >
        <button
          aria-label="Elige fecha"
          class="MuiButtonBase-root Mui-disabled MuiIconButton-root Mui-disabled MuiIconButton-edgeEnd MuiIconButton-sizeMedium emotion-16"
          disabled=""
          tabindex="-1"
          type="button"
        >
          <svg
            aria-hidden="true"
            class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium emotion-17"
            data-testid="CalendarIcon"
            focusable="false"
            viewBox="0 0 24 24"
          >
            <path
              d="M17 12h-5v5h5v-5zM16 1v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2h-1V1h-2zm3 18H5V8h14v11z"
            />
          </svg>
        </button>
      </div>
      <fieldset
        aria-hidden="true"
        class="MuiPickersOutlinedInput-notchedOutline emotion-18"
      >
        <legend
          class="emotion-19"
        >
          <span
            class="notranslate emotion-20"
          >
            ​
          </span>
        </legend>
      </fieldset>
      <input
        aria-hidden="true"
        class="MuiPickersInputBase-input MuiPickersOutlinedInput-input emotion-21"
        data-testid="date-input-test-date-snapshot-disabled"
        disabled=""
        id=":r8:"
        name="test-date-snapshot-disabled"
        tabindex="-1"
        value=""
      />
    </div>
  </div>
</DocumentFragment>
`;

exports[`DateInputComponent matches snapshot - with initial value 1`] = `
<DocumentFragment>
  .emotion-0 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-flex-direction: column;
  -ms-flex-direction: column;
  flex-direction: column;
  position: relative;
  min-width: 0;
  padding: 0;
  margin: 0;
  border: 0;
  vertical-align: top;
}

.emotion-1 {
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-weight: 400;
  font-size: 1rem;
  line-height: 1.5;
  letter-spacing: 0.00938em;
  color: rgba(0, 0, 0, 0.87);
  cursor: text;
  padding: 0;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  position: relative;
  box-sizing: border-box;
  padding: 0 14px;
  border-radius: 4px;
}

.emotion-1:hover .MuiPickersOutlinedInput-notchedOutline {
  border-color: rgba(0, 0, 0, 0.87);
}

@media (hover: none) {
  .emotion-1:hover .MuiPickersOutlinedInput-notchedOutline {
    border-color: rgba(0, 0, 0, 0.23);
  }
}

.emotion-1.Mui-focused .MuiPickersOutlinedInput-notchedOutline {
  border-style: solid;
  border-width: 2px;
}

.emotion-1.Mui-disabled .MuiPickersOutlinedInput-notchedOutline {
  border-color: rgba(0, 0, 0, 0.26);
}

.emotion-1.Mui-disabled * {
  color: rgba(0, 0, 0, 0.26);
}

.emotion-1.Mui-error .MuiPickersOutlinedInput-notchedOutline {
  border-color: #d32f2f;
}

.emotion-1.Mui-focused:not(.Mui-error) .MuiPickersOutlinedInput-notchedOutline {
  border-color: #1976d2;
}

.emotion-2 {
  direction: ltr;
  outline: none;
  padding: 4px 0 5px;
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-size: inherit;
  line-height: 1.4375em;
  -webkit-box-flex: 1;
  -webkit-flex-grow: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  outline: none;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-flex-wrap: nowrap;
  -webkit-flex-wrap: nowrap;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  overflow: hidden;
  letter-spacing: inherit;
  width: 182px;
  padding: 16.5px 0;
}

.emotion-3 {
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-size: inherit;
  letter-spacing: inherit;
  line-height: 1.4375em;
  display: inline-block;
  white-space: nowrap;
}

.emotion-4 {
  white-space: pre;
  white-space: pre;
  letter-spacing: inherit;
}

.emotion-5 {
  outline: none;
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  line-height: 1.4375em;
  letter-spacing: inherit;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  outline: none;
}

.emotion-15 {
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  max-height: 2em;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  white-space: nowrap;
  color: rgba(0, 0, 0, 0.54);
  margin-left: 8px;
}

.emotion-16 {
  display: -webkit-inline-box;
  display: -webkit-inline-flex;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
  position: relative;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  background-color: transparent;
  outline: 0;
  border: 0;
  margin: 0;
  border-radius: 0;
  padding: 0;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  vertical-align: middle;
  -moz-appearance: none;
  -webkit-appearance: none;
  -webkit-text-decoration: none;
  text-decoration: none;
  color: inherit;
  text-align: center;
  -webkit-flex: 0 0 auto;
  -ms-flex: 0 0 auto;
  flex: 0 0 auto;
  font-size: 1.5rem;
  padding: 8px;
  border-radius: 50%;
  color: rgba(0, 0, 0, 0.54);
  -webkit-transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  --IconButton-hoverBg: rgba(0, 0, 0, 0.04);
  margin-right: -12px;
}

.emotion-16::-moz-focus-inner {
  border-style: none;
}

.emotion-16.Mui-disabled {
  pointer-events: none;
  cursor: default;
}

@media print {
  .emotion-16 {
    -webkit-print-color-adjust: exact;
    color-adjust: exact;
  }
}

.emotion-16:hover {
  background-color: var(--IconButton-hoverBg);
}

@media (hover: none) {
  .emotion-16:hover {
    background-color: transparent;
  }
}

.emotion-16.Mui-disabled {
  background-color: transparent;
  color: rgba(0, 0, 0, 0.26);
}

.emotion-16.MuiIconButton-loading {
  color: transparent;
}

.emotion-17 {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  width: 1em;
  height: 1em;
  display: inline-block;
  -webkit-flex-shrink: 0;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  -webkit-transition: fill 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: fill 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  fill: currentColor;
  font-size: 1.5rem;
}

.emotion-18 {
  text-align: left;
  position: absolute;
  bottom: 0;
  right: 0;
  top: -5px;
  left: 0;
  margin: 0;
  padding: 0 8px;
  pointer-events: none;
  border-radius: inherit;
  border-style: solid;
  border-width: 1px;
  overflow: hidden;
  min-width: 0%;
  border-color: rgba(0, 0, 0, 0.23);
}

.emotion-19 {
  float: unset;
  width: auto;
  overflow: hidden;
  padding: 0;
  line-height: 11px;
  -webkit-transition: width 150ms cubic-bezier(0.0, 0, 0.2, 1) 0ms;
  transition: width 150ms cubic-bezier(0.0, 0, 0.2, 1) 0ms;
}

.emotion-20 {
  font-family: "Roboto","Helvetica","Arial",sans-serif;
  font-size: inherit;
}

.emotion-21 {
  border: 0;
  clip: rect(0 0 0 0);
  height: 1px;
  margin: -1px;
  overflow: hidden;
  padding: 0;
  position: absolute;
  white-space: nowrap;
  width: 1px;
}

<div
    class="MuiFormControl-root MuiPickersTextField-root pd-w-full emotion-0"
  >
    <div
      aria-invalid="false"
      class="MuiPickersInputBase-root MuiPickersOutlinedInput-root MuiPickersInputBase-colorPrimary MuiPickersInputBase-adornedEnd 
 !pd-h-[38px] 
 !pd-rounded-md 
 !pd-text-[13px] 
 !pd-text-dark-700
 focus:!pd-outline-1 
 [&.Mui-disabled]:!pd-bg-lightGray 
 [&.Mui-disabled]:!pd-text-dark-500 
 [&.Mui-disabled]:!pd-cursor-not-allowed 
 [&.Mui-focused:not(.Mui-error)>fieldset]:!pd-border-positive
 [&>fieldset]:!pd-border-1
 [&>fieldset]:!pd-border
 [&>fieldset]:!pd-border-line
 [&.Mui-error>fieldset]:!pd-border-negative
 undefined emotion-1"
      role="group"
    >
      <div
        class="MuiPickersSectionList-root MuiPickersInputBase-sectionsContainer emotion-2"
        contenteditable="false"
        tabindex="0"
      >
        <span
          class="MuiPickersSectionList-section emotion-3"
          data-sectionindex="0"
        >
          <span
            class="MuiPickersInputBase-sectionBefore emotion-4"
          />
          <span
            aria-disabled="false"
            aria-label="Dia"
            aria-labelledby=":r5:-day"
            aria-readonly="false"
            aria-valuemax="31"
            aria-valuemin="1"
            aria-valuenow="26"
            aria-valuetext="26º"
            autocapitalize="off"
            autocorrect="off"
            class="MuiPickersSectionList-sectionContent MuiPickersInputBase-sectionContent emotion-5"
            contenteditable="true"
            enterkeyhint="next"
            id=":r5:-day"
            inputmode="numeric"
            role="spinbutton"
            spellcheck="false"
            tabindex="0"
          >
            26
          </span>
          <span
            class="MuiPickersInputBase-sectionAfter emotion-4"
          >
            /
          </span>
        </span>
        <span
          class="MuiPickersSectionList-section emotion-3"
          data-sectionindex="1"
        >
          <span
            class="MuiPickersInputBase-sectionBefore emotion-4"
          />
          <span
            aria-disabled="false"
            aria-label="Mes"
            aria-labelledby=":r5:-month"
            aria-readonly="false"
            aria-valuemax="12"
            aria-valuemin="1"
            aria-valuenow="10"
            aria-valuetext="Octubre"
            autocapitalize="off"
            autocorrect="off"
            class="MuiPickersSectionList-sectionContent MuiPickersInputBase-sectionContent emotion-5"
            contenteditable="true"
            enterkeyhint="next"
            id=":r5:-month"
            inputmode="numeric"
            role="spinbutton"
            spellcheck="false"
            tabindex="-1"
          >
            10
          </span>
          <span
            class="MuiPickersInputBase-sectionAfter emotion-4"
          >
            /
          </span>
        </span>
        <span
          class="MuiPickersSectionList-section emotion-3"
          data-sectionindex="2"
        >
          <span
            class="MuiPickersInputBase-sectionBefore emotion-4"
          />
          <span
            aria-disabled="false"
            aria-label="Año"
            aria-labelledby=":r5:-year"
            aria-readonly="false"
            aria-valuemax="9999"
            aria-valuemin="0"
            aria-valuenow="2023"
            autocapitalize="off"
            autocorrect="off"
            class="MuiPickersSectionList-sectionContent MuiPickersInputBase-sectionContent emotion-5"
            contenteditable="true"
            enterkeyhint="next"
            id=":r5:-year"
            inputmode="numeric"
            role="spinbutton"
            spellcheck="false"
            tabindex="-1"
          >
            2023
          </span>
          <span
            class="MuiPickersInputBase-sectionAfter emotion-4"
          />
        </span>
      </div>
      <div
        class="MuiInputAdornment-root MuiInputAdornment-positionEnd MuiInputAdornment-outlined MuiInputAdornment-sizeMedium emotion-15"
      >
        <button
          aria-label="Elige fecha, la fecha elegida es 26 de Oct. de 2023"
          class="MuiButtonBase-root MuiIconButton-root MuiIconButton-edgeEnd MuiIconButton-sizeMedium emotion-16"
          tabindex="0"
          type="button"
        >
          <svg
            aria-hidden="true"
            class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium emotion-17"
            data-testid="CalendarIcon"
            focusable="false"
            viewBox="0 0 24 24"
          >
            <path
              d="M17 12h-5v5h5v-5zM16 1v2H8V1H6v2H5c-1.11 0-1.99.9-1.99 2L3 19c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2h-1V1h-2zm3 18H5V8h14v11z"
            />
          </svg>
        </button>
      </div>
      <fieldset
        aria-hidden="true"
        class="MuiPickersOutlinedInput-notchedOutline emotion-18"
      >
        <legend
          class="emotion-19"
        >
          <span
            class="notranslate emotion-20"
          >
            ​
          </span>
        </legend>
      </fieldset>
      <input
        aria-hidden="true"
        class="MuiPickersInputBase-input MuiPickersOutlinedInput-input emotion-21"
        data-testid="date-input-test-date-snapshot-value"
        id=":r4:"
        name="test-date-snapshot-value"
        tabindex="-1"
        value="26/10/2023"
      />
    </div>
  </div>
</DocumentFragment>
`;
