import styled from '@emotion/styled';

import Theme from '../../configurations/Theme.Configuration';

export interface StyledModalProps {
    isOpen: boolean;
}

export interface StyledProfileAvatarProps {
    src: string;
}

export const StyledHeader = styled.header`
    display: flex;
    justify-content: space-between;
    height: 5rem;
    width: 100%;
    align-items: center;
    padding: 12px;
    background-color: ${Theme.colors.dark[200]};
    `;

export const StyledHeaderLogoContainer = styled.div`
    max-width: 150px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 6px;
    `;

export const StyledHeaderLogo = styled.img`
    height: 1.5rem;
    width: auto;
    `;

export const StyledLogoText = styled.span`
    font-size: ${Theme.fontSize.xxsm};
    color: ${Theme.colors.dark[600]};
`;

export const StyledHeaderNav = styled.nav`
    display: flex;
    align-items: center;
    gap: 1rem;
    `;

export const StyledHeaderNavItem = styled.div`
    background-color: ${Theme.colors.white};
    border-radius: ${Theme.borderRadius.DEFAULT};
    padding: 4px 12px;
    display: flex;
    gap: 1rem;
    justify-content: space-between;
    align-items: center;
    min-width: 10rem;
    cursor: pointer;
    position: relative;
    box-shadow: ${Theme.shadow.lg};
    z-index: 50;
    `;

export const StyledProfileAvatar = styled.div <StyledProfileAvatarProps>`
    height: 2rem;
    width: 2rem;
    border-radius: 50%;
    ${(props) => props.src && 'background-size: cover;'};
    ${(props) => props.src && `background-image: url(${props.src})`};
    ${(props) => props.src && 'background-position: center'};
`;

export const StyledProfileTextAvatar = styled.div`
    height: 2rem;
    width: 2rem;
    border-radius: 50%;
    background-color: ${Theme.colors.dark[400]};
    color: ${Theme.colors.white};
    font-size: ${Theme.fontSize.lg};
    display: flex;
    justify-content: center;
    align-items: center;
    `;

export const StyledProfileSubMenu = styled.div`
    background-color: ${Theme.colors.white};
    position: absolute;
    top: 90%;
    left: 0;
    right: 0;
    padding: 12px 0 8px 0;
    border-bottom-left-radius: ${Theme.borderRadius.DEFAULT};
    border-bottom-right-radius: ${Theme.borderRadius.DEFAULT};
    display: flex;
    flex-direction: column;
    box-shadow: ${Theme.shadow.lg};
`;

export const StyledHeaderNavItemText = styled.div`
    display: flex;
    flex-direction: column;
    flex-grow: 1;
    `;

export const StyledNotificationsContainer = styled.div`
    position: relative;
    z-index: 10;
`;

export const StyledHeaderNavNotificationButton = styled.div`
    background-color: ${Theme.colors.transparent};
    height: 3rem;
    width: 3rem;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 20;
    position: relative;
    transition: all 300ms ease-in-out;

    &:hover {
        color: ${Theme.colors.primary};
    }
`;

export const StyledNotificationsMenuContainer = styled.div<StyledModalProps>`
    position: absolute;
    top: 0;
    right: 0;
    background-color: ${Theme.colors.white};
    border-radius: ${Theme.borderRadius.DEFAULT};
    box-shadow: ${Theme.shadow.lg};
    padding: 1rem;
    width: 12rem;
    display: flex;
    flex-direction: column;
    gap: 4px;

    opacity: ${(props) => (props.isOpen ? '1' : '0')};
    max-height: ${(props) => (props.isOpen ? '20rem' : '0')};
    overflow: hidden;
    transition: all 300ms ease-in-out;
    `;

export const StyledNotificationsMenuMessages = styled.div`
    display: flex;
    flex-direction: column;
`;
