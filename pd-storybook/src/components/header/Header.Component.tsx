import { useEffect, useRef, useState } from 'react';

import { Button, ButtonProps } from '../button/Button.Component';
import { IconImporter } from '../iconImporter/IconImporter.Component';
import { RouteLink, RouteLinkProps } from '../routeLink/RouteLink.Component';
import { SearchBox, SearchBoxProps } from '../searchBox/SearchBox.Component';
import { Title } from '../title/Title.Component';

import {
  StyledHeader,
  StyledHeaderLogo,
  StyledHeaderLogoContainer,
  StyledHeaderNav,
  StyledHeaderNavItem,
  StyledHeaderNavItemText,
  StyledHeaderNavNotificationButton,
  StyledNotificationsContainer,
  StyledNotificationsMenuContainer,
  StyledNotificationsMenuMessages,
  StyledProfileAvatar,
  StyledProfileSubMenu,
  StyledProfileTextAvatar,
} from './Header.Style';

export interface NotificationsProps {
  id: string;
  message: string;
  to: string;
}

export interface ProfileUserProps {
  userName: string;
  userProfilePhoto?: string;
  userRol?: string;
}

export type HeaderProps = {
  logo: string;
  searchBox?: SearchBoxProps;
  notifications?: NotificationsProps[] | null;
  profileUserInfo: ProfileUserProps;
  profileUserSubmenu: RouteLinkProps[];
  logoutButton: ButtonProps;
  className?: string;
};

export const Header: React.FC<HeaderProps> = ({
  logo,
  searchBox,
  notifications,
  profileUserInfo,
  profileUserSubmenu,
  logoutButton,
  className,
  ...props
}) => {
  const [isNotificationOpen, setIsNotificationOpen] = useState(false);
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);
  const notificationsRef = useRef<HTMLDivElement>(null);
  const userMenuRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (notificationsRef.current && !notificationsRef.current.contains(event.target as Node)) {
        setIsNotificationOpen(false);
      }
      if (userMenuRef.current && !userMenuRef.current.contains(event.target as Node)) {
        setIsUserMenuOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleNotification = () => {
    setIsNotificationOpen(!isNotificationOpen);
  };

  const handleUserMenu = () => {
    setIsUserMenuOpen(!isUserMenuOpen);
  };

  return (
    <StyledHeader className={className} {...props}>
      <StyledHeaderLogoContainer>
        <StyledHeaderLogo src={logo} alt="Genia Logo" />
      </StyledHeaderLogoContainer>
      {searchBox && <SearchBox className="pd-bg-transparent" {...searchBox} />}
      <StyledHeaderNav>
        <StyledNotificationsContainer ref={notificationsRef}>
          <StyledHeaderNavNotificationButton
            data-testid="notificationButton"
            onClick={handleNotification}
          >
            <IconImporter name={isNotificationOpen ? 'x' : 'bell'} />
          </StyledHeaderNavNotificationButton>
          {isNotificationOpen && (
            <StyledNotificationsMenuContainer isOpen={isNotificationOpen}>
              <Title as="h4" size="xsm" weight="regular">
                Notifications
              </Title>
              {notifications?.length ? (
                <StyledNotificationsMenuMessages>
                  {notifications.map((notification) => (
                    <RouteLink
                      to={notification.to}
                      className="!pd-text-dark-600 pd-py-2 pd-px-3 hover:pd-bg-slate-200 hover:!pd-text-dark-500 hover:pd-rounded"
                      size="xxsm"
                      weight="light"
                      key={notification.id}
                    >
                      {notification.message}
                    </RouteLink>
                  ))}
                </StyledNotificationsMenuMessages>
              ) : (
                <Title as="h5" size="xxsm" weight="light" data-testid="noNotifications">
                  No notifications
                </Title>
              )}
            </StyledNotificationsMenuContainer>
          )}
        </StyledNotificationsContainer>

        <StyledHeaderNavItem data-testid="profileNavItem" onClick={handleUserMenu} ref={userMenuRef}>
          <StyledProfileAvatar data-testid="profileAvatar" src={profileUserInfo.userProfilePhoto || ''}>
            {!profileUserInfo.userProfilePhoto && (
              <StyledProfileTextAvatar data-testid="profileTextAvatar">
                {profileUserInfo.userName.charAt(0)}
              </StyledProfileTextAvatar>
            )}
          </StyledProfileAvatar>
          <StyledHeaderNavItemText>
            <Title as="h5" size="xsm">
              {profileUserInfo.userName || 'User Name'}
            </Title>
            <Title as="h6" size="xxsm" weight="light" className="!pd-text-dark-400">
              {profileUserInfo.userRol || 'User Role'}
            </Title>
          </StyledHeaderNavItemText>
          <IconImporter name="caretDown" />
          {isUserMenuOpen && (
            <StyledProfileSubMenu>
              {profileUserSubmenu && profileUserSubmenu.map((link) => (
                <RouteLink
                  key={link.id}
                  {...link}
                  className="!pd-text-dark-600 !pd-text-xsm hover:pd-bg-dark-300 pd-rounded !pd-px-3 pd-py-1"
                >
                  {link.children}
                </RouteLink>
              ))}
              <Button
                className="!pd-w-full !pd-bg-transparent !pd-text-dark-600 !pd-text-xsm hover:!pd-bg-dark-300 pd-rounded !pd-px-3 !pd-py-1 !pd-text-left"
                data-testid="logoutButton"
                onClick={logoutButton.onClick}
              >
                {logoutButton.children || 'Logout'}
              </Button>
            </StyledProfileSubMenu>
          )}
        </StyledHeaderNavItem>
      </StyledHeaderNav>
    </StyledHeader>
  );
};
