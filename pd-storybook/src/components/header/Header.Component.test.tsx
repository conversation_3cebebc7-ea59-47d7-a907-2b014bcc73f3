import { fireEvent, render, screen } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';

import { Header, HeaderProps } from './Header.Component';

describe('Header Component', () => {
  const defaultProps: HeaderProps = {
    logo: 'logo.png',
    profileUserInfo: {
      userName: '<PERSON>',
      userProfilePhoto: 'profile.jpg',
      userRol: 'Admin',
    },
    profileUserSubmenu: [
      {
        id: '1',
        to: '/profile',
        children: 'Profile',
      },
      {
        id: '2',
        to: '/settings',
        children: 'Settings',
      },
    ],
    logoutButton: {
      onClick: jest.fn(),
      children: 'Logout',
    },
  };

  const renderHeader = (props?: Partial<HeaderProps>) => render(
    <BrowserRouter>
      <Header {...defaultProps} {...props} />
    </BrowserRouter>,
  );

  test('renders the logo with correct src and alt text', () => {
    renderHeader();
    const logo = screen.getByAltText('Genia Logo');
    expect(logo).toBeInTheDocument();
    expect(logo).toHaveAttribute('src', 'logo.png');
  });

  test('renders search box when searchBox prop is provided', () => {
    const searchBoxProps = {
      placeholder: 'Search...',
      suggestions: [],
      onValueChange: jest.fn(),
      onSearchChange: jest.fn(),
      isLoading: false,
    };
    renderHeader({ searchBox: searchBoxProps });
    const searchBox = screen.getByTestId('searchBoxInput');
    expect(searchBox).toBeInTheDocument();
  });

  test('does not render search box when searchBox prop is not provided', () => {
    renderHeader({ searchBox: undefined });
    const searchBox = screen.queryByTestId('searchBoxInput');
    expect(searchBox).not.toBeInTheDocument();
  });

  test('search box handles input changes correctly', () => {
    const searchBoxProps = {
      placeholder: 'Search...',
      suggestions: [],
      onValueChange: jest.fn(),
      onSearchChange: jest.fn(),
      isLoading: false,
    };
    renderHeader({ searchBox: searchBoxProps });
    const searchBox = screen.getByTestId('searchBoxInput');

    fireEvent.change(searchBox, { target: { value: 'test' } });
    expect(searchBoxProps.onValueChange).toHaveBeenCalledWith('test');
  });

  test('search box handles suggestions correctly', () => {
    const suggestions = [
      { id: 1, title: 'Suggestion 1' },
      { id: 2, title: 'Suggestion 2' },
    ];
    const searchBoxProps = {
      placeholder: 'Search...',
      suggestions,
      onValueChange: jest.fn(),
      onSearchChange: jest.fn(),
      isLoading: false,
    };
    renderHeader({ searchBox: searchBoxProps });
    const searchBox = screen.getByTestId('searchBoxInput');

    fireEvent.change(searchBox, { target: { value: 'Sugg' } });
    expect(screen.getByText('Suggestion 1')).toBeInTheDocument();
    expect(screen.getByText('Suggestion 2')).toBeInTheDocument();
  });

  test('search box shows loading state', () => {
    const searchBoxProps = {
      placeholder: 'Search...',
      suggestions: [],
      onValueChange: jest.fn(),
      onSearchChange: jest.fn(),
      isLoading: true,
    };
    renderHeader({ searchBox: searchBoxProps });
    const searchBox = screen.getByTestId('searchBoxInput');

    fireEvent.change(searchBox, { target: { value: 'test' } });
    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  test('toggles notifications menu when notification button is clicked', () => {
    const notifications = [
      {
        id: '1',
        message: 'Notification 1',
        to: '/notification/1',
      },
    ];
    renderHeader({ notifications });
    const notificationButton = screen.getByTestId('notificationButton');
    fireEvent.click(notificationButton);
    expect(screen.getByText('Notifications')).toBeInTheDocument();
    expect(screen.getByText('Notification 1')).toBeInTheDocument();
    fireEvent.click(notificationButton);
    expect(screen.queryByText('Notifications')).not.toBeInTheDocument();
  });

  test('displays "No notifications" when there are no notifications', () => {
    renderHeader({ notifications: [] });
    const notificationButton = screen.getByTestId('notificationButton');
    fireEvent.click(notificationButton);
    expect(screen.getByText('No notifications')).toBeInTheDocument();
  });

  test('displays "No notifications" when notifications prop is undefined', () => {
    renderHeader({ notifications: undefined });
    const notificationButton = screen.getByTestId('notificationButton');
    fireEvent.click(notificationButton);
    expect(screen.getByText('No notifications')).toBeInTheDocument();
  });

  test('displays "No notifications" when notifications prop is null', () => {
    renderHeader({ notifications: null });
    const notificationButton = screen.getByTestId('notificationButton');
    fireEvent.click(notificationButton);
    expect(screen.getByText('No notifications')).toBeInTheDocument();
  });

  test('renders user profile information', () => {
    renderHeader();
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('Admin')).toBeInTheDocument();
    const profileAvatar = screen.getByTestId('profileAvatar');
    expect(profileAvatar).toHaveAttribute('src', 'profile.jpg');
  });

  test('renders user initial when no profile photo is provided', () => {
    renderHeader({
      profileUserInfo: {
        userName: 'Jane Smith',
        userProfilePhoto: '',
        userRol: 'User',
      },
    });
    const profileTextAvatar = screen.getByTestId('profileTextAvatar');
    expect(profileTextAvatar).toBeInTheDocument();
    expect(profileTextAvatar).toHaveTextContent('J');
  });

  test('toggles user menu when profile avatar is clicked', () => {
    renderHeader();
    const profileNavItem = screen.getByTestId('profileNavItem');
    fireEvent.click(profileNavItem);
    expect(screen.getByText('Profile')).toBeInTheDocument();
    expect(screen.getByText('Settings')).toBeInTheDocument();
    fireEvent.click(profileNavItem);
    expect(screen.queryByText('Profile')).not.toBeInTheDocument();
  });

  test('calls logout function when logout button is clicked', () => {
    const logoutMock = jest.fn();
    renderHeader({
      logoutButton: {
        onClick: logoutMock,
        children: 'Logout',
      },
    });
    const profileNavItem = screen.getByTestId('profileNavItem');
    fireEvent.click(profileNavItem);
    const logoutButton = screen.getByTestId('logoutButton');
    fireEvent.click(logoutButton);
    expect(logoutMock).toHaveBeenCalled();
  });

  test('renders notifications correctly when provided', () => {
    const notifications = [
      {
        id: '1',
        message: 'New message received',
        to: '/messages/1',
      },
      {
        id: '2',
        message: 'Your order has been shipped',
        to: '/orders/123',
      },
    ];
    renderHeader({ notifications });
    const notificationButton = screen.getByTestId('notificationButton');
    fireEvent.click(notificationButton);
    expect(screen.getByText('Notifications')).toBeInTheDocument();
    expect(screen.getByText('New message received')).toBeInTheDocument();
    expect(screen.getByText('Your order has been shipped')).toBeInTheDocument();
  });

  test('renders profile submenu links correctly', () => {
    renderHeader();
    const profileNavItem = screen.getByTestId('profileNavItem');
    fireEvent.click(profileNavItem);
    const profileLink = screen.getByText('Profile');
    const settingsLink = screen.getByText('Settings');
    expect(profileLink).toBeInTheDocument();
    expect(profileLink).toHaveAttribute('href', '/profile');
    expect(settingsLink).toBeInTheDocument();
    expect(settingsLink).toHaveAttribute('href', '/settings');
  });

  test('renders default user name when userName is missing', () => {
    renderHeader({
      profileUserInfo: {
        userName: '',
        userProfilePhoto: '',
        userRol: 'Admin',
      },
    });
    expect(screen.getByText('User Name')).toBeInTheDocument();
    expect(screen.getByText('Admin')).toBeInTheDocument();
  });

  test('renders default user role when userRol is missing', () => {
    renderHeader({
      profileUserInfo: {
        userName: 'John Doe',
        userProfilePhoto: '',
        userRol: '',
      },
    });
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('User Role')).toBeInTheDocument();
  });

  test('renders default logout text when logoutButton.children is missing', () => {
    const logoutMock = jest.fn();
    renderHeader({
      logoutButton: {
        onClick: logoutMock,
        children: undefined,
      },
    });
    const profileNavItem = screen.getByTestId('profileNavItem');
    fireEvent.click(profileNavItem);
    const logoutButton = screen.getByTestId('logoutButton');
    expect(logoutButton).toHaveTextContent('Logout');
  });

  test('renders provided logoutButton.children when available', () => {
    const logoutMock = jest.fn();
    renderHeader({
      logoutButton: {
        onClick: logoutMock,
        children: 'Sign Out',
      },
    });
    const profileNavItem = screen.getByTestId('profileNavItem');
    fireEvent.click(profileNavItem);
    const logoutButton = screen.getByTestId('logoutButton');
    expect(logoutButton).toHaveTextContent('Sign Out');
  });

  test('closes notifications menu when clicking outside', () => {
    const notifications = [{ id: '1', message: 'Test', to: '/test' }];
    renderHeader({ notifications });

    // Open notifications menu
    const notificationButton = screen.getByTestId('notificationButton');
    fireEvent.click(notificationButton);
    expect(screen.getByText('Notifications')).toBeInTheDocument();

    // Click outside
    fireEvent.mouseDown(document.body);
    expect(screen.queryByText('Notifications')).not.toBeInTheDocument();
  });

  test('closes user menu when clicking outside', () => {
    renderHeader();

    // Open user menu
    const profileNavItem = screen.getByTestId('profileNavItem');
    fireEvent.click(profileNavItem);
    expect(screen.getByText('Profile')).toBeInTheDocument();

    // Click outside
    fireEvent.mouseDown(document.body);
    expect(screen.queryByText('Profile')).not.toBeInTheDocument();
  });

  test('keeps menus open when clicking inside', () => {
    const notifications = [{ id: '1', message: 'Test', to: '/test' }];
    renderHeader({ notifications });

    // Test notifications menu
    const notificationButton = screen.getByTestId('notificationButton');
    fireEvent.click(notificationButton);
    const notificationsMenu = screen.getByText('Notifications');
    fireEvent.mouseDown(notificationsMenu);
    expect(screen.getByText('Notifications')).toBeInTheDocument();

    // Test user menu
    const profileNavItem = screen.getByTestId('profileNavItem');
    fireEvent.click(profileNavItem);
    const userMenu = screen.getByText('Profile');
    fireEvent.mouseDown(userMenu);
    expect(screen.getByText('Profile')).toBeInTheDocument();
  });
});
