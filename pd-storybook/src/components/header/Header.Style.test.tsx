import { render } from '@testing-library/react';

import {
  Styled<PERSON>ead<PERSON>,
  Styled<PERSON><PERSON><PERSON><PERSON><PERSON>,
  StyledHeader<PERSON>ogoContainer,
  StyledHeaderNav,
  StyledHeaderNavItem,
  StyledHeaderNavItemText,
  StyledHeaderNavNotificationButton,
  StyledNotificationsContainer,
  StyledNotificationsMenuContainer,
  StyledNotificationsMenuMessages,
  StyledProfileAvatar,
  StyledProfileSubMenu,
  StyledProfileTextAvatar,
} from './Header.Style';

describe('StyledHeader Snapshots', () => {
  it('renders StyledHeader correctly', () => {
    const { asFragment } = render(<StyledHeader />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders StyledHeaderLogoContainer correctly', () => {
    const { asFragment } = render(<StyledHeaderLogoContainer />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders StyledHeader<PERSON>ogo correctly', () => {
    const { asFragment } = render(<StyledHeaderLogo src="logo.png" alt="Logo" />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders StyledHeaderNav correctly', () => {
    const { asFragment } = render(<StyledHeaderNav />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders StyledHeaderNavItem correctly', () => {
    const { asFragment } = render(<StyledHeaderNavItem />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders StyledProfileAvatar correctly', () => {
    const { asFragment } = render(<StyledProfileAvatar src="avatar.jpg" />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders StyledProfileTextAvatar correctly', () => {
    const { asFragment } = render(<StyledProfileTextAvatar>JD</StyledProfileTextAvatar>);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders StyledProfileSubMenu correctly', () => {
    const { asFragment } = render(<StyledProfileSubMenu />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders StyledHeaderNavItemText correctly', () => {
    const { asFragment } = render(<StyledHeaderNavItemText />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders StyledNotificationsContainer correctly', () => {
    const { asFragment } = render(<StyledNotificationsContainer />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders StyledHeaderNavNotificationButton correctly', () => {
    const { asFragment } = render(<StyledHeaderNavNotificationButton />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders StyledNotificationsMenuContainer correctly when open', () => {
    const { asFragment } = render(<StyledNotificationsMenuContainer isOpen={true} />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders StyledNotificationsMenuContainer correctly when closed', () => {
    const { asFragment } = render(<StyledNotificationsMenuContainer isOpen={false} />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders StyledNotificationsMenuMessages correctly', () => {
    const { asFragment } = render(<StyledNotificationsMenuMessages />);
    expect(asFragment()).toMatchSnapshot();
  });
});
