// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`StyledMenuItem Snapshots renders StyledMenuItem correctly when closed 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #F6F7FA;
  border-radius: 12px;
  padding: 12px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  cursor: pointer;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
  gap: 16px;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}

.emotion-0:hover {
  background-color: #FFFFFF;
  color: #3A3D44;
}

<div
    class="emotion-0"
  >
    Closed Menu Item
  </div>
</DocumentFragment>
`;

exports[`StyledMenuItem Snapshots renders StyledMenuItem correctly when open 1`] = `
<DocumentFragment>
  .emotion-0 {
  background-color: #FFFFFF;
  border-radius: 12px;
  padding: 12px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  cursor: pointer;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: start;
  -ms-flex-pack: start;
  -webkit-justify-content: flex-start;
  justify-content: flex-start;
  gap: 16px;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}

.emotion-0:hover {
  background-color: #FFFFFF;
  color: #3A3D44;
}

<div
    class="emotion-0"
  >
    Open Menu Item
  </div>
</DocumentFragment>
`;

exports[`StyledMenuItem Snapshots renders StyledMenuItemContainer correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  overflow: hidden;
  width: 240px;
  background-color: #F6F7FA;
}

<div
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`StyledMenuItem Snapshots renders StyledMenuItemsList correctly when closed 1`] = `
<DocumentFragment>
  .emotion-0 {
  max-height: 0;
  overflow: hidden;
  transform-origin: top;
  -webkit-transform: scaleY(0);
  -moz-transform: scaleY(0);
  -ms-transform: scaleY(0);
  transform: scaleY(0);
  opacity: 0;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
  margin-top: 0;
  margin-bottom: 0;
  padding-top: 0;
  padding-bottom: 0;
}

<ul
    class="emotion-0"
  >
    Closed Menu Items List
  </ul>
</DocumentFragment>
`;

exports[`StyledMenuItem Snapshots renders StyledMenuItemsList correctly when open 1`] = `
<DocumentFragment>
  .emotion-0 {
  max-height: 1000px;
  overflow: hidden;
  transform-origin: top;
  -webkit-transform: scaleY(1);
  -moz-transform: scaleY(1);
  -ms-transform: scaleY(1);
  transform: scaleY(1);
  opacity: 1;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
}

<ul
    class="emotion-0"
  >
    Open Menu Items List
  </ul>
</DocumentFragment>
`;

exports[`StyledMenuItem Snapshots renders StyledMenuSubItem correctly 1`] = `
<DocumentFragment>
  .emotion-0 {
  height: 28px;
  display: -webkit-box;
  display: -webkit-flex;
  display: -ms-flexbox;
  display: flex;
  width: 100%;
  -webkit-align-items: center;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  position: relative;
  padding-left: 52px;
  cursor: pointer;
  border-radius: 8px;
  -webkit-transition: all 300ms ease-in-out;
  transition: all 300ms ease-in-out;
  background-color: #F6F7FA;
  margin-top: 10px;
}

.emotion-0:hover {
  background-color: #FFFFFF;
}

<li
    class="emotion-0"
  >
    Menu Sub Item
  </li>
</DocumentFragment>
`;
