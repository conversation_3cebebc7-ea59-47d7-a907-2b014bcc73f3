import styled from '@emotion/styled';

import Theme from '../../configurations/Theme.Configuration';

export interface StyledMenuItemProps {
    isOpen: boolean;
}

export const StyledMenuItemContainer = styled.div`
    overflow: hidden;
    width: 240px;
    background-color: ${Theme.colors.dark[200]};
`;

export const StyledMenuItem = styled.div<StyledMenuItemProps & {active?: boolean}>`
    background-color: ${(props) => (props.isOpen || props.active ? Theme.colors.white : Theme.colors.dark[200])};
    border-radius: ${Theme.borderRadius.xl};
    padding: 12px;
    display: flex;
    cursor: pointer;
    align-items: center;
    justify-content: flex-start;
    gap: 16px;
    transition: all 300ms ease-in-out;

    &:hover {
        background-color: ${Theme.colors.white};
        color: ${Theme.colors.dark[600]};
    }
`;

export const StyledMenuItemsList = styled.ul<StyledMenuItemProps>`
    max-height: ${(props) => (props.isOpen ? '1000px' : '0')};
    overflow: hidden;
    transform-origin: top;
    transform: scaleY(${(props) => (props.isOpen ? 1 : 0)});
    opacity: ${(props) => (props.isOpen ? 1 : 0)};
    transition: all 300ms ease-in-out;
    
    ${(props) => !props.isOpen && `
        margin-top: 0;
        margin-bottom: 0;
        padding-top: 0;
        padding-bottom: 0;
    `}
`;

export const StyledMenuSubItem = styled.li<{active?: boolean}>`
    height: 28px;
    display: flex;
    width: 100%;
    align-items: center;
    position: relative;
    padding-left: 52px;
    cursor: pointer;
    border-radius: ${Theme.borderRadius.DEFAULT};
    transition: all 300ms ease-in-out;
    background-color: ${(props) => (props.active ? Theme.colors.white : Theme.colors.dark[200])};
    margin-top: 10px;

    &:hover {
        background-color: ${Theme.colors.white};
    }
`;

export const StyledMenuItemTitle = styled.span`
    flex-grow: 1;
    font-weight: ${Theme.fontWeight.regular};
    color: ${Theme.colors.dark[600]};
`;
