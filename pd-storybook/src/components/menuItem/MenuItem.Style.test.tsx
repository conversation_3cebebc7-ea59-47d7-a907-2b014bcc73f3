import { render } from '@testing-library/react';

import {
  StyledMenuItem,
  StyledMenuItemContainer,
  StyledMenuItemProps,
  StyledMenuItemsList,
  StyledMenuSubItem,
} from './MenuItem.Style';

describe('StyledMenuItem Snapshots', () => {
  it('renders StyledMenuItemContainer correctly', () => {
    const { asFragment } = render(<StyledMenuItemContainer />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders StyledMenuItem correctly when open', () => {
    const props: StyledMenuItemProps = { isOpen: true };
    const { asFragment } = render(<StyledMenuItem {...props} >Open Menu Item</StyledMenuItem>);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders StyledMenuItem correctly when closed', () => {
    const props: StyledMenuItemProps = { isOpen: false };
    const { asFragment } = render(<StyledMenuItem {...props}>Closed Menu Item</StyledMenuItem>);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders StyledMenuItemsList correctly when open', () => {
    const props: StyledMenuItemProps = { isOpen: true };
    const { asFragment } = render(<StyledMenuItemsList {...props}>Open Menu Items List</StyledMenuItemsList>);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders StyledMenuItemsList correctly when closed', () => {
    const props: StyledMenuItemProps = { isOpen: false };
    const { asFragment } = render(<StyledMenuItemsList {...props}>Closed Menu Items List</StyledMenuItemsList>);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders StyledMenuSubItem correctly', () => {
    const { asFragment } = render(<StyledMenuSubItem>Menu Sub Item</StyledMenuSubItem>);
    expect(asFragment()).toMatchSnapshot();
  });
});
