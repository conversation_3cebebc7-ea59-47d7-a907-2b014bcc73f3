import React, { useEffect, useState } from 'react';

import Theme from '../../configurations/Theme.Configuration';
import { IconImporter } from '../iconImporter/IconImporter.Component';
import { IconName } from '../iconImporter/IconMap.Component';
import { RouteLink, RouteLinkProps } from '../routeLink/RouteLink.Component';

import {
  StyledMenuItem,
  StyledMenuItemContainer,
  StyledMenuItemsList,
  StyledMenuItemTitle,
  StyledMenuSubItem,
} from './MenuItem.Style';

export interface SubItemProps extends RouteLinkProps {
  active?: boolean;
}

export interface MenuItemWithSubItems {
  id: string;
  title: string;
  icon: IconName;
  items: SubItemProps[];
  active?: boolean;
  linkAs?: React.ElementType;
}

export interface MenuItemWithoutSubItems {
  id: string;
  title: string;
  icon: IconName;
  to: string; // requerido cuando no hay subitems
  active?: boolean;
  linkAs?: React.ElementType;
}

export type MenuItemProps = MenuItemWithSubItems | MenuItemWithoutSubItems;

export const MenuItem: React.FC<MenuItemProps> = (props) => {
  const { items } = props as MenuItemWithSubItems;
  const {
    title, icon, to, active = false, linkAs,
  } = props as MenuItemWithoutSubItems;

  const [isOpen, setIsOpen] = useState(false);
  const hasSubItems = items && items.length > 0;

  const handleClick = () => {
    if (hasSubItems) {
      setIsOpen(!isOpen);
    }
  };

  useEffect(() => {
    setIsOpen(active);
  }, [active]);

  const iconColor = () => ((isOpen || active) ? Theme.colors.secondary : Theme.colors.dark[700]);

  return (
    <StyledMenuItemContainer>
      {hasSubItems ? (
        <StyledMenuItem
          active={active}
          onClick={handleClick}
          isOpen={isOpen}
        >
          <IconImporter
            className="pd-transition-all pd-duration-300 pd-ease-in-out"
            name={icon}
            color={iconColor()}
            size="24"
            data-testid="menu-item-icon"
          />
          <StyledMenuItemTitle>{title}</StyledMenuItemTitle>
          <IconImporter
            size="16"
            name="caretRight"
            className={`pd-transition-transform pd-duration-300 ${isOpen ? 'pd-rotate-90' : ''}`}
          />
        </StyledMenuItem>
      ) : (
        <RouteLink
          color={Theme.colors.dark[600]}
          to={to}
          as={linkAs}
        >
          <StyledMenuItem active={active} isOpen={isOpen}>
            <IconImporter
              className="pd-transition-all pd-duration-300 pd-ease-in-out"
              name={icon}
              color={iconColor()}
              size="24"
              data-testid="menu-item-icon"
            />
            <StyledMenuItemTitle>{title}</StyledMenuItemTitle>
          </StyledMenuItem>
        </RouteLink>
      )}
      {hasSubItems && (
        <StyledMenuItemsList isOpen={isOpen}>
          {items.map((item) => (
            <RouteLink
              key={item.id}
              color={item.active ? Theme.colors.secondary : Theme.colors.dark[700]}
              size='xsm'
              weight={item.active ? 'regular' : 'light'}
              to={item.to}
              title={item.title}
              as={linkAs}
            >
              <StyledMenuSubItem
                active={item?.active || false}
              >
                {item.title}
              </StyledMenuSubItem>
            </RouteLink>

          ))}
        </StyledMenuItemsList>
      )}
    </StyledMenuItemContainer>
  );
};
