import { fireEvent, render, screen } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';

import Theme from '../../configurations/Theme.Configuration';

import { MenuItem, MenuItemProps } from './MenuItem.Component';

const defaultProps: MenuItemProps = {
  id: '1',
  title: 'Menu Item',
  icon: 'clock',
  items: [
    {
      id: '1',
      title: 'Sub Menu Item 1',
      to: '/sub-menu-item-1',
    },
    {
      id: '2',
      title: 'Sub Menu Item 2',
      to: '/sub-menu-item-2',
    },
  ],
  to: '/menu-item',
  active: false,
};

const renderWithRouter = (ui: React.ReactElement) => render(<MemoryRouter>{ui}</MemoryRouter>);

describe('MenuItem Component', () => {
  test('renders MenuItem with title and icon', () => {
    renderWithRouter(<MenuItem {...defaultProps} />);
    expect(screen.getByText('Menu Item')).toBeInTheDocument();
    expect(screen.getByTestId('menu-item-icon')).toBeInTheDocument();
  });

  test('renders sub menu items when clicked', () => {
    renderWithRouter(<MenuItem {...defaultProps} />);
    const menuItem = screen.getByText('Menu Item');
    fireEvent.click(menuItem);
    expect(screen.getByText('Sub Menu Item 1')).toBeInTheDocument();
    expect(screen.getByText('Sub Menu Item 2')).toBeInTheDocument();
  });

  test('toggles sub menu items visibility on click', () => {
    renderWithRouter(<MenuItem {...defaultProps} />);
    const menuItem = screen.getByText('Menu Item');
    fireEvent.click(menuItem);
    expect(screen.getByText('Sub Menu Item 1')).toBeVisible();
    fireEvent.click(menuItem);
    expect(screen.queryByText('Sub Menu Item 1')).not.toBeVisible();
  });

  test('renders without sub menu items', () => {
    const propsWithoutSubItems: MenuItemProps = {
      ...defaultProps,
      items: [],
    };
    renderWithRouter(<MenuItem {...propsWithoutSubItems} />);
    expect(screen.queryByText('Sub Menu Item 1')).not.toBeInTheDocument();
  });

  test('renders RouteLink when there are no sub menu items', () => {
    const propsWithoutSubItems: MenuItemProps = {
      ...defaultProps,
      items: [],
    };
    renderWithRouter(<MenuItem {...propsWithoutSubItems} />);
    expect(screen.getByRole('link', { name: /menu item/i })).toHaveAttribute('href', '/menu-item');
  });

  test('changes icon color when clicked', () => {
    renderWithRouter(<MenuItem {...defaultProps} />);
    const menuItem = screen.getByText('Menu Item');
    const icon = screen.getByTestId('menu-item-icon');
    expect(icon).toHaveAttribute('fill', Theme.colors.dark[700]);
    fireEvent.click(menuItem);
    expect(icon).toHaveAttribute('fill', Theme.colors.secondary);
  });

  test('changes icon color when active', () => {
    const propsActive: MenuItemProps = {
      ...defaultProps,
      active: true,
    };
    renderWithRouter(<MenuItem {...propsActive} />);
    const icon = screen.getByTestId('menu-item-icon');
    expect(icon).toHaveAttribute('fill', Theme.colors.secondary);
  });

  test('changes sub menu item color when active', () => {
    const propsActive: MenuItemProps = {
      ...defaultProps,
      items: [
        {
          id: '1',
          title: 'Sub Menu Item 1',
          to: '/sub-menu-item-1',
          active: true,
        },
      ],
    };
    renderWithRouter(<MenuItem {...propsActive} />);
    const menuItem = screen.getByText('Menu Item');
    fireEvent.click(menuItem); // Open the submenu
    const subMenuLink = screen.getByRole('link', { name: 'Sub Menu Item 1' });
    expect(subMenuLink).toHaveStyle({ color: Theme.colors.secondary });
  });
});
