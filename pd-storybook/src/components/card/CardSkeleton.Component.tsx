export interface CardSkeletonProps {
  className?: string;
}

export const CardSkeleton = ({ className = '' }: CardSkeletonProps) => (
  <div className={`pd-w-fit pd-h-fit ${className}`}>
    <div className="pd-bg-white pd-flex pd-justify-center pd-p-4 pd-rounded-2xl">
      <div className="pd-flex pd-flex-col pd-justify-center pd-items-center pd-gap-2">
        <div className="pd-w-52 pd-h-52 pd-overflow-hidden">
          <div className="pd-w-full pd-h-full pd-bg-gray-200 pd-animate-pulse pd-rounded-md" />
        </div>
        <div className='pd-flex pd-flex-col pd-w-52 pd-gap-2'>
          <div className="pd-h-6 pd-bg-gray-200 pd-animate-pulse pd-rounded pd-w-full" />
          <div className="pd-h-4 pd-bg-gray-200 pd-animate-pulse pd-rounded pd-w-3/4" />
          <div className='pd-flex pd-justify-between pd-gap-2'>
            <div className='pd-flex pd-gap-2 pd-items-center'>
              <div className="pd-h-6 pd-w-16 pd-bg-gray-200 pd-animate-pulse pd-rounded" />
              <div className="pd-h-6 pd-w-10 pd-bg-gray-200 pd-animate-pulse pd-rounded" />
            </div>
            <div className="pd-h-8 pd-w-12 pd-bg-gray-200 pd-animate-pulse pd-rounded-full" />
          </div>
        </div>
      </div>
    </div>
  </div>
);
