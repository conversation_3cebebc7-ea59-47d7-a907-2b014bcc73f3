export interface DiscountItemSkeletonProps {
    className?: string;
}

export const DiscountItemSkeleton = ({ className = '' }: DiscountItemSkeletonProps) => (
  // eslint-disable-next-line max-len
  <div className={`pd-w-full pd-h-full pd-min-h-[50px] pd-bg-dark-100 pd-border pd-border-dark-200 pd-rounded-2xl pd-px-2 pd-flex pd-flex-col pd-justify-center ${className}`}>
    <div className="pd-flex pd-flex-col pd-justify-between pd-items-center pd-gap-2">
      <div className="pd-h-3 pd-bg-gray-200 pd-animate-pulse pd-rounded pd-w-full"></div>
      <div className="pd-flex pd-gap-1 pd-w-full">
        <div className="pd-h-3 pd-bg-gray-200 pd-animate-pulse pd-rounded pd-w-full"></div>
        <div className="pd-h-3 pd-bg-gray-200 pd-animate-pulse pd-rounded pd-w-full"></div>
      </div>
    </div>
  </div>
);
