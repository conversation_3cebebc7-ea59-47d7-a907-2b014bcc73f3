import React from 'react';

import Theme from '../../configurations/Theme.Configuration';
import { Tooltip } from '../tooltip/Tooltip.Component';

import { DiscountItemSkeleton } from './DiscountItemSkeleton.Component';

export interface Discount {
  id: string;
  discountValue: number;
  discountType?: string;
  priceAfterDiscount?: number;
  requiredQuantity?: number;
  requiredAmount?: number;
}

export interface DiscountItemProps {
  discount: Discount;
  nextRequired: number | null;
  isApplied?: boolean;
  measurementLabel?: string;
  currencyLabel?: string;
}

export const DiscountItem = ({
  discount,
  nextRequired,
  isApplied = false,
  measurementLabel = 'unidades',
  currencyLabel = 'MXN',
}: DiscountItemProps) => {
  const isPercentage = discount.discountType === 'percentage';
  const isRequiredQuantity = 'requiredQuantity' in discount;
  const required = isRequiredQuantity ? discount.requiredQuantity || 0 : discount.requiredAmount || 0;

  let discountRangeText = '';
  if (required === 0 && nextRequired === null) {
    discountRangeText = `+${isRequiredQuantity ? '' : '$'}${required + 1} ${isRequiredQuantity ? measurementLabel : currencyLabel}`;
  } else if (required === 0) {
    // eslint-disable-next-line max-len
    discountRangeText = `${isRequiredQuantity ? '' : '$'}${required + 1} - ${isRequiredQuantity ? '' : '$'}${nextRequired} ${isRequiredQuantity ? measurementLabel : currencyLabel}`;
  } else {
    discountRangeText = `${isRequiredQuantity ? `+${required} ${measurementLabel}` : `+$${required} ${currencyLabel}`}`;
  }

  const discountValueText = isPercentage
    ? `-${discount.discountValue}%`
    : `-$${discount.discountValue?.toFixed(2)}`;

  return (
  // eslint-disable-next-line max-len
    <div className={`pd-flex pd-flex-col pd-gap-1 pd-flex-wrap pd-p-1 pd-rounded-md pd-text-dark-700 pd-w-1/2 pd-justify-center ${isApplied ? 'pd-bg-fadedBlue' : 'pd-bg-dark-200 pd-border pd-border-dark-300'}`}>
      <div>
        <p className='pd-text-xxsm pd-font-light'>
          {discountRangeText}
        </p>
      </div>
      <div className='pd-flex pd-gap-1 pd-items-center pd-flex-wrap'>
        <p className='pd-text-sm pd-font-semiBold pd-text-dark-700'>${discount.priceAfterDiscount?.toFixed(2)}</p>
        <p className='pd-text-xxsm pd-font-light pd-text-primary'>{discountValueText}</p>
      </div>
    </div>
  );
};

export const renderDiscounts = ({
  discounts = [],
  applied,
  start,
  end,
  isLoading = false,
  defaultApplied = false,
}: {
  discounts: Discount[],
  applied?: Discount,
  start: number,
  end?: number,
  isLoading?: boolean,
  defaultApplied?: boolean,
}) => discounts?.slice(start, end)?.map((discount, index, array) => {
  const nextItem = array[index + 1];
  let nextRequired: number | null = null;
  const isApplied = defaultApplied || (applied && applied.id === discount.id);

  if (nextItem && 'requiredQuantity' in nextItem) {
    nextRequired = (nextItem.requiredQuantity ?? 0) - 1 || null;
  } else if (nextItem && 'requiredAmount' in nextItem) {
    nextRequired = (nextItem.requiredAmount ?? 0) - 1 || null;
  }
  return (
    <React.Fragment key={discount.id}>
      {isLoading
        ? <DiscountItemSkeleton className='!pd-w-1/2' />
        : <DiscountItem
            discount={discount}
            nextRequired={nextRequired}
            isApplied={isApplied}
        />
    }
    </React.Fragment>
  );
});

interface DiscountBlockWrapperProps {
  discounts: Discount[],
  applied?: Discount,
  isLoading?: boolean,
  defaultApplied?: boolean,
}

export const DiscountBlockWrapper: React.FC<DiscountBlockWrapperProps> = ({
  discounts,
  applied,
  isLoading,
  defaultApplied = false,
}) => (
  <div className='pd-flex pd-flex-col pd-gap-2 pd-text-dark-600'>
    <div className='pd-flex pd-gap-2 pd-w-full'>
      {
          renderDiscounts({
            discounts, applied, start: 0, end: 2, isLoading, defaultApplied,
          })
        }
    </div>
    {(discounts?.length > 2)
        && <Tooltip
          bgColor={Theme.colors.white}
          content={
            <div className='pd-flex pd-gap-2 pd-p-3 pd-w-full pd-min-w-52 pd-flex-wrap'>
              {
                renderDiscounts({
                  discounts, applied, start: 2, defaultApplied,
                })
              }
            </div>
          }>
          <p
            className='pd-text-xxsm pd-text-center pd-text-toscaBlue hover:pd-cursor-pointer'
            onClick={(e) => e.preventDefault()}
          >
            Más descuentos
          </p>
        </Tooltip>
      }
  </div>
);
