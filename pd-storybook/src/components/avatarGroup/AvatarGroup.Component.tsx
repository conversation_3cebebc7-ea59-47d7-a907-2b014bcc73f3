import { Avatar, AvatarProps } from '../avatar/Avatar.Component';
import { DropdownWithSearch, OptionsDropdownProps } from '../dropdown/DropdownWithSearch.Component';

import {
  AddButtonContainer, AvatarGroupContainer, AvatarGroupItem, AvatarGroupWrapper,
  RemainStyle,
} from './AvatarGroup.Style';

interface DropdownProps {
  options?: OptionsDropdownProps[];
  setSelectedOption?: (option: OptionsDropdownProps) => void;
  dropDownClassName?: string;
}

export interface AvatarGroupProps {
  itemsSelected: OptionsDropdownProps[];

  maxToShow?: number;
  readOnly?: boolean;
  direction?: 'horizontal' | 'vertical';
  avatarProps?: AvatarProps,
  dropdownProps?: DropdownProps;
}

const calculateSize = (size: string | number, factor: number) => {
  const sizeNum = Number(String(size).replace('px', ''));
  return sizeNum * factor;
};

export const AvatarGroup: React.FC<AvatarGroupProps> = ({
  itemsSelected, maxToShow = 3, readOnly, avatarProps, dropdownProps, direction = 'horizontal', ...props
}) => {
  const { options, setSelectedOption, dropDownClassName } = dropdownProps || {};

  const screenOptions = itemsSelected.slice(0, maxToShow);
  const remainingUsersCount = itemsSelected.length - screenOptions.length;

  const sizeNumber = `${avatarProps?.size || 50}`.replace('px', '');
  const width = Number(sizeNumber) * itemsSelected.length;

  const readOnlyNoData = readOnly && !screenOptions.length ? 'No existen datos' : '';

  const addButtonDiplacement = screenOptions.length * (Number(sizeNumber) - (Number(sizeNumber) * 0.40)) + 3;
  const remainDiplacement = (screenOptions.length * (Number(sizeNumber) - (Number(sizeNumber) * 0.40))) + Number(sizeNumber) + 10;

  const selectedMap = screenOptions.map((user, index) => {
    const localDisplacement = index === 0 ? 0 : index * (Number(sizeNumber) - (Number(sizeNumber) * 0.40));
    return (
      <AvatarGroupItem
        data-testid="avatar-group-item"
        key={user.id}
        index={index}
        length={screenOptions.length}
        left={direction === 'vertical' ? 0 : localDisplacement}
        top={direction === 'vertical' ? localDisplacement : 0}
        {...props}
      >
        <Avatar
          src={user.src}
          name={user.name}
          size={avatarProps?.size }
          {...avatarProps}
        />
      </AvatarGroupItem>
    );
  });

  return (
    <AvatarGroupContainer data-testid="avatar-group">
      <AvatarGroupWrapper
        width={width}
        height={calculateSize(Number(sizeNumber), 1)}
        >
        {selectedMap}

        {readOnlyNoData}

        {!readOnly && <AddButtonContainer
          data-testid='addbutton'
          className={dropDownClassName}
          left={direction === 'vertical' ? 0 : addButtonDiplacement}
          top={direction === 'vertical' ? addButtonDiplacement : 0}
          >
          <DropdownWithSearch
            options={options || []}
            setSelectedOption={setSelectedOption || (() => {})}
            itemsSelected={itemsSelected}
            {...dropdownProps}
            >
            <Avatar
              name='+'
              tooltip={false}
              size={avatarProps?.size}
              shape={avatarProps?.shape}
              />
          </DropdownWithSearch>
          </AddButtonContainer>}

      </AvatarGroupWrapper>

      <RemainStyle
        left={ direction === 'vertical' ? `${(Number(sizeNumber) - 20) / 2}px` : remainDiplacement}
        top= {direction === 'vertical' ? remainDiplacement : `${(Number(sizeNumber) - 20) / 2}px`}
        >
        {remainingUsersCount > 0 && (
          <div className="avatar-count">
            +{remainingUsersCount}
          </div>
        )}
      </RemainStyle>

    </AvatarGroupContainer>
  );
};
