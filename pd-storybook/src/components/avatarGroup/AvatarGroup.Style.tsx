import styled from '@emotion/styled';

export const AvatarGroupContainer = styled.div`
  position: relative;
  display: inline-flex;
  align-items: center;
`;

export const AvatarGroupWrapper = styled.div<{width?: number, height?: number}>`
  display: flex;

  height: ${({ height }) => `${height}px`};
  align-items: center;
`;

export const AvatarGroupItem = styled.div<{index?: number, length?: number, width?: number, left?: number, top?: number}>`
  position: absolute;
  z-index: ${({ index }) => 10 - (index || 0)};
  left: ${({ left }) => `${left}px`};
  top: ${({ top }) => `${top}px`};
`;

export const AddButtonContainer = styled.div<{left?: string | number, top?: string | number}>`
  position: absolute;
  left: ${({ left }) => `${left}px`};
  top: ${({ top }) => `${top}px`};
`;

export const RemainStyle = styled.div<{left?: string | number, top?: string | number}>`
  position: absolute;
  left: ${({ left }) => `${left}px`};
  top: ${({ top }) => `${top}px`};
`;
