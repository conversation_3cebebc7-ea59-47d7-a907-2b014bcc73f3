import OutsideClick from '../../utils/OutsideClick';
import { Avatar } from '../avatar/Avatar.Component';
import { providersFormConstants } from '../providersForm/ProvidersForm.constant';
import { StyledOptionName } from '../providersForm/ProvidersForm.Style';

import {
  DropdownSimpeOptionsWrapper, DropdownWrapper, NoOptionsFound,
  OptionListWrapper,
  SimpleOptionItem,
} from './Dropdown.Style';
import { OptionsDropdownProps } from './DropdownWithSearch.Component';

export interface DropdownSuggestionsProps {
  options: OptionsDropdownProps[];
  children?: React.ReactNode;
  showAvatar?: boolean;
  isOpen?: boolean;
  onSelect?: (option: OptionsDropdownProps) => void;
  handleScroll?: (e: React.UIEvent<HTMLDivElement>) => void;
  onToggle?: (isOpen: boolean) => void;
  loading?: boolean;
  hasMore?: boolean;
}

export const DropdownSuggestions = ({
  options, children, onSelect, showAvatar = false, handleScroll, onToggle, isOpen,
}: DropdownSuggestionsProps) => {
  const optionList = () => (options.length !== undefined && options.length > 0 ? (
    options.map((option, index) => {
      if (option?.renderer) {
        return (
          <div key={option.id} onMouseDown={() => onSelect?.(option)}>
            {option?.renderer(option)}
          </div>
        );
      }

      return (
        <SimpleOptionItem
          data-testid='dropdown-item'
          key={index}
          height='40px'
          onMouseDown={() => onSelect?.(option)}
        >
          {showAvatar && <Avatar
            name={option.name}
            src={(option as OptionsDropdownProps & { media?: { url: string }[] }).media?.[0]?.url || ''}
            size={36}
          />}

          <StyledOptionName>{option.name}</StyledOptionName>
        </SimpleOptionItem>

      );
    })
  ) : (
    <NoOptionsFound>{providersFormConstants.NO_OPTION_FOUND}</NoOptionsFound>
  ));

  return (
    <OutsideClick onOutsideClick={() => onToggle?.(false)}>
      <DropdownWrapper >
        <div data-testid="card-list-trigger">
          {children}
        </div>

        {isOpen && (
          <DropdownSimpeOptionsWrapper data-testid='dropdown' onScroll={handleScroll}>
            <OptionListWrapper>
              {optionList()}
            </OptionListWrapper>
          </DropdownSimpeOptionsWrapper>
        )}
      </DropdownWrapper>
    </OutsideClick>
  );
};
