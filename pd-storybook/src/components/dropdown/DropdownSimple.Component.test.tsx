import '@testing-library/jest-dom';
import { fireEvent, render, screen } from '@testing-library/react';
import React from 'react';

import { DropdownSimple } from './DropdownSimple.Component';
import { OptionsDropdownProps } from './DropdownWithSearch.Component';

jest.mock('../../utils/OutsideClick', () => ({
  __esModule: true,
  default: ({ children, onOutsideClick }: { children: React.ReactNode, onOutsideClick: () => void }) => (
    <div data-testid="outside-click" onClick={onOutsideClick}>
      {children}
    </div>
  ),
}));

describe('DropdownSimple', () => {
  const mockOptions: OptionsDropdownProps[] = [
    { id: '1', name: 'Option 1' },
    { id: '2', name: 'Option 2' },
    { id: '3', name: 'Option 3' },
  ];

  const mockSetSelectedOption = jest.fn();
  const mockOnToggle = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders TooltipContainer correctly', () => {
    const { asFragment } = render(<DropdownSimple
      options={mockOptions}
      setSelectedOption={mockSetSelectedOption}
    >
      <button>Open Dropdown</button>
    </DropdownSimple>);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders the dropdown with children', () => {
    render(
      <DropdownSimple
        options={mockOptions}
        setSelectedOption={mockSetSelectedOption}
      >
        <button>Open Dropdown</button>
      </DropdownSimple>,
    );

    expect(screen.getByText('Open Dropdown')).toBeInTheDocument();
    expect(screen.queryByTestId('dropdown')).not.toBeInTheDocument();
  });

  it('does not open the dropdown when disabled', () => {
    render(
      <DropdownSimple
        options={mockOptions}
        setSelectedOption={mockSetSelectedOption}
        disabled={true}
      >
        <button>Open Dropdown</button>
      </DropdownSimple>,
    );

    fireEvent.click(screen.getByText('Open Dropdown'));
    expect(screen.queryByTestId('dropdown')).not.toBeInTheDocument();
  });

  it('calls onToggle when dropdown is opened', () => {
    jest.spyOn(React, 'useState').mockImplementationOnce(() => [true, jest.fn()]);

    render(
      <DropdownSimple
        options={mockOptions}
        setSelectedOption={mockSetSelectedOption}
        onToggle={mockOnToggle}
      >
        <button>Open Dropdown</button>
      </DropdownSimple>,
    );

    fireEvent.click(screen.getByText('Open Dropdown'));
    expect(mockOnToggle).toHaveBeenCalledWith(true);

    const dropdown = screen.getByTestId('dropdown');

    expect(dropdown).toBeVisible();
  });

  test('should call setSelectedOption when an option is selected', () => {
    jest.spyOn(React, 'useState').mockImplementationOnce(() => [true, jest.fn()]);
    jest.spyOn(React, 'useState').mockImplementationOnce(() => [{ id: '2', name: 'Option 2' }, jest.fn()]);

    render(
      <DropdownSimple
        options={mockOptions}
        setSelectedOption={mockSetSelectedOption}
        onToggle={mockOnToggle}
      >
        <button>Open Dropdown</button>
      </DropdownSimple>,
    );

    fireEvent.click(screen.getByTestId('dropdown-trigger'));

    expect(screen.getByTestId('dropdown')).toBeInTheDocument();
    expect(mockOnToggle).toHaveBeenCalledWith(true);

    fireEvent.click(screen.getAllByTestId('dropdown-item')[1]); // Selecting Option 2

    expect(mockSetSelectedOption).toHaveBeenCalledWith(mockOptions[1]);
  });

  test('should handle option selection with custom renderer', () => {
    jest.spyOn(React, 'useState').mockImplementationOnce(() => [true, jest.fn()]);
    const optionsWithRenderer = [
      {
        id: '1',
        name: 'Custom Option',
        renderer: (option: unknown) => <div data-testid="custom-rendered-option">Custom {(option as OptionsDropdownProps).name}</div>,
      },
    ];

    render(
      <DropdownSimple
        options={optionsWithRenderer}
        setSelectedOption={mockSetSelectedOption}
      >
        <button>Open Dropdown</button>
      </DropdownSimple>,
    );

    fireEvent.click(screen.getByTestId('dropdown-trigger'));

    expect(screen.getByTestId('custom-rendered-option')).toBeInTheDocument();

    fireEvent.click(screen.getByTestId('custom-rendered-option'));

    expect(mockSetSelectedOption).toHaveBeenCalledWith(optionsWithRenderer[0]);
  });

  it('should not call loadMoreOptions when not scrolled to the bottom', () => {
    const mockLoadMoreOptions = jest.fn();
    jest.spyOn(React, 'useState').mockImplementationOnce(() => [true, jest.fn()]);

    render(
      <DropdownSimple
        options={mockOptions}
        setSelectedOption={mockSetSelectedOption}
        loadMoreOptions={mockLoadMoreOptions}
      >
        <span>Dropdown Trigger</span>
      </DropdownSimple>,
    );

    fireEvent.click(screen.getByText('Dropdown Trigger'));

    const dropdown = screen.getByTestId('dropdown');

    Object.defineProperty(dropdown, 'scrollTop', { value: 10 });
    Object.defineProperty(dropdown, 'scrollHeight', { value: 150 });
    Object.defineProperty(dropdown, 'clientHeight', { value: 50 });

    fireEvent.scroll(dropdown);

    expect(mockLoadMoreOptions).not.toHaveBeenCalled();
  });

  it('should not throw error when loadMoreOptions is not provided', () => {
    jest.spyOn(React, 'useState').mockImplementationOnce(() => [true, jest.fn()]);

    render(
      <DropdownSimple
        options={mockOptions}
        setSelectedOption={mockSetSelectedOption}
      >
        <span>Dropdown Trigger</span>
      </DropdownSimple>,
    );

    fireEvent.click(screen.getByText('Dropdown Trigger'));

    const dropdown = screen.getByTestId('dropdown');

    Object.defineProperty(dropdown, 'scrollTop', { value: 100 });
    Object.defineProperty(dropdown, 'scrollHeight', { value: 150 });
    Object.defineProperty(dropdown, 'clientHeight', { value: 50 });

    expect(() => {
      fireEvent.scroll(dropdown);
    }).not.toThrow();
  });

  it('should call loadMoreOptions when scrolled to the bottom', () => {
    const mockLoadMoreOptions = jest.fn();
    jest.spyOn(React, 'useState').mockImplementationOnce(() => [true, jest.fn()]);

    render(
      <DropdownSimple
        options={mockOptions}
        setSelectedOption={mockSetSelectedOption}
        loadMoreOptions={mockLoadMoreOptions}
      >
        <span>Dropdown Trigger</span>
      </DropdownSimple>,
    );

    fireEvent.click(screen.getByText('Dropdown Trigger'));

    const dropdown = screen.getByTestId('dropdown');

    Object.defineProperty(dropdown, 'scrollTop', { value: 100 });
    Object.defineProperty(dropdown, 'scrollHeight', { value: 150 });
    Object.defineProperty(dropdown, 'clientHeight', { value: 50 });

    fireEvent.scroll(dropdown);

    expect(mockLoadMoreOptions).toHaveBeenCalled();
  });
});
