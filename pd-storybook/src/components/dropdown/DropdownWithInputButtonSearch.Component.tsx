import { SetStateAction, useState } from 'react';

import OutsideClick from '../../utils/OutsideClick';
import { IconImporter } from '../iconImporter/IconImporter.Component';
import { StyledOptionName } from '../providersForm/ProvidersForm.Style';
import { providersFormConstants } from '../providersForm/ProvidersForm.constant';

import {
  DropdownContainer, DropdownHeader, DropdownLabel, DropdownOptionsContainer, NoOptionsFound,
  Option,
  OptionListWrapper,
  SearchInput,
} from './Dropdown.Style';

export type Provider = {
  id: string;
  name: string;
  tributaryId: string | null;
  providerCompanyId: string | null;
  companyId: string;
  totalItemsProvided?: number;
};

export interface AddProviderInventory {
  id: string | undefined;
  name: string | undefined;
  providerInventories: {
      currentPurchasePrice: number | undefined;
      currentDiscount: number | undefined;
  }[]
}

interface DropdownProps {
  options: Provider[] | undefined;
  label: string;
  selectedOption: Provider | null;
  setSelectedOption: (provider: Provider) => void
  provider?:AddProviderInventory[]
}

export const DropdowWithSearch = ({
  options, label, selectedOption, setSelectedOption, provider,
}: DropdownProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  const filteredOptions = options?.filter((option) => {
    if (!provider || provider.length === 0) {
      return true;
    }
    return provider.every((selected) => selected.id !== option.id);
  });

  const handleOptionSelect = (option: Provider) => {
    setSelectedOption(option);
    setSearchQuery('');
    setIsOpen(false);
  };

  const onHandleInputChange = (e: { target: { value: SetStateAction<string>; }; }) => setSearchQuery(e.target.value);

  const optionList = () => (filteredOptions?.length !== undefined && filteredOptions?.length > 0 ? (
    filteredOptions?.map((option, index) => (

      <Option
        key={index}
        onClick={() => handleOptionSelect(option)}
      >
        <IconImporter
          size={36}
          name={'factory'}
          className="pd-text-dark-400 hover:pd-text-dark-700 pd-transition-all pd-ease-in-out pd-mr-1"
        />
        <StyledOptionName>{option?.name}</StyledOptionName>
      </Option>

    ))
  ) : (
    <NoOptionsFound>{providersFormConstants.NO_OPTION_FOUND}</NoOptionsFound>
  ));

  return (
    <OutsideClick onOutsideClick={() => setIsOpen(false)}>
      <DropdownContainer>
        <DropdownHeader
          onClick={() => setIsOpen(true)}
        >
          <DropdownLabel> {selectedOption?.name || label}</DropdownLabel>
          <IconImporter
            size={18}
            name={'caretDown'}
            className="pd-text-dark-400 hover:pd-text-dark-700 pd-transition-all pd-ease-in-out pd-mr-1"
          />
        </DropdownHeader>

        {isOpen && (
          <DropdownOptionsContainer>
            <SearchInput
              type="text"
              value={searchQuery}
              onChange={onHandleInputChange}
              placeholder="Search..."
            />
            <OptionListWrapper>
              {optionList()}
            </OptionListWrapper>
          </DropdownOptionsContainer>
        )}
      </DropdownContainer>
    </OutsideClick>
  );
};
