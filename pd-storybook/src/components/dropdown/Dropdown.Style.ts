import styled from '@emotion/styled';

import Theme from '../../configurations/Theme.Configuration';

export type DropdownPosition = 'top' | 'bottom' | 'left' | 'right' | 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';

export const DropdownWrapper = styled.div`
  cursor: pointer;
  position: relative;
`;

export const DropdownOptionsWrapper = styled.div<{
  minWidth?: string;
  maxWidth?: string;
  position?: DropdownPosition;
}>`
  max-height: 220px;
  overflow-y: auto;
  border: 1px solid ${Theme.colors.dark[200]};
  border-radius: 6px;
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.05);
  position: absolute;
  padding: 8px;
  background: white;
  z-index: 1050;
  box-sizing: border-box;

  ${({ position = 'bottom', minWidth: propMinWidth, maxWidth: propMaxWidth }) => {
    let styles = '';
    const defaultSideWidth = propMinWidth || '250px';

    switch (position) {
      case 'top':
        styles = `
          bottom: 100%;
          left: 0;
          right: 0;
          width: 100%;
          min-width: ${propMinWidth || '100%'};
          max-width: ${propMaxWidth || '100%'};
          margin-bottom: 6px;
          margin-top: 0;
        `;
        break;
      case 'top-left':
        styles = `
          bottom: 100%;
          left: 0;
          right: auto;
          width: 100%;
          min-width: ${propMinWidth || '100%'};
          max-width: ${propMaxWidth || '100%'};
          margin-bottom: 6px;
          margin-top: 0;
        `;
        break;
      case 'top-right':
        styles = `
          bottom: 100%;
          right: 0;
          left: auto;
          width: 100%;
          min-width: ${propMinWidth || '100%'};
          max-width: ${propMaxWidth || '100%'};
          margin-bottom: 6px;
          margin-top: 0;
        `;
        break;
      case 'left':
        styles = `
          top: 0;
          right: 100%;
          width: ${defaultSideWidth};
          min-width: ${propMinWidth || 'auto'};
          max-width: ${propMaxWidth || 'auto'};
          margin-right: 6px;
          margin-top: 0;
          left: auto;
        `;
        break;
      case 'right':
        styles = `
          top: 0;
          left: 100%;
          width: ${defaultSideWidth};
          min-width: ${propMinWidth || 'auto'};
          max-width: ${propMaxWidth || 'auto'};
          margin-left: 6px;
          margin-top: 0;
          right: auto;
        `;
        break;
      case 'bottom':
        styles = `
          top: 100%;
          left: 0;
          right: 0;
          width: 100%;
          min-width: ${propMinWidth || '100%'};
          max-width: ${propMaxWidth || '100%'};
          margin-top: 6px;
        `;
        break;
      case 'bottom-left':
        styles = `
          top: 100%;
          left: 0;
          right: auto;
          width: 100%;
          min-width: ${propMinWidth || '100%'};
          max-width: ${propMaxWidth || '100%'};
          margin-top: 6px;
        `;
        break;
      case 'bottom-right':
        styles = `
          top: 100%;
          right: 0;
          left: auto;
          width: 100%;
          min-width: ${propMinWidth || '100%'};
          max-width: ${propMaxWidth || '100%'};
          margin-top: 6px;
        `;
        break;
      default:
        styles = `
          top: 100%;
          left: 0;
          right: 0;
          width: 100%;
          min-width: ${propMinWidth || '100%'};
          max-width: ${propMaxWidth || '100%'};
          margin-top: 6px;
        `;
        break;
    }
    return styles;
  }}
`;

export const OptionItem = styled.div<{ height?: string}>`
  cursor: pointer;
  height: ${({ height }) => height || 'auto'};
  display: flex;
  gap: 12px;
  align-items: center;
  padding: 10px 12px;

  &:hover {
    background: ${Theme.colors.fadedGray || '#f5f5f5'};
  }
`;

export const DropdownContainer = styled.div`
  border: 1px solid ${Theme.colors.dark[300]};
  padding: 24px 16px;
  border-radius: 12px;
  min-width: 323px;
  cursor: pointer;
  position: relative;
  background: white;
  z-index: 50;
`;

export const DropdownHeader = styled.div`
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid ${Theme.colors.dark[300]};
  padding: 12px 16px;
`;

export const DropdownLabel = styled.span`
  color: ${Theme.colors.dark[500]};
`;

export const DropdownOptionsContainer = styled.div`
  margin-top: 8px;
  max-height: 200px;
  overflow-y: auto;
  border-bottom: 1px solid ${Theme.colors.dark[300]};
  border-radius: 8px;
`;

export const SearchInput = styled.input`
  width: 100%;
  padding: 10px 12px;
  border: 1px solid ${Theme.colors.dark[300]};
  border-radius: 4px;
  outline: none;
  margin-bottom: 8px;
  box-sizing: border-box;
  font-size: 14px;
`;

export const Option = styled.div`
  padding: 8px 16px;
  cursor: pointer;
  border-bottom: 1px solid ${Theme.colors.dark[300]};
  height: 48px;
  display: flex;
  gap: 14px;
`;

export const OptionImage = styled.img`
  width: 36px;
  height: 36px;
`;

export const NoOptionsFound = styled.div`
  padding: 12px 16px;
  color: ${Theme.colors.dark[400]};
  text-align: center;
  font-size: 14px;
`;

export const OptionListWrapper = styled.div`
  padding: 0;
`;

export const SimpleOptionItem = styled.div<{ height?: string}>`
  cursor: pointer;
  display: flex;
  gap: 14px;
  align-items: center;
  padding: 6px;
  fons-size: 12px;
  

  &:hover {
    background: ${Theme.colors.fadedGray};
  }
`;

export const DropdownSimpeOptionsWrapper = styled.div<{ minWidth?: string, maxWidth?: string }>`
  margin-top: 8px;
  max-height: 200px;
  overflow-y: auto;
  border-bottom: 1px solid ${Theme.colors.dark[300]};
  border-radius: 8px;
  box-shadow: 0px 6px 8px ${Theme.colors.dark[300]};
  position: absolute;
  padding: 8px;
  background: white;
  width: 100%;

  max-width: ${({ maxWidth }) => maxWidth || '100%'};
  position: absolute;
  z-index: 50;
  left: 0; 
  right: 0; 
  box-sizing: border-box; 
`;
