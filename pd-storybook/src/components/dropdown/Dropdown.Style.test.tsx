import { render } from '@testing-library/react';

import {
  Dropdown<PERSON>ontainer,
  DropdownHeader,
  DropdownLabel,
  DropdownOptionsContainer,
  DropdownOptionsWrapper,
  DropdownPosition,
  DropdownSimpeOptionsWrapper,
  DropdownWrapper,
  NoOptionsFound,
  Option,
  OptionImage,
  OptionItem,
  OptionListWrapper,
  SearchInput,
  SimpleOptionItem,
} from './Dropdown.Style';

describe('Dropdown Style Snapshots', () => {
  it('renders DropdownContainer correctly', () => {
    const { asFragment } = render(<DropdownContainer />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders DropdownHeader correctly', () => {
    const { asFragment } = render(<DropdownHeader />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders DropdownLabel correctly', () => {
    const { asFragment } = render(<DropdownLabel />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders DropdownOptionsContainer correctly', () => {
    const { asFragment } = render(<DropdownOptionsContainer />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders SearchInput correctly', () => {
    const { asFragment } = render(<SearchInput />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders Option correctly', () => {
    const { asFragment } = render(<Option />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders OptionImage correctly', () => {
    const { asFragment } = render(<OptionImage />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders NoOptionsFound correctly', () => {
    const { asFragment } = render(<NoOptionsFound />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders OptionListWrapper correctly', () => {
    const { asFragment } = render(<OptionListWrapper />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders DropdownWrapper correctly by default', () => {
    const { asFragment } = render(<DropdownWrapper />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders DropdownOptionsWrapper correctly by default (position bottom)', () => {
    const { asFragment } = render(<DropdownOptionsWrapper />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders OptionItem correctly by default', () => {
    const { asFragment } = render(<OptionItem />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders SimpleOptionItem correctly by default', () => {
    const { asFragment } = render(<SimpleOptionItem />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders DropdownSimpeOptionsWrapper correctly by default', () => {
    const { asFragment } = render(<DropdownSimpeOptionsWrapper />);
    expect(asFragment()).toMatchSnapshot();
  });

  describe('DropdownOptionsWrapper prop variations', () => {
    const positions: DropdownPosition[] = ['top', 'left', 'right', 'bottom'];
    positions.forEach((position) => {
      it(`renders DropdownOptionsWrapper correctly with position ${position}`, () => {
        const { asFragment } = render(<DropdownOptionsWrapper position={position} />);
        expect(asFragment()).toMatchSnapshot();
      });
    });

    it('renders DropdownOptionsWrapper correctly with minWidth prop', () => {
      const { asFragment } = render(<DropdownOptionsWrapper minWidth="200px" />);
      expect(asFragment()).toMatchSnapshot();
    });

    it('renders DropdownOptionsWrapper correctly with maxWidth prop', () => {
      const { asFragment } = render(<DropdownOptionsWrapper maxWidth="400px" />);
      expect(asFragment()).toMatchSnapshot();
    });

    it('renders DropdownOptionsWrapper correctly with minWidth and maxWidth props', () => {
      const { asFragment } = render(<DropdownOptionsWrapper minWidth="150px" maxWidth="350px" />);
      expect(asFragment()).toMatchSnapshot();
    });
  });

  describe('OptionItem prop variations', () => {
    it('renders OptionItem correctly with height prop', () => {
      const { asFragment } = render(<OptionItem height="60px" />);
      expect(asFragment()).toMatchSnapshot();
    });
  });

  describe('SimpleOptionItem prop variations', () => {
    it('renders SimpleOptionItem correctly with height prop', () => {
      const { asFragment } = render(<SimpleOptionItem height="30px" />);
      expect(asFragment()).toMatchSnapshot();
    });
  });

  describe('DropdownSimpeOptionsWrapper prop variations', () => {
    it('renders DropdownSimpeOptionsWrapper correctly with minWidth prop', () => {
      const { asFragment } = render(<DropdownSimpeOptionsWrapper minWidth="200px" />);
      expect(asFragment()).toMatchSnapshot();
    });

    it('renders DropdownSimpeOptionsWrapper correctly with maxWidth prop', () => {
      const { asFragment } = render(<DropdownSimpeOptionsWrapper maxWidth="500px" />);
      expect(asFragment()).toMatchSnapshot();
    });

    it('renders DropdownSimpeOptionsWrapper correctly with minWidth and maxWidth props', () => {
      const { asFragment } = render(<DropdownSimpeOptionsWrapper minWidth="180px" maxWidth="450px" />);
      expect(asFragment()).toMatchSnapshot();
    });
  });
});
