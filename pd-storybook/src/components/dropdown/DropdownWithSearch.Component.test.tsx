import { fireEvent, render, screen } from '@testing-library/react';

import { AvatarProps } from '../avatar/Avatar.Component';

import { DropdownWithSearch, OptionsDropdownProps } from './DropdownWithSearch.Component';

jest.mock('../avatar/Avatar.Component', () => ({
  __esModule: true,
  Avatar: ({ name, size, ...props }: AvatarProps) => <div data-testid="avatar" {...props}></div>,
}));

jest.mock('../providersForm/ProvidersForm.constant', () => ({
  providersFormConstants: {
    NO_OPTION_FOUND: 'No se encontraron opciones',
  },
}));

describe('DropdownWithSearch Component', () => {
  const mockOptions: OptionsDropdownProps[] = [
    {
      id: '1', name: 'Option 1', src: 'src1.png', shape: 'circle',
    },
    {
      id: '2', name: 'Option 2', src: 'src2.png', shape: 'square',
    },
    { id: '3', name: 'Option 3' },
  ];

  const mockSetSelectedOption = jest.fn();
  const mockOnToggle = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render children', () => {
    render(
      <DropdownWithSearch options={mockOptions} setSelectedOption={mockSetSelectedOption}>
        <span>Dropdown Trigger</span>
      </DropdownWithSearch>,
    );
    expect(screen.getByText('Dropdown Trigger')).toBeInTheDocument();
  });

  it('should open dropdown on click', () => {
    render(
      <DropdownWithSearch options={mockOptions} setSelectedOption={mockSetSelectedOption}>
        <span>Dropdown Trigger</span>
      </DropdownWithSearch>,
    );
    fireEvent.click(screen.getByText('Dropdown Trigger'));
    expect(screen.getByTestId('dropdown')).toBeVisible();
  });

  it('should close dropdown on outside click', () => {
    render(
      <DropdownWithSearch options={mockOptions} setSelectedOption={mockSetSelectedOption}>
        <span>Dropdown Trigger</span>
      </DropdownWithSearch>,
    );
    fireEvent.click(screen.getByText('Dropdown Trigger'));
    expect(screen.getByTestId('dropdown')).toBeVisible();
    fireEvent.mouseDown(document);
    expect(screen.queryByTestId('dropdown')).not.toBeInTheDocument();
  });

  it('should show all options when itemsSelected is an empty array', () => {
    render(
      <DropdownWithSearch options={mockOptions} setSelectedOption={mockSetSelectedOption} itemsSelected={[]}>
        <span>Dropdown Trigger</span>
      </DropdownWithSearch>,
    );
    fireEvent.click(screen.getByText('Dropdown Trigger'));
    expect(screen.getAllByTestId('dropdown-item').length).toBe(3);
  });

  it('should show all options when itemsSelected is undefined', () => {
    render(
      <DropdownWithSearch options={mockOptions} setSelectedOption={mockSetSelectedOption} itemsSelected={undefined}>
        <span>Dropdown Trigger</span>
      </DropdownWithSearch>,
    );
    fireEvent.click(screen.getByText('Dropdown Trigger'));
    expect(screen.getAllByTestId('dropdown-item').length).toBe(3);
    // Verificamos que se rendericen los nombres de las opciones
    expect(screen.getByText('Option 1')).toBeInTheDocument();
    expect(screen.getByText('Option 2')).toBeInTheDocument();
    expect(screen.getByText('Option 3')).toBeInTheDocument();
  });

  it('should filter options when itemsSelected is empty', () => {
    render(
      <DropdownWithSearch options={mockOptions} setSelectedOption={mockSetSelectedOption}>
        <span>Dropdown Trigger</span>
      </DropdownWithSearch>,
    );
    fireEvent.click(screen.getByText('Dropdown Trigger'));
    expect(screen.getAllByTestId('dropdown-item').length).toBe(3);
  });

  it('should filter options when itemsSelected has items', () => {
    const itemsSelected = [{ id: '1', name: 'Option 1' }];
    render(
      <DropdownWithSearch options={mockOptions} setSelectedOption={mockSetSelectedOption} itemsSelected={itemsSelected}>
        <span>Dropdown Trigger</span>
      </DropdownWithSearch>,
    );
    fireEvent.click(screen.getByText('Dropdown Trigger'));
    expect(screen.getAllByTestId('dropdown-item').length).toBe(2);
  });

  it('should display "No se encontraron opciones" when no options match', () => {
    const itemsSelected = [{ id: '1', name: 'Option 1' }, { id: '2', name: 'Option 2' }, { id: '3', name: 'Option 3' }];

    render(
      <DropdownWithSearch options={mockOptions} setSelectedOption={mockSetSelectedOption} itemsSelected={itemsSelected}>
        <span>Dropdown Trigger</span>
      </DropdownWithSearch>,
    );
    fireEvent.click(screen.getByText('Dropdown Trigger'));
    expect(screen.getByText('No se encontraron opciones')).toBeInTheDocument();
  });

  it('should not show avatar when showAvatar is false', () => {
    render(
      <DropdownWithSearch options={mockOptions} setSelectedOption={mockSetSelectedOption} showAvatar={false}>
        <span>Dropdown Trigger</span>
      </DropdownWithSearch>,
    );
    fireEvent.click(screen.getByText('Dropdown Trigger'));
    expect(screen.queryByTestId('avatar')).not.toBeInTheDocument();
  });

  it('should not open the dropdown when disabled is true', () => {
    render(
      <DropdownWithSearch options={mockOptions} setSelectedOption={mockSetSelectedOption} disabled>
        <span>Dropdown Trigger</span>
      </DropdownWithSearch>,
    );
    fireEvent.click(screen.getByText('Dropdown Trigger'));
    expect(screen.queryByTestId('dropdown')).not.toBeInTheDocument();
  });

  it('should handle option selection correctly', () => {
    const mockSetSelectedOptionT2 = jest.fn();
    render(
      <DropdownWithSearch options={mockOptions} setSelectedOption={mockSetSelectedOptionT2}>
        <span>Dropdown Trigger</span>
      </DropdownWithSearch>,
    );

    fireEvent.click(screen.getByText('Dropdown Trigger'));
    expect(screen.getByTestId('dropdown')).toBeVisible();

    fireEvent.click(screen.getAllByTestId('dropdown-item')[0]);

    expect(mockSetSelectedOptionT2).toHaveBeenCalledWith(mockOptions[0]);

    expect(screen.queryByTestId('dropdown')).not.toBeInTheDocument();
  });

  it('should update search query when typing in search input', () => {
    render(
      <DropdownWithSearch options={mockOptions} setSelectedOption={mockSetSelectedOption}>
        <span>Dropdown Trigger</span>
      </DropdownWithSearch>,
    );

    fireEvent.click(screen.getByText('Dropdown Trigger'));

    const searchInput = screen.getByPlaceholderText('Buscar...');
    fireEvent.change(searchInput, { target: { value: 'test search' } });

    expect(searchInput).toHaveValue('test search');
  });

  it('should call onToggle with the correct state when dropdown is toggled', () => {
    render(
      <DropdownWithSearch options={mockOptions} setSelectedOption={mockSetSelectedOption} onToggle={mockOnToggle}>
        <span>Dropdown Trigger</span>
      </DropdownWithSearch>,
    );

    fireEvent.click(screen.getByText('Dropdown Trigger'));
    expect(mockOnToggle).toHaveBeenCalledWith(true);

    fireEvent.mouseDown(document);
    expect(mockOnToggle).toHaveBeenCalledWith(false);
  });

  it('should call onSearchChange with the correct value when search input changes', () => {
    const mockOnSearchChange = jest.fn();

    render(
      <DropdownWithSearch options={mockOptions} setSelectedOption={mockSetSelectedOption} onSearchChange={mockOnSearchChange}>
        <span>Dropdown Trigger</span>
      </DropdownWithSearch>,
    );

    fireEvent.click(screen.getByText('Dropdown Trigger'));

    const searchInput = screen.getByPlaceholderText('Buscar...');
    fireEvent.change(searchInput, { target: { value: 'test search' } });

    expect(mockOnSearchChange).toHaveBeenCalledWith('test search');
  });

  it('should not open dropdown when disabled is true', () => {
    render(
      <DropdownWithSearch
        options={mockOptions}
        setSelectedOption={mockSetSelectedOption}
        disabled={true}
        onToggle={mockOnToggle}
      >
        <button>Toggle Dropdown</button>
      </DropdownWithSearch>,
    );

    const toggleButton = screen.getByText('Toggle Dropdown');
    fireEvent.click(toggleButton);

    expect(screen.queryByTestId('dropdown')).not.toBeInTheDocument();

    expect(mockOnToggle).not.toHaveBeenCalled();
  });

  it('should open dropdown when disabled is false', () => {
    render(
      <DropdownWithSearch
        options={mockOptions}
        setSelectedOption={mockSetSelectedOption}
        disabled={false}
        onToggle={mockOnToggle}
      >
        <button>Toggle Dropdown</button>
      </DropdownWithSearch>,
    );

    const toggleButton = screen.getByText('Toggle Dropdown');
    fireEvent.click(toggleButton);

    expect(screen.getByTestId('dropdown')).toBeInTheDocument();

    expect(mockOnToggle).toHaveBeenCalledWith(true);
  });

  it('should call loadMoreOptions when scrolled to the bottom', () => {
    const mockLoadMoreOptions = jest.fn();

    render(
      <DropdownWithSearch
        options={mockOptions}
        setSelectedOption={mockSetSelectedOption}
        loadMoreOptions={mockLoadMoreOptions}
      >
        <span>Dropdown Trigger</span>
      </DropdownWithSearch>,
    );

    fireEvent.click(screen.getByText('Dropdown Trigger'));

    const dropdown = screen.getByTestId('dropdown');

    Object.defineProperty(dropdown, 'scrollTop', { value: 100 });
    Object.defineProperty(dropdown, 'scrollHeight', { value: 150 });
    Object.defineProperty(dropdown, 'clientHeight', { value: 50 });

    fireEvent.scroll(dropdown);

    expect(mockLoadMoreOptions).toHaveBeenCalled();
  });

  it('should not call loadMoreOptions when not scrolled to the bottom', () => {
    const mockLoadMoreOptions = jest.fn();

    render(
      <DropdownWithSearch
        options={mockOptions}
        setSelectedOption={mockSetSelectedOption}
        loadMoreOptions={mockLoadMoreOptions}
      >
        <span>Dropdown Trigger</span>
      </DropdownWithSearch>,
    );

    fireEvent.click(screen.getByText('Dropdown Trigger'));

    const dropdown = screen.getByTestId('dropdown');

    Object.defineProperty(dropdown, 'scrollTop', { value: 10 });
    Object.defineProperty(dropdown, 'scrollHeight', { value: 150 });
    Object.defineProperty(dropdown, 'clientHeight', { value: 50 });

    fireEvent.scroll(dropdown);

    expect(mockLoadMoreOptions).not.toHaveBeenCalled();
  });

  it('should not throw error when loadMoreOptions is not provided', () => {
    render(
      <DropdownWithSearch
        options={mockOptions}
        setSelectedOption={mockSetSelectedOption}
      >
        <span>Dropdown Trigger</span>
      </DropdownWithSearch>,
    );

    fireEvent.click(screen.getByText('Dropdown Trigger'));

    const dropdown = screen.getByTestId('dropdown');

    Object.defineProperty(dropdown, 'scrollTop', { value: 100 });
    Object.defineProperty(dropdown, 'scrollHeight', { value: 150 });
    Object.defineProperty(dropdown, 'clientHeight', { value: 50 });

    expect(() => {
      fireEvent.scroll(dropdown);
    }).not.toThrow();
  });

  it('should handle undefined options correctly', () => {
    render(
      <DropdownWithSearch options={[]} setSelectedOption={mockSetSelectedOption}>
        <span>Dropdown Trigger</span>
      </DropdownWithSearch>,
    );

    fireEvent.click(screen.getByText('Dropdown Trigger'));

    expect(screen.getByText('No se encontraron opciones')).toBeInTheDocument();
  });

  it('should render and select a custom rendered option', () => {
    const customRenderer = jest.fn((option) => <div data-testid="custom-option">Custom {option.name}</div>);
    const customOptions = [
      { id: '1', name: 'Option 1', renderer: customRenderer },
      { id: '2', name: 'Option 2' },
    ];
    const mockSetSelectedOptionCustom = jest.fn();

    render(
      <DropdownWithSearch options={customOptions} setSelectedOption={mockSetSelectedOptionCustom}>
        <span>Dropdown Trigger</span>
      </DropdownWithSearch>,
    );
    fireEvent.click(screen.getByText('Dropdown Trigger'));
    expect(screen.getByTestId('custom-option')).toHaveTextContent('Custom Option 1');
    fireEvent.click(screen.getByTestId('custom-option'));
    expect(mockSetSelectedOptionCustom).toHaveBeenCalledWith(customOptions[0]);
    expect(screen.queryByTestId('dropdown')).not.toBeInTheDocument();
  });

  it('should show customSearchbox and hide default search input', () => {
    const customSearchbox = <div data-testid="custom-searchbox">Soy custom</div>;
    render(
      <DropdownWithSearch
        options={[{ id: '1', name: 'Option 1' }]}
        setSelectedOption={jest.fn()}
        customSearchbox={customSearchbox}
      >
        <span>Dropdown Trigger</span>
      </DropdownWithSearch>,
    );
    fireEvent.click(screen.getByText('Dropdown Trigger'));
    expect(screen.getByTestId('custom-searchbox')).toBeInTheDocument();
    expect(screen.queryByPlaceholderText('Buscar...')).not.toBeInTheDocument();
  });

  it('should select custom rendered option and call setSelectedOption', () => {
    const customRenderer = (option: OptionsDropdownProps) => <div data-testid={`custom-render-${option.id}`}>{option.name}</div>;
    const options = [
      { id: 'a', name: 'A', renderer: customRenderer },
      { id: 'b', name: 'B' },
    ];
    const mockSetSelectedOptionCustomRender = jest.fn();
    render(
      <DropdownWithSearch options={options as OptionsDropdownProps[]} setSelectedOption={mockSetSelectedOptionCustomRender}>
        <span>Dropdown Trigger</span>
      </DropdownWithSearch>,
    );
    fireEvent.click(screen.getByText('Dropdown Trigger'));
    fireEvent.click(screen.getByTestId('custom-render-a'));
    expect(mockSetSelectedOptionCustomRender).toHaveBeenCalledWith(options[0]);
    expect(screen.queryByTestId('dropdown')).not.toBeInTheDocument();
  });

  describe('Dropdown positions', () => {
    it('should render with bottom position by default', () => {
      render(
        <DropdownWithSearch options={mockOptions} setSelectedOption={mockSetSelectedOption}>
          <span>Dropdown Trigger</span>
        </DropdownWithSearch>,
      );

      fireEvent.click(screen.getByText('Dropdown Trigger'));
      const dropdown = screen.getByTestId('dropdown');

      expect(dropdown).toHaveStyle('top: 100%');
    });

    it('should render with position top-left', () => {
      render(
        <DropdownWithSearch
          options={mockOptions}
          setSelectedOption={mockSetSelectedOption}
          position="top-left"
        >
          <span>Dropdown Trigger</span>
        </DropdownWithSearch>,
      );

      fireEvent.click(screen.getByText('Dropdown Trigger'));
      const dropdown = screen.getByTestId('dropdown');

      expect(dropdown).toHaveStyle('bottom: 100%');
      expect(dropdown).toHaveStyle('left: 0');
      expect(dropdown).toHaveStyle('right: auto');
    });

    it('should render with position top-right', () => {
      render(
        <DropdownWithSearch
          options={mockOptions}
          setSelectedOption={mockSetSelectedOption}
          position="top-right"
        >
          <span>Dropdown Trigger</span>
        </DropdownWithSearch>,
      );

      fireEvent.click(screen.getByText('Dropdown Trigger'));
      const dropdown = screen.getByTestId('dropdown');

      expect(dropdown).toHaveStyle('bottom: 100%');
      expect(dropdown).toHaveStyle('right: 0');
      expect(dropdown).toHaveStyle('left: auto');
    });

    it('should render with position bottom-left', () => {
      render(
        <DropdownWithSearch
          options={mockOptions}
          setSelectedOption={mockSetSelectedOption}
          position="bottom-left"
        >
          <span>Dropdown Trigger</span>
        </DropdownWithSearch>,
      );

      fireEvent.click(screen.getByText('Dropdown Trigger'));
      const dropdown = screen.getByTestId('dropdown');

      expect(dropdown).toHaveStyle('top: 100%');
      expect(dropdown).toHaveStyle('left: 0');
      expect(dropdown).toHaveStyle('right: auto');
    });

    it('should render with position bottom-right', () => {
      render(
        <DropdownWithSearch
          options={mockOptions}
          setSelectedOption={mockSetSelectedOption}
          position="bottom-right"
        >
          <span>Dropdown Trigger</span>
        </DropdownWithSearch>,
      );

      fireEvent.click(screen.getByText('Dropdown Trigger'));
      const dropdown = screen.getByTestId('dropdown');

      expect(dropdown).toHaveStyle('top: 100%');
      expect(dropdown).toHaveStyle('right: 0');
      expect(dropdown).toHaveStyle('left: auto');
    });
  });
});
