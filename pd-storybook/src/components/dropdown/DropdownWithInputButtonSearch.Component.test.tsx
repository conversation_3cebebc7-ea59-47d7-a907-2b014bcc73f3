import { fireEvent, render, screen } from '@testing-library/react';

import { providersFormConstants } from '../providersForm/ProvidersForm.constant';

import { DropdowWithSearch } from './DropdownWithInputButtonSearch.Component';

const mockOptions = [
  {
    id: '1', name: 'Option 1', tributaryId: null, providerCompanyId: null, companyId: '1',
  },
  {
    id: '2', name: 'Option 2', tributaryId: null, providerCompanyId: null, companyId: '2',
  },
];

describe('DropdownWithSearch Component', () => {
  it('should render the dropdown label correctly', () => {
    render(<DropdowWithSearch options={mockOptions} label="Select an option" selectedOption={null} setSelectedOption={jest.fn()} />);
    expect(screen.getByText('Select an option')).toBeInTheDocument();
  });

  it('should open the dropdown when header is clicked', () => {
    render(<DropdowWithSearch options={mockOptions} label="Select an option" selectedOption={null} setSelectedOption={jest.fn()} />);
    fireEvent.click(screen.getByText('Select an option'));
    expect(screen.getByPlaceholderText('Search...')).toBeInTheDocument();
  });

  it('should call setSelectedOption when an option is clicked', () => {
    const setSelectedOption = jest.fn();
    render(<DropdowWithSearch options={mockOptions} label="Select an option" selectedOption={null} setSelectedOption={setSelectedOption} />);
    fireEvent.click(screen.getByText('Select an option'));
    fireEvent.click(screen.getByText('Option 1'));
    expect(setSelectedOption).toHaveBeenCalledWith(mockOptions[0]);
  });

  it('should close the dropdown when an option is selected', () => {
    render(<DropdowWithSearch options={mockOptions} label="Select an option" selectedOption={null} setSelectedOption={jest.fn()} />);
    fireEvent.click(screen.getByText('Select an option'));
    fireEvent.click(screen.getByText('Option 1'));
    expect(screen.queryByPlaceholderText('Search...')).toBeNull();
  });

  it('should filter out options that are already selected', () => {
    const selectedProviders = [{ id: '1', name: 'Option 1', providerInventories: [] }];
    render(<DropdowWithSearch options={mockOptions} label="Select an option" selectedOption={null} setSelectedOption={jest.fn()} provider={selectedProviders} />);
    fireEvent.click(screen.getByText('Select an option'));
    expect(screen.queryByText('Option 1')).toBeNull();
    expect(screen.getByText('Option 2')).toBeInTheDocument();
  });

  it('should close the dropdown when clicking outside', () => {
    render(<DropdowWithSearch options={mockOptions} label="Select an option" selectedOption={null} setSelectedOption={jest.fn()} />);
    fireEvent.click(screen.getByText('Select an option'));
    expect(screen.getByPlaceholderText('Search...')).toBeInTheDocument();
    fireEvent.mouseDown(document);
    expect(screen.queryByPlaceholderText('Search...')).toBeNull();
  });

  it('should display "No options found" when no options match the search query', () => {
    render(<DropdowWithSearch options={[]} label="Select an option" selectedOption={null} setSelectedOption={jest.fn()} />);
    fireEvent.click(screen.getByText('Select an option'));
    expect(screen.getByText(providersFormConstants.NO_OPTION_FOUND)).toBeInTheDocument();
  });

  it('should display the selected option name in the dropdown label', () => {
    const selectedOption = mockOptions[0];
    render(<DropdowWithSearch options={mockOptions} label="Select an option" selectedOption={selectedOption} setSelectedOption={jest.fn()} />);
    expect(screen.getByText(selectedOption.name)).toBeInTheDocument();
  });

  it('should update the search query when input changes', () => {
    render(<DropdowWithSearch options={mockOptions} label="Select an option" selectedOption={null} setSelectedOption={jest.fn()} />);
    fireEvent.click(screen.getByText('Select an option'));
    const searchInput = screen.getByPlaceholderText('Search...');
    fireEvent.change(searchInput, { target: { value: 'Option 1' } });
    expect(searchInput).toHaveValue('Option 1');
  });
});
