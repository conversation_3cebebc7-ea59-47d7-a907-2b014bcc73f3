import {
  ReactNode,
  useCallback, useState,
} from 'react';

import OutsideClick from '../../utils/OutsideClick';
import { Avatar, BorderProps } from '../avatar/Avatar.Component';
import { providersFormConstants } from '../providersForm/ProvidersForm.constant';
import { StyledOptionName } from '../providersForm/ProvidersForm.Style';
import { TooltipProps } from '../tooltip/Tooltip.Component';

import {
  DropdownOptionsWrapper,
  DropdownPosition,
  DropdownWrapper,
  NoOptionsFound,
  OptionItem,
  OptionListWrapper,
  SearchInput,
} from './Dropdown.Style';

export interface OptionsDropdownProps {
  id: string;
  name?: string;
  src?: string;
  size?: number;
  border?: BorderProps;
  tooltip?: boolean;
  tooltipProps?: TooltipProps;
  shape?: 'circle' | 'square';
  renderer?: (value: OptionsDropdownProps) => ReactNode;
}

interface DropdownProps {
  options: OptionsDropdownProps[];
  children: React.ReactNode;
  setSelectedOption: (option: OptionsDropdownProps) => void;
  itemsSelected?: OptionsDropdownProps[];
  showAvatar?: boolean;
  disabled?: boolean;
  onSearchChange?: (searchValue: string) => void;
  customSearchbox?: React.ReactNode;
  loadMoreOptions?: () => void;
  onToggle?: (isOpen: boolean) => void;
  position?: DropdownPosition;
  dropdownMinWidth?: string;
  dropdownMaxWidth?: string;
}

export const DropdownWithSearch = ({
  options,
  children,
  setSelectedOption,
  itemsSelected,
  showAvatar = true,
  disabled = false,
  onSearchChange,
  loadMoreOptions,
  customSearchbox,
  onToggle,
  position = 'bottom',
  dropdownMinWidth = '250px',
  dropdownMaxWidth,
}: DropdownProps) => {
  const [isOpen, setIsOpen] = useState(false);

  const toggleDropdown = (state: boolean) => {
    if (disabled) {
      return;
    }
    setIsOpen(state);
    if (onToggle) {
      onToggle(state);
    }
  };

  const [searchQuery, setSearchQuery] = useState('');

  const filteredOptions = options?.filter((option) => {
    if (!itemsSelected || itemsSelected.length === 0) {
      return true;
    }

    return !itemsSelected.some((selected) => selected.id === option.id);
  });

  const handleOptionSelect = useCallback((option: OptionsDropdownProps) => {
    setSelectedOption(option);
    setSearchQuery('');
    toggleDropdown(false);
  }, [setSelectedOption]);

  const onHandleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;
    setSearchQuery(value);

    if (onSearchChange) {
      onSearchChange(value);
    }
  };

  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = e.currentTarget;

    if (scrollHeight - scrollTop <= clientHeight + 50 && loadMoreOptions) {
      loadMoreOptions();
    }
  };

  const handleOutsideClick = () => {
    if (isOpen) {
      setSearchQuery('');
      toggleDropdown(false);
    }
  };

  const optionList = useCallback(() => {
    if (!filteredOptions || filteredOptions.length === 0) {
      return <NoOptionsFound>{providersFormConstants.NO_OPTION_FOUND}</NoOptionsFound>;
    }

    return filteredOptions.map((option) => {
      if (option?.renderer) {
        return (
          <div key={`renderer-${option.id}`} onClick={() => handleOptionSelect(option)}>
            {option.renderer(option)}
          </div>
        );
      }

      return (
        <OptionItem
          data-testid='dropdown-item'
          key={option.id}
          onClick={() => handleOptionSelect(option)}
        >
          {showAvatar && option.name && (
            <Avatar
              name={option.name}
              src={option.src}
              size={36}
              shape={option.shape || 'circle'}
            />
          )}
          <StyledOptionName>{option.name || 'Opción sin nombre'}</StyledOptionName>
        </OptionItem>
      );
    });
  }, [filteredOptions, handleOptionSelect, showAvatar]);

  return (
    <OutsideClick onOutsideClick={handleOutsideClick}>
      <DropdownWrapper>
        <div
          onClick={() => {
            if (!disabled) {
              toggleDropdown(!isOpen);
            }
          }}
          role="button"
          tabIndex={disabled ? -1 : 0}
          onKeyDown={(e) => {
            if (!disabled && (e.key === 'Enter' || e.key === ' ')) {
              toggleDropdown(!isOpen);
            }
          }}
        >
          {children}
        </div>

        {isOpen && (
          <DropdownOptionsWrapper
            data-testid='dropdown'
            onScroll={handleScroll}
            position={position}
            minWidth={dropdownMinWidth}
            maxWidth={dropdownMaxWidth}
          >
            {customSearchbox || (
              <SearchInput
                type="text"
                value={searchQuery}
                onChange={onHandleInputChange}
                placeholder="Buscar..."
                className="pd-search-input"
                autoFocus
              />
            )}
            <OptionListWrapper>
              {optionList()}
            </OptionListWrapper>
          </DropdownOptionsWrapper>
        )}
      </DropdownWrapper>
    </OutsideClick>
  );
};
