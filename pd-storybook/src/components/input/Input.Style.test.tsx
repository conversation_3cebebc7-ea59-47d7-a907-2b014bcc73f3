import { render } from '@testing-library/react';

import { StyledInput } from './Input.Style';

describe('StyledInput Snapshots', () => {
  it('renders correctly without error', () => {
    const { asFragment } = render(<StyledInput placeholder="Test Placeholder" />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders correctly with error', () => {
    const { asFragment } = render(<StyledInput error placeholder="Test Placeholder" />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders correctly with custom className', () => {
    const { asFragment } = render(<StyledInput className="custom-input-class" placeholder="Test Placeholder" />);
    expect(asFragment()).toMatchSnapshot();
  });
});
