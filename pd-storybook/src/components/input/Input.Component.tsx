import React from 'react';
import CurrencyInput from 'react-currency-input-field';

import { StyledInput, StyledInputProps } from './Input.Style';

export enum FormInputType {
  Text = 'text',
  Password = 'password',
  Email = 'email',
  Number = 'number',
}
export interface ErrorMsg {
  id: string | number;
  message: string;
}

export interface OptionProps {
  value: string;
  placeholder?: string;
}

export interface InputProps extends StyledInputProps {
  name: string;
  inputType?: FormInputType;
  label?: string;
  placeholder?: string;
  inputClassName?: string;
  labelClassName?: string;
  isRequired?: boolean;
  value?: string | number;
  error?: boolean;
  disabled?: boolean;
  min?: number;
  max?: number;
}

export const InputPrice = (props: {
  value?: number;
  onChange?: (event: React.ChangeEvent<HTMLInputElement>) => void;
  disabled?: boolean,
  name?: string
  className?: string
 }) => {
  const {
    onChange, disabled, name, value, className, ...restProps
  } = props;

  const inputChange = (localValue: string | undefined, localName?: string) => {
    if (localName) {
      const sanitizedValue = localValue ? localValue.replace(/[^\d.,]/g, '') : '';
      const event = {
        target: {
          name: localName,
          value: sanitizedValue,
        },
      } as React.ChangeEvent<HTMLInputElement>;

      if (onChange) {
        onChange(event);
      }
    }
  };

  return (
    <CurrencyInput
      data-testid="input-price"
      {...restProps}
      defaultValue={0}
      value={value}
      name={name}
      disabled={disabled}
      decimalsLimit={2}
      onValueChange={inputChange}
      prefix="$"
      style={{ lineHeight: '18px' }}
      className={`h-[38px] w-full rounded-lg text-xsm px-[12px] border pd-outline-positive pd-text-dark-700 ${className || ''}`}
      intlConfig={{ locale: 'es-MX', currency: 'MXN' }}
    />
  );
};

export const InputComponent: React.FC<InputProps> = ({
  name,
  label,
  inputType = FormInputType.Text,
  labelClassName,
  placeholder,
  isRequired,
  inputClassName,
  error,
  ...props
}) => (
  <div>
    <StyledInput
      className={inputClassName}
      type={inputType}
      placeholder={placeholder}
      name={name}
      required={isRequired}
      error={error}
      autoComplete='off'
      {...props}
        ></StyledInput>
  </div>
);
