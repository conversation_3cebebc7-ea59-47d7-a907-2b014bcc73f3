// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`StyledInput Snapshots renders correctly with custom className 1`] = `
<DocumentFragment>
  .emotion-0 {
  height: 38px;
  width: 100%;
  border-radius: 8px;
  font-size: 13px;
  line-height: 18px;
  color: #1D1F24;
  padding: 0px 12px;
  border: 1px solid #E0E3E8;
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
  text-align: left;
  border: 1px solid #E0E3E8;
  text-align: left;
}

.emotion-0:active,
.emotion-0:focus,
.emotion-0::selection,
.emotion-0:focus-visible {
  outline: 1px solid #5D923D;
}

.emotion-0:disabled {
  background-color: #FAFAFA;
  color: #3A3D44;
  cursor: not-allowed;
  opacity: 1;
}

.emotion-0::selection {
  background-color: #CEE4F8;
}

.emotion-0:active,
.emotion-0:focus,
.emotion-0::selection,
.emotion-0:focus-visible {
  outline: 1px solid #5D923D;
}

<input
    class="custom-input-class emotion-0"
    placeholder="Test Placeholder"
  />
</DocumentFragment>
`;

exports[`StyledInput Snapshots renders correctly with error 1`] = `
<DocumentFragment>
  .emotion-0 {
  height: 38px;
  width: 100%;
  border-radius: 8px;
  font-size: 13px;
  line-height: 18px;
  color: #1D1F24;
  padding: 0px 12px;
  border: 1px solid #E0E3E8;
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
  text-align: left;
  border: 1px solid #E55D57;
  text-align: left;
}

.emotion-0:active,
.emotion-0:focus,
.emotion-0::selection,
.emotion-0:focus-visible {
  outline: 1px solid #5D923D;
}

.emotion-0:disabled {
  background-color: #FAFAFA;
  color: #3A3D44;
  cursor: not-allowed;
  opacity: 1;
}

.emotion-0::selection {
  background-color: #CEE4F8;
}

.emotion-0:active,
.emotion-0:focus,
.emotion-0::selection,
.emotion-0:focus-visible {
  outline: 1px solid #E55D57;
}

<input
    class="emotion-0"
    placeholder="Test Placeholder"
  />
</DocumentFragment>
`;

exports[`StyledInput Snapshots renders correctly without error 1`] = `
<DocumentFragment>
  .emotion-0 {
  height: 38px;
  width: 100%;
  border-radius: 8px;
  font-size: 13px;
  line-height: 18px;
  color: #1D1F24;
  padding: 0px 12px;
  border: 1px solid #E0E3E8;
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
  text-align: left;
  border: 1px solid #E0E3E8;
  text-align: left;
}

.emotion-0:active,
.emotion-0:focus,
.emotion-0::selection,
.emotion-0:focus-visible {
  outline: 1px solid #5D923D;
}

.emotion-0:disabled {
  background-color: #FAFAFA;
  color: #3A3D44;
  cursor: not-allowed;
  opacity: 1;
}

.emotion-0::selection {
  background-color: #CEE4F8;
}

.emotion-0:active,
.emotion-0:focus,
.emotion-0::selection,
.emotion-0:focus-visible {
  outline: 1px solid #5D923D;
}

<input
    class="emotion-0"
    placeholder="Test Placeholder"
  />
</DocumentFragment>
`;
