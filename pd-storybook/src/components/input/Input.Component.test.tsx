import { fireEvent, render, screen } from '@testing-library/react';

import {
  FormInputType, InputComponent, InputPrice, InputProps,
} from './Input.Component';

describe('InputComponent', () => {
  const defaultProps: InputProps = {
    name: 'test-input',
    inputType: FormInputType.Text,
    placeholder: 'Enter text',
    label: 'Test Label',
    onChange: jest.fn(),
  };

  it('should render input with default props', () => {
    render(<InputComponent {...defaultProps} />);
    const inputElement = screen.getByPlaceholderText('Enter text');
    expect(inputElement).toBeInTheDocument();
    expect(inputElement).toHaveAttribute('type', 'text');
  });

  it('should render input with custom className', () => {
    render(<InputComponent {...defaultProps} inputClassName="custom-input-class" />);
    const inputElement = screen.getByPlaceholderText('Enter text');
    expect(inputElement).toHaveClass('custom-input-class');
  });

  it('should call onChange when input value changes', () => {
    const handleChange = jest.fn();
    render(<InputComponent {...defaultProps} onChange={handleChange} />);
    const inputElement = screen.getByPlaceholderText('Enter text');
    fireEvent.change(inputElement, { target: { value: 'new value' } });
    expect(handleChange).toHaveBeenCalled();
  });

  it('should render input with value', () => {
    render(<InputComponent {...defaultProps} value="test value" />);
    const inputElement = screen.getByPlaceholderText('Enter text');
    expect(inputElement).toHaveValue('test value');
  });

  it('should render input with name', () => {
    render(<InputComponent {...defaultProps} name="test-name" />);
    const inputElement = screen.getByPlaceholderText('Enter text');
    expect(inputElement).toHaveAttribute('name', 'test-name');
  });

  it('should render input with placeholder', () => {
    render(<InputComponent {...defaultProps} placeholder="test placeholder" />);
    const inputElement = screen.getByPlaceholderText('test placeholder');
    expect(inputElement).toBeInTheDocument();
  });
});

describe('InputPrice', () => {
  const defaultProps = {
    onChange: jest.fn(),
    value: 100,
  };

  it('should render input with default props', () => {
    render(<InputPrice {...defaultProps} />);
    const inputElement = screen.getByDisplayValue('$100');
    expect(inputElement).toBeInTheDocument();
    expect(inputElement).toHaveClass('pd-outline-positive');
  });

  it('should render input with empty value when no value is provided', () => {
    render(<InputPrice onChange={jest.fn()} />);
    const inputElement = screen.getByDisplayValue('$0');
    expect(inputElement).toBeInTheDocument();
  });

  it('should render input as disabled', () => {
    render(<InputPrice {...defaultProps} disabled={true} />);
    const inputElement = screen.getByDisplayValue('$100');
    expect(inputElement).toBeDisabled();
  });

  it('renders with correct props', () => {
    render(
      <InputComponent
        name="testInput"
        inputType={FormInputType.Text}
        placeholder="Enter text"
      />,
    );

    const inputElement = screen.getByPlaceholderText('Enter text');
    expect(inputElement).toBeInTheDocument();
    expect(inputElement).toHaveAttribute('type', 'text');
    expect(inputElement).toHaveAttribute('name', 'testInput');
  });

  it('should call onChange with correct event when onValueChange is triggered', () => {
    const handleChange = jest.fn();
    render(<InputPrice onChange={handleChange} name='price' value={0}/>);

    const currencyInput = screen.getByTestId('input-price');

    fireEvent.change(currencyInput, { target: { value: '123' } });

    expect(handleChange).toHaveBeenCalled();
    expect(handleChange).toHaveBeenCalledWith(
      expect.objectContaining({
        target: expect.objectContaining({
          value: '123',
        }),
      }),
    );
  });
});
