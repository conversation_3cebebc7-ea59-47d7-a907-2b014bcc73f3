import styled from '@emotion/styled';

import theme from '../../configurations/Theme.Configuration';

export interface StyledInputProps extends React.HTMLAttributes<HTMLElement> {
    error?: boolean;
    textAlign?: 'left' | 'right' | 'center';
}

export const BaseInput = styled.div`
  height: 38px;
  width: 100%;
  border-radius: ${theme.borderRadius.DEFAULT};
  font-size: ${theme.fontSize.xsm};
  color: ${theme.colors.dark[700]};
  padding: 0px 12px;
  border: 1px solid ${theme.colors.line};
  user-select: text;
  text-align: left;

  &:active,
  &:focus,
  &::selection,
  &:focus-visible {
    outline: 1px solid ${theme.colors.positive};
  }

  &:disabled {
    background-color: ${theme.colors.lightGray};
    color: ${theme.colors.dark[600]};
    cursor: not-allowed;
    opacity: 1;
  }

  &::selection {
    background-color: ${theme.colors.fadedBlue};
  }
`;

export const StyledInput = styled(BaseInput.withComponent('input')) <StyledInputProps>`
  border: 1px solid ${(props) => (!props.error ? theme.colors.line : theme.colors.negative)};
  text-align: ${(props) => props.textAlign || 'left'};

    &:active, &:focus, &::selection, &:focus-visible {
        outline: 1px solid ${(props) => (!props.error ? theme.colors.positive : theme.colors.negative)};
`;

export const StyledRequired = styled.span`
  color: ${theme.colors.negative};
  font-size: ${theme.fontSize.xsm};
`;
