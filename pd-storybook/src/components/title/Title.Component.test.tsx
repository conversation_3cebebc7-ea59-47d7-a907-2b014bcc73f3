import { render, screen } from '@testing-library/react';

import { Title } from './Title.Component';

describe('Title Component', () => {
  const defaultProps = {
    children: 'Test Title',
    className: 'test-class',
  };

  it('should render the title with correct text', () => {
    render(<Title as='h1' {...defaultProps} />);
    expect(screen.getByText('Test Title')).toBeInTheDocument();
  });

  it('should apply the correct className', () => {
    render(<Title as="h1" {...defaultProps} />);
    expect(screen.getByText('Test Title')).toHaveClass('test-class');
  });

  it('should render with the correct HTML tag', () => {
    render(<Title {...defaultProps} as="h2" />);
    const titleElement = screen.getByText('Test Title');
    expect(titleElement.tagName).toBe('H2');
  });

  it('should render children correctly', () => {
    render(<Title as='h1' {...defaultProps}><span data-testid="child">Child Element</span></Title>);
    expect(screen.getByTestId('child')).toBeInTheDocument();
  });
});
