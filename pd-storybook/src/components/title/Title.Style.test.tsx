import { render } from '@testing-library/react';

import { FontSize, FontWeight, TextAlign } from '../../types/Global.Type';

import { StyledTitle, StyledTitleProps } from './Title.Style';

describe('StyledTitle Snapshots', () => {
  const defaultProps: StyledTitleProps = {
    size: 'xxlg',
    weight: 'regular',
    align: 'left',
  };

  it('renders correctly with default styles', () => {
    const { asFragment } = render(
      <StyledTitle {...defaultProps}>Default Title</StyledTitle>,
    );
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders correctly with custom size', () => {
    const { asFragment } = render(
      <StyledTitle {...defaultProps} size="lg">
        Large Title
      </StyledTitle>,
    );
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders correctly with custom weight', () => {
    const { asFragment } = render(
      <StyledTitle {...defaultProps} weight="bold">
        Bold Title
      </StyledTitle>,
    );
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders correctly with custom alignment', () => {
    const { asFragment } = render(
      <StyledTitle {...defaultProps} align="center">
        Centered Title
      </StyledTitle>,
    );
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders correctly with custom className', () => {
    const { asFragment } = render(
      <StyledTitle {...defaultProps} className="custom-class">
        Custom Class Title
      </StyledTitle>,
    );
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders correctly with all custom props', () => {
    const { asFragment } = render(
      <StyledTitle
        size="sm"
        weight="light"
        align="right"
        className="custom-class"
      >
        All Custom Props Title
      </StyledTitle>,
    );
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders correctly with empty children', () => {
    const { asFragment } = render(
      <StyledTitle {...defaultProps}></StyledTitle>,
    );
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders correctly with null children', () => {
    const { asFragment } = render(
      <StyledTitle {...defaultProps}>{null}</StyledTitle>,
    );
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders correctly with undefined children', () => {
    const { asFragment } = render(
      <StyledTitle {...defaultProps}>{undefined}</StyledTitle>,
    );
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders correctly with different sizes', () => {
    const sizes: StyledTitleProps['size'][] = ['xxsm', 'xsm', 'sm', 'base', 'mdPlus', 'lg', 'lgPlus', 'xlg', 'xxlg'];
    sizes.forEach((size) => {
      const { asFragment } = render(
        <StyledTitle {...defaultProps} size={size}>
          {size} Title
        </StyledTitle>,
      );
      expect(asFragment()).toMatchSnapshot();
    });
  });

  it('renders correctly with different weights', () => {
    const weights: StyledTitleProps['weight'][] = ['light', 'regular', 'medium', 'semiBold', 'bold'];
    weights.forEach((weight) => {
      const { asFragment } = render(
        <StyledTitle {...defaultProps} weight={weight}>
          {weight} Title
        </StyledTitle>,
      );
      expect(asFragment()).toMatchSnapshot();
    });
  });

  it('renders correctly with different alignments', () => {
    const aligns: StyledTitleProps['align'][] = ['left', 'center', 'right', 'justify'];
    aligns.forEach((align) => {
      const { asFragment } = render(
        <StyledTitle {...defaultProps} align={align}>
          {align} Title
        </StyledTitle>,
      );
      expect(asFragment()).toMatchSnapshot();
    });
  });

  it('renders correctly with no props', () => {
    const { asFragment } = render(
      <StyledTitle>Title with no props</StyledTitle>,
    );
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders correctly with invalid size', () => {
    const { asFragment } = render(
      <StyledTitle {...defaultProps} size={'invalid' as FontSize}>
        Invalid Size Title
      </StyledTitle>,
    );
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders correctly with invalid weight', () => {
    const { asFragment } = render(
      <StyledTitle {...defaultProps} weight={'invalid' as FontWeight}>
        Invalid Weight Title
      </StyledTitle>,
    );
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders correctly with invalid align', () => {
    const { asFragment } = render(
      <StyledTitle {...defaultProps} align={'invalid' as TextAlign}>
        Invalid Align Title
      </StyledTitle>,
    );
    expect(asFragment()).toMatchSnapshot();
  });
});
