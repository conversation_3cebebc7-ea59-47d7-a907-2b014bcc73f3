import { css } from '@emotion/react';
import styled from '@emotion/styled';

import theme from '../../configurations/Theme.Configuration';
import { StyledGlobalTextStyles } from '../../styles/Global.Style';
import { TextAlign } from '../../types/Global.Type';
import { CommonTextProps } from '../../types/StyledGlobalText.Type';

export interface StyledTitleProps extends React.PropsWithChildren<CommonTextProps> {
    align?: TextAlign;
}

export const StyledTitle = styled.h1<StyledTitleProps>`
    ${(props) => css`
        ${StyledGlobalTextStyles(props)}
        color: ${props.color || theme.colors.dark[700]};
        font-size: ${theme.fontSize[props.size || 'xxlg']};
        text-align:  ${props.align || 'left'};
    `} 
`;
