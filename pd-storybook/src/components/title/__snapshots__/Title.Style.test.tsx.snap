// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`StyledTitle Snapshots renders correctly with all custom props 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-size: 14px;
  line-height: 20px;
  font-weight: 300;
  text-decoration-line: none;
  color: #1D1F24;
  font-size: 14px;
  line-height: 20px;
  text-align: right;
}

<h1
    class="custom-class emotion-0"
  >
    All Custom Props Title
  </h1>
</DocumentFragment>
`;

exports[`StyledTitle Snapshots renders correctly with custom alignment 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-size: 26px;
  line-height: 34px;
  font-weight: 400;
  text-decoration-line: none;
  color: #1D1F24;
  font-size: 26px;
  line-height: 34px;
  text-align: center;
}

<h1
    class="emotion-0"
  >
    Centered Title
  </h1>
</DocumentFragment>
`;

exports[`StyledTitle Snapshots renders correctly with custom className 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-size: 26px;
  line-height: 34px;
  font-weight: 400;
  text-decoration-line: none;
  color: #1D1F24;
  font-size: 26px;
  line-height: 34px;
  text-align: left;
}

<h1
    class="custom-class emotion-0"
  >
    Custom Class Title
  </h1>
</DocumentFragment>
`;

exports[`StyledTitle Snapshots renders correctly with custom size 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-size: 20px;
  line-height: 28px;
  font-weight: 400;
  text-decoration-line: none;
  color: #1D1F24;
  font-size: 20px;
  line-height: 28px;
  text-align: left;
}

<h1
    class="emotion-0"
  >
    Large Title
  </h1>
</DocumentFragment>
`;

exports[`StyledTitle Snapshots renders correctly with custom weight 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-size: 26px;
  line-height: 34px;
  font-weight: 700;
  text-decoration-line: none;
  color: #1D1F24;
  font-size: 26px;
  line-height: 34px;
  text-align: left;
}

<h1
    class="emotion-0"
  >
    Bold Title
  </h1>
</DocumentFragment>
`;

exports[`StyledTitle Snapshots renders correctly with default styles 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-size: 26px;
  line-height: 34px;
  font-weight: 400;
  text-decoration-line: none;
  color: #1D1F24;
  font-size: 26px;
  line-height: 34px;
  text-align: left;
}

<h1
    class="emotion-0"
  >
    Default Title
  </h1>
</DocumentFragment>
`;

exports[`StyledTitle Snapshots renders correctly with different alignments 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-size: 26px;
  line-height: 34px;
  font-weight: 400;
  text-decoration-line: none;
  color: #1D1F24;
  font-size: 26px;
  line-height: 34px;
  text-align: left;
}

<h1
    class="emotion-0"
  >
    left Title
  </h1>
</DocumentFragment>
`;

exports[`StyledTitle Snapshots renders correctly with different alignments 2`] = `
<DocumentFragment>
  .emotion-0 {
  font-size: 26px;
  line-height: 34px;
  font-weight: 400;
  text-decoration-line: none;
  color: #1D1F24;
  font-size: 26px;
  line-height: 34px;
  text-align: center;
}

<h1
    class="emotion-0"
  >
    center Title
  </h1>
</DocumentFragment>
`;

exports[`StyledTitle Snapshots renders correctly with different alignments 3`] = `
<DocumentFragment>
  .emotion-0 {
  font-size: 26px;
  line-height: 34px;
  font-weight: 400;
  text-decoration-line: none;
  color: #1D1F24;
  font-size: 26px;
  line-height: 34px;
  text-align: right;
}

<h1
    class="emotion-0"
  >
    right Title
  </h1>
</DocumentFragment>
`;

exports[`StyledTitle Snapshots renders correctly with different alignments 4`] = `
<DocumentFragment>
  .emotion-0 {
  font-size: 26px;
  line-height: 34px;
  font-weight: 400;
  text-decoration-line: none;
  color: #1D1F24;
  font-size: 26px;
  line-height: 34px;
  text-align: justify;
}

<h1
    class="emotion-0"
  >
    justify Title
  </h1>
</DocumentFragment>
`;

exports[`StyledTitle Snapshots renders correctly with different sizes 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-size: 12px;
  line-height: 16px;
  font-weight: 400;
  text-decoration-line: none;
  color: #1D1F24;
  font-size: 12px;
  line-height: 16px;
  text-align: left;
}

<h1
    class="emotion-0"
  >
    xxsm Title
  </h1>
</DocumentFragment>
`;

exports[`StyledTitle Snapshots renders correctly with different sizes 2`] = `
<DocumentFragment>
  .emotion-0 {
  font-size: 13px;
  line-height: 18px;
  font-weight: 400;
  text-decoration-line: none;
  color: #1D1F24;
  font-size: 13px;
  line-height: 18px;
  text-align: left;
}

<h1
    class="emotion-0"
  >
    xsm Title
  </h1>
</DocumentFragment>
`;

exports[`StyledTitle Snapshots renders correctly with different sizes 3`] = `
<DocumentFragment>
  .emotion-0 {
  font-size: 14px;
  line-height: 20px;
  font-weight: 400;
  text-decoration-line: none;
  color: #1D1F24;
  font-size: 14px;
  line-height: 20px;
  text-align: left;
}

<h1
    class="emotion-0"
  >
    sm Title
  </h1>
</DocumentFragment>
`;

exports[`StyledTitle Snapshots renders correctly with different sizes 4`] = `
<DocumentFragment>
  .emotion-0 {
  font-size: 16px;
  line-height: 24px;
  font-weight: 400;
  text-decoration-line: none;
  color: #1D1F24;
  font-size: 16px;
  line-height: 24px;
  text-align: left;
}

<h1
    class="emotion-0"
  >
    base Title
  </h1>
</DocumentFragment>
`;

exports[`StyledTitle Snapshots renders correctly with different sizes 5`] = `
<DocumentFragment>
  .emotion-0 {
  font-size: 18px;
  line-height: 28px;
  font-weight: 400;
  text-decoration-line: none;
  color: #1D1F24;
  font-size: 18px;
  line-height: 28px;
  text-align: left;
}

<h1
    class="emotion-0"
  >
    mdPlus Title
  </h1>
</DocumentFragment>
`;

exports[`StyledTitle Snapshots renders correctly with different sizes 6`] = `
<DocumentFragment>
  .emotion-0 {
  font-size: 20px;
  line-height: 28px;
  font-weight: 400;
  text-decoration-line: none;
  color: #1D1F24;
  font-size: 20px;
  line-height: 28px;
  text-align: left;
}

<h1
    class="emotion-0"
  >
    lg Title
  </h1>
</DocumentFragment>
`;

exports[`StyledTitle Snapshots renders correctly with different sizes 7`] = `
<DocumentFragment>
  .emotion-0 {
  font-size: 22px;
  line-height: 30px;
  font-weight: 400;
  text-decoration-line: none;
  color: #1D1F24;
  font-size: 22px;
  line-height: 30px;
  text-align: left;
}

<h1
    class="emotion-0"
  >
    lgPlus Title
  </h1>
</DocumentFragment>
`;

exports[`StyledTitle Snapshots renders correctly with different sizes 8`] = `
<DocumentFragment>
  .emotion-0 {
  font-size: 24px;
  line-height: 32px;
  font-weight: 400;
  text-decoration-line: none;
  color: #1D1F24;
  font-size: 24px;
  line-height: 32px;
  text-align: left;
}

<h1
    class="emotion-0"
  >
    xlg Title
  </h1>
</DocumentFragment>
`;

exports[`StyledTitle Snapshots renders correctly with different sizes 9`] = `
<DocumentFragment>
  .emotion-0 {
  font-size: 26px;
  line-height: 34px;
  font-weight: 400;
  text-decoration-line: none;
  color: #1D1F24;
  font-size: 26px;
  line-height: 34px;
  text-align: left;
}

<h1
    class="emotion-0"
  >
    xxlg Title
  </h1>
</DocumentFragment>
`;

exports[`StyledTitle Snapshots renders correctly with different weights 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-size: 26px;
  line-height: 34px;
  font-weight: 300;
  text-decoration-line: none;
  color: #1D1F24;
  font-size: 26px;
  line-height: 34px;
  text-align: left;
}

<h1
    class="emotion-0"
  >
    light Title
  </h1>
</DocumentFragment>
`;

exports[`StyledTitle Snapshots renders correctly with different weights 2`] = `
<DocumentFragment>
  .emotion-0 {
  font-size: 26px;
  line-height: 34px;
  font-weight: 400;
  text-decoration-line: none;
  color: #1D1F24;
  font-size: 26px;
  line-height: 34px;
  text-align: left;
}

<h1
    class="emotion-0"
  >
    regular Title
  </h1>
</DocumentFragment>
`;

exports[`StyledTitle Snapshots renders correctly with different weights 3`] = `
<DocumentFragment>
  .emotion-0 {
  font-size: 26px;
  line-height: 34px;
  font-weight: 500;
  text-decoration-line: none;
  color: #1D1F24;
  font-size: 26px;
  line-height: 34px;
  text-align: left;
}

<h1
    class="emotion-0"
  >
    medium Title
  </h1>
</DocumentFragment>
`;

exports[`StyledTitle Snapshots renders correctly with different weights 4`] = `
<DocumentFragment>
  .emotion-0 {
  font-size: 26px;
  line-height: 34px;
  font-weight: 600;
  text-decoration-line: none;
  color: #1D1F24;
  font-size: 26px;
  line-height: 34px;
  text-align: left;
}

<h1
    class="emotion-0"
  >
    semiBold Title
  </h1>
</DocumentFragment>
`;

exports[`StyledTitle Snapshots renders correctly with different weights 5`] = `
<DocumentFragment>
  .emotion-0 {
  font-size: 26px;
  line-height: 34px;
  font-weight: 700;
  text-decoration-line: none;
  color: #1D1F24;
  font-size: 26px;
  line-height: 34px;
  text-align: left;
}

<h1
    class="emotion-0"
  >
    bold Title
  </h1>
</DocumentFragment>
`;

exports[`StyledTitle Snapshots renders correctly with empty children 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-size: 26px;
  line-height: 34px;
  font-weight: 400;
  text-decoration-line: none;
  color: #1D1F24;
  font-size: 26px;
  line-height: 34px;
  text-align: left;
}

<h1
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`StyledTitle Snapshots renders correctly with invalid align 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-size: 26px;
  line-height: 34px;
  font-weight: 400;
  text-decoration-line: none;
  color: #1D1F24;
  font-size: 26px;
  line-height: 34px;
  text-align: invalid;
}

<h1
    class="emotion-0"
  >
    Invalid Align Title
  </h1>
</DocumentFragment>
`;

exports[`StyledTitle Snapshots renders correctly with invalid size 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-weight: 400;
  text-decoration-line: none;
  color: #1D1F24;
  text-align: left;
}

<h1
    class="emotion-0"
  >
    Invalid Size Title
  </h1>
</DocumentFragment>
`;

exports[`StyledTitle Snapshots renders correctly with invalid weight 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-size: 26px;
  line-height: 34px;
  text-decoration-line: none;
  color: #1D1F24;
  font-size: 26px;
  line-height: 34px;
  text-align: left;
}

<h1
    class="emotion-0"
  >
    Invalid Weight Title
  </h1>
</DocumentFragment>
`;

exports[`StyledTitle Snapshots renders correctly with no props 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-size: 16px;
  line-height: 24px;
  font-weight: 500;
  text-decoration-line: none;
  color: #1D1F24;
  font-size: 26px;
  line-height: 34px;
  text-align: left;
}

<h1
    class="emotion-0"
  >
    Title with no props
  </h1>
</DocumentFragment>
`;

exports[`StyledTitle Snapshots renders correctly with null children 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-size: 26px;
  line-height: 34px;
  font-weight: 400;
  text-decoration-line: none;
  color: #1D1F24;
  font-size: 26px;
  line-height: 34px;
  text-align: left;
}

<h1
    class="emotion-0"
  />
</DocumentFragment>
`;

exports[`StyledTitle Snapshots renders correctly with undefined children 1`] = `
<DocumentFragment>
  .emotion-0 {
  font-size: 26px;
  line-height: 34px;
  font-weight: 400;
  text-decoration-line: none;
  color: #1D1F24;
  font-size: 26px;
  line-height: 34px;
  text-align: left;
}

<h1
    class="emotion-0"
  />
</DocumentFragment>
`;
