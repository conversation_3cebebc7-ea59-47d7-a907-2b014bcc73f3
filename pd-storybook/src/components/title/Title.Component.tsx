import React from 'react';

import { As } from '../../types/Global.Type';

import { StyledTitle, StyledTitleProps } from './Title.Style';

interface TitleProps extends StyledTitleProps {
  as: As;
  className?: string;
}

export const Title: React.FC<TitleProps> = ({
  as,
  children,
  className,
  ...props
}) => (
  <StyledTitle as={as} className={className} {...props}>
    {children}
  </StyledTitle>);
