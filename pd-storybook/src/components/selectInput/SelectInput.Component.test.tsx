import { fireEvent, render, screen } from '@testing-library/react';

import { SelectInput, SelectInputProps } from './SelectInput.Component';

describe('SelectInput Component', () => {
  const defaultProps: SelectInputProps = {
    name: 'select-input',
    inputClassName: 'custom-select',
    isRequired: true,
    options: [
      { value: '1', placeholder: 'Option 1' },
      { value: '2', placeholder: 'Option 2' },
      { value: '3', placeholder: 'Option 3' },
    ],
  };

  it('must to render with the default propertires', () => {
    render(<SelectInput {...defaultProps} />);

    const selectElement = screen.getByRole('combobox');
    expect(selectElement).toBeInTheDocument();
    expect(selectElement).toHaveClass('custom-select');
    expect(selectElement).toHaveAttribute('name', 'select-input');
    expect(selectElement).toBeRequired();
  });

  it('must to render the options correctly', () => {
    render(<SelectInput {...defaultProps} />);

    const options = screen.getAllByRole('option');
    expect(options).toHaveLength(4);
    expect(options[0]).toHaveTextContent('Select');
    expect(options[1]).toHaveTextContent('Option 1');
    expect(options[2]).toHaveTextContent('Option 2');
    expect(options[3]).toHaveTextContent('Option 3');
  });

  it('must to call the onChange function when an option is selected', () => {
    const handleChange = jest.fn();
    render(<SelectInput {...defaultProps} onChange={handleChange} />);

    const selectElement = screen.getByRole('combobox');
    fireEvent.change(selectElement, { target: { value: '2' } });
    expect(handleChange).toHaveBeenCalled();
    expect(selectElement).toHaveValue('2');
  });

  it('must to apply the custom classes', () => {
    render(<SelectInput {...defaultProps} inputClassName="custom-class" />);

    const selectElement = screen.getByRole('combobox');
    expect(selectElement).toHaveClass('custom-class');
  });

  it('must to handle the additional props', () => {
    render(
      <SelectInput
        {...defaultProps}
        data-testid="custom-select-input"
        aria-label="Select Input"
      />,
    );

    const selectElement = screen.getByTestId('custom-select-input');
    expect(selectElement).toBeInTheDocument();
    expect(selectElement).toHaveAttribute('aria-label', 'Select Input');
  });

  it('must to render correctly when no options is provided', () => {
    render(<SelectInput {...defaultProps} options={[]} />);

    const options = screen.getAllByRole('option');
    expect(options).toHaveLength(1);
    expect(options[0]).toHaveTextContent('Select');
  });

  it('Must to render correctly when no options placeholder is provided', () => {
    render(<SelectInput {...defaultProps} options={[{ value: '111' }]} />);

    const options = screen.getAllByRole('option');
    expect(options).toHaveLength(2);
    expect(options[0]).toHaveTextContent('Select');
    expect(options[1]).toHaveTextContent('111');
  });
});
