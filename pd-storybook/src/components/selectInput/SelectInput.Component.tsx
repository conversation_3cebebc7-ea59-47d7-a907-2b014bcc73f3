import { InputProps, OptionProps } from '../input/Input.Component';

import { StyledSelect } from './SelectInput.Style';

export interface SelectInputProps extends InputProps {
    options?: OptionProps[];
}

export const SelectInput: React.FC<SelectInputProps> = ({
  inputClassName,
  name,
  isRequired,
  options,
  ...props
}) => (
  <StyledSelect
    className={inputClassName}
    name={name}
    required={isRequired}
    {...props}
    >
    <option value='' disabled>Select</option>
    {options && options.map((option) => (
      <option key={option.value} value={option.value}>
        {option.placeholder || option.value}
      </option>
    ))}
  </StyledSelect>
);
