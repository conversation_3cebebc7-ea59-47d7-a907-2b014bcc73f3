import * as SelectPrimitive from '@radix-ui/react-select';
import * as React from 'react';

import { cn } from '../../utils/Cn.Util';
import { IconImporter } from '../iconImporter/IconImporter.Component';

const BaseSelectInput = SelectPrimitive.Root;

const SelectGroup = SelectPrimitive.Group;

const SelectValue = SelectPrimitive.Value;

const SelectTrigger = React.forwardRef<
    React.ElementRef<typeof SelectPrimitive.Trigger>,
    React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>
>(({ className, children, ...props }, ref) => (
  <SelectPrimitive.Trigger
    ref={ref}
    className={cn(
      // eslint-disable-next-line max-len
      'pd-flex pd-h-9 pd-w-full pd-items-center pd-justify-between pd-whitespace-nowrap pd-rounded-md pd-border pd-border-input pd-bg-transparent pd-px-3 pd-py-2 pd-text-sm pd-shadow-sm pd-ring-offset-background data-[placeholder]:pd-text-muted-foreground focus:pd-outline-none focus:pd-ring-1 focus:pd-ring-ring disabled:pd-cursor-not-allowed disabled:pd-opacity-50 [&>span]:pd-line-clamp-1',
      className,
    )}
    {...props}
    >
    {children}
    <SelectPrimitive.Icon asChild>
      <IconImporter name='caretDown' className="pd-h-4 pd-w-4 pd-opacity-50" />
    </SelectPrimitive.Icon>
  </SelectPrimitive.Trigger>
));
SelectTrigger.displayName = SelectPrimitive.Trigger.displayName;

const SelectScrollUpButton = React.forwardRef<
    React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,
    React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>
>(({ className, ...props }, ref) => (
  <SelectPrimitive.ScrollUpButton
    ref={ref}
    className={cn(
      'pd-flex pd-cursor-default pd-items-center pd-justify-center pd-py-1',
      className,
    )}
    {...props}
    >
    <IconImporter name='caretUp' className="pd-h-4 pd-w-4" />
  </SelectPrimitive.ScrollUpButton>
));
SelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName;

const SelectScrollDownButton = React.forwardRef<
    React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,
    React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>
>(({ className, ...props }, ref) => (
  <SelectPrimitive.ScrollDownButton
    ref={ref}
    className={cn(
      'pd-flex pd-cursor-default pd-items-center pd-justify-center pd-py-1',
      className,
    )}
    {...props}
    >
    <IconImporter name='caretDown' className="pd-h-4 pd-w-4" />
  </SelectPrimitive.ScrollDownButton>
));
SelectScrollDownButton.displayName = SelectPrimitive.ScrollDownButton.displayName;

const SelectContent = React.forwardRef<
    React.ElementRef<typeof SelectPrimitive.Content>,
    React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>
>(({
  className, children, position = 'popper', ...props
}, ref) => (
  <SelectPrimitive.Portal>
    <SelectPrimitive.Content
      ref={ref}
      className={cn(
        // eslint-disable-next-line max-len
        'pd-relative pd-z-50 pd-max-h-[--radix-select-content-available-height] pd-min-w-[8rem] pd-overflow-y-auto pd-overflow-x-hidden pd-rounded-md pd-border pd-bg-white pd-text-popover-foreground pd-shadow-md data-[state=open]:pd-animate-in data-[state=closed]:pd-animate-out data-[state=closed]:pd-fade-out-0 data-[state=open]:pd-fade-in-0 data-[state=closed]:pd-zoom-out-95 data-[state=open]:pd-zoom-in-95 data-[side=bottom]:pd-slide-in-from-top-2 data-[side=left]:pd-slide-in-from-right-2 data-[side=right]:pd-slide-in-from-left-2 data-[side=top]:pd-slide-in-from-bottom-2 pd-origin-[--radix-select-content-transform-origin]',
        position === 'popper'
                && 'data-[side=bottom]:pd-translate-y-1 data-[side=left]:-pd-translate-x-1 data-[side=right]:pd-translate-x-1 data-[side=top]:-pd-translate-y-1',
        className,
      )}
      position={position}
      {...props}
        >
      <SelectScrollUpButton />
      <SelectPrimitive.Viewport
        className={cn(
          'pd-p-1',
          position === 'popper' && 'pd-h-[var(--radix-select-trigger-height)] pd-w-full pd-min-w-[var(--radix-select-trigger-width)]',
        )}
            >
        {children}
      </SelectPrimitive.Viewport>
      <SelectScrollDownButton />
    </SelectPrimitive.Content>
  </SelectPrimitive.Portal>
));
SelectContent.displayName = SelectPrimitive.Content.displayName;

const SelectLabel = React.forwardRef<
    React.ElementRef<typeof SelectPrimitive.Label>,
    React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>
>(({ className, ...props }, ref) => (
  <SelectPrimitive.Label
    ref={ref}
    className={cn('pd-px-2 pd-py-1.5 pd-text-sm pd-font-semibold', className)}
    {...props}
    />
));
SelectLabel.displayName = SelectPrimitive.Label.displayName;

const SelectItem = React.forwardRef<
    React.ElementRef<typeof SelectPrimitive.Item>,
    React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>
>(({ className, children, ...props }, ref) => (
  <SelectPrimitive.Item
    ref={ref}
    className={cn(
      // eslint-disable-next-line max-len
      'pd-relative pd-flex pd-w-full pd-cursor-default pd-select-none pd-items-center pd-rounded-sm pd-py-1.5 pd-pl-2 pd-pr-8 pd-text-sm pd-outline-none focus:pd-bg-accent focus:pd-text-accent-foreground data-[disabled]:pd-pointer-events-none data-[disabled]:pd-opacity-50',
      className,
    )}
    {...props}
    >
    <span className="pd-absolute pd-right-2 pd-flex pd-h-3.5 pd-w-3.5 pd-items-center pd-justify-center">
      <SelectPrimitive.ItemIndicator>
        <IconImporter name='check' className="pd-h-4 pd-w-4" />
      </SelectPrimitive.ItemIndicator>
    </span>
    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>
  </SelectPrimitive.Item>
));
SelectItem.displayName = SelectPrimitive.Item.displayName;

const SelectSeparator = React.forwardRef<
    React.ElementRef<typeof SelectPrimitive.Separator>,
    React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>
>(({ className, ...props }, ref) => (
  <SelectPrimitive.Separator
    ref={ref}
    className={cn('pd--mx-1 pd-my-1 pd-h-px pd-bg-muted', className)}
    {...props}
    />
));
SelectSeparator.displayName = SelectPrimitive.Separator.displayName;

export {
  BaseSelectInput,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectScrollDownButton,
  SelectScrollUpButton,
  SelectSeparator,
  SelectTrigger,
  SelectValue,
};
