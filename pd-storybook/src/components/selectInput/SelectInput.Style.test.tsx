import React from 'react';
import { render } from '@testing-library/react';

import { StyledSelect } from './SelectInput.Style';

describe('StyledSelect Snapshots', () => {
  it('renders StyledSelect correctly', () => {
    const { asFragment } = render(<StyledSelect />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders StyledSelect with custom className correctly', () => {
    const { asFragment } = render(<StyledSelect className="custom-select-class" />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders StyledSelect with additional props correctly', () => {
    const { asFragment } = render(<StyledSelect data-testid="custom-select" aria-label="Select Input" />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders StyledSelect with error state correctly', () => {
    const { asFragment } = render(<StyledSelect error />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders StyledSelect when disabled', () => {
    const { asFragment } = render(<StyledSelect disabled />);
    expect(asFragment()).toMatchSnapshot();
  });
});
