import { render, screen } from '@testing-library/react';

import { IconImporter } from '../iconImporter/IconImporter.Component';

import { InfoTips, InfoTipsProps } from './InfoTips.Component';

jest.mock('../iconImporter/IconImporter.Component', () => ({
  IconImporter: jest.fn(() => <svg data-testid="mock-icon" />),
}));

describe('InfoTips Component', () => {
  const defaultProps: InfoTipsProps = {
    title: 'Test Title',
    items: [
      {
        id: 1,
        subtitle: 'Test Subtitle 1',
        text: 'Test Text 1',
        icon: 'airplane',
      },
      {
        id: 2,
        subtitle: 'Test Subtitle 2',
        text: 'Test Text 2',
        icon: 'card',
      },
    ],
    className: 'test-class',
  };

  it('should render the title correctly', () => {
    render(<InfoTips {...defaultProps} />);
    expect(screen.getByText('Test Title')).toBeInTheDocument();
  });

  it('should render the correct number of items', () => {
    render(<InfoTips {...defaultProps} />);
    expect(screen.getAllByText(/Test Subtitle/)).toHaveLength(2);
    expect(screen.getAllByText(/Test Text/)).toHaveLength(2);
  });

  it('should render the icons correctly', () => {
    render(<InfoTips {...defaultProps} />);
    expect(IconImporter).toHaveBeenCalledWith(
      expect.objectContaining({ name: 'airplane' }),
      expect.anything(),
    );
    expect(IconImporter).toHaveBeenCalledWith(
      expect.objectContaining({ name: 'card' }),
      expect.anything(),
    );
  });

  it('should apply the correct className', () => {
    render(<InfoTips {...defaultProps} />);
    expect(screen.getByRole('complementary')).toHaveClass('test-class');
  });
});
