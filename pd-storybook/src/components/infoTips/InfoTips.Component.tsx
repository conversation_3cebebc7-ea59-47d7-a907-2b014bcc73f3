import React from 'react';

import { IconImporter } from '../iconImporter/IconImporter.Component';
import { IconName } from '../iconImporter/IconMap.Component';
import { Title } from '../title/Title.Component';

export interface InfoTipsItems {
  id: string | number;
  subtitle: string;
  text: string;
  icon: IconName;
}

export interface InfoTipsProps {
  title: string;
  items: InfoTipsItems[];
  className?: string;
}

export const InfoTips: React.FC<InfoTipsProps> = ({
  title,
  items,
  className,
  ...props
}) => (
  <aside
    className={`pd-font-Geist pd-bg-white dark:pd-bg-white pd-p-8 pd-rounded-lg ${className}`}
    {...props}
  >
    <div className="pd-flex pd-flex-col pd-gap-10">
      <Title as='h1' weight='semiBold'>{title}</Title>
      {items.map((item) => (
        <div key={item.id} className="pd-flex pd-flex-row pd-gap-2">
          <div className="pd-min-w-6 pd-h-6 pd-rounded-full pd-bg-primary pd-flex pd-justify-center pd-items-center">
            <IconImporter name={item.icon} color="white" size="16" />
          </div>
          <div>
            <h3 className="tpd-ext-base">{item.subtitle}</h3>
            <p className="pd-text-xsm">{item.text}</p>
          </div>
        </div>
      ))}
    </div>
  </aside>
);
