import { PropsWithChildren } from 'react';

import { IconImporter } from '../iconImporter/IconImporter.Component';

export interface EmptyCartProps {
    className?: string;
}

export const EmptyCart: React.FC<PropsWithChildren<EmptyCartProps>> = ({ children, className }) => (
  <div className={`pd-flex pd-flex-col pd-items-center pd-justify-center pd-h-full pd-text-gray-500 pd-text-center pd-p-4 ${className}`}>
    <IconImporter name="shoppingCart" size={48} className="pd-mb-4 pd-text-gray-300" />
    {children}
  </div>
);
