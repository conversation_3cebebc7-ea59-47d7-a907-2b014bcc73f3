/* eslint-disable max-len */
import { useEffect, useRef, useState } from 'react';

import { Discount, DiscountBlockWrapper } from '../discounts/DiscountItem.Component';
import { IconImporter } from '../iconImporter/IconImporter.Component';
import { ThreeDotsLoader } from '../loaders/ThreeDotsLoader.Component';

export interface ShoppingCartItemProps {
  id: string;
  name: string;
  unitPrice: number;
  unitPriceAfterDiscount?: number;
  subtotal?: number;
  quantity: number;
  imageUrl?: string;
  onImageError?: () => void;
  appliedDiscount?: Discount;
  applicableDiscounts?: Discount[];
  onIncrement?: (id: string) => void;
  onDecrement?: (id: string) => void;
  onRemove?: (id: string) => void;
  onQuantityChange?: (id: string, quantity: number) => void;
  isLoading?: boolean;
  className?: string;
}

export const ShoppingCartItem: React.FC<ShoppingCartItemProps> = ({
  id,
  name,
  unitPrice = 0,
  unitPriceAfterDiscount,
  subtotal = 0,
  quantity = 0,
  imageUrl,
  onImageError,
  onIncrement,
  onDecrement,
  onRemove,
  onQuantityChange,
  appliedDiscount,
  applicableDiscounts,
  isLoading = true,
  className = '',
}) => {
  const [inputValue, setInputValue] = useState(String(quantity));
  const [isInvalid, setIsInvalid] = useState(false); // New state for validation
  const typingTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  const inputRef = useRef<HTMLInputElement | null>(null);

  useEffect(() => {
    setInputValue(String(quantity));
    setIsInvalid(false);
  }, [quantity]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;
    if (/^\d*$/.test(value)) {
      setInputValue(value);
      setIsInvalid(false);

      if (Number(value) > 100 || Number(value) < 1) {
        setIsInvalid(true);
      }

      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }

      typingTimeoutRef.current = setTimeout(() => {
        const newQuantity = Number(value);
        if (newQuantity > 0 && newQuantity < 100 && newQuantity !== quantity) {
          onQuantityChange?.(id, newQuantity);
          inputRef.current?.blur();
        } else {
          setIsInvalid(true);
        }
      }, 1000);
    } else {
      setIsInvalid(true);
    }
  };

  return (
    <div
      className={`pd-w-full pd-p-4 pd-flex pd-border-b pd-border-b-dark-200 pd-flex-col pd-gap-4 pd-bg-white pd-transition-all pd-duration-300 pd-ease-in-out hover:pd-bg-dark-200 ${className}`}
    >
      <div className="pd-flex pd-items-center pd-gap-2">
        <div className="pd-relative pd-w-12 pd-h-12 pd-rounded pd-overflow-hidden pd-flex-shrink-0">
          <img
            src={imageUrl}
            className='pd-object-cover pd-h-full pd-w-auto'
            onError={onImageError}
            />
        </div>
        <div className="pd-flex-1 pd-min-w-0">
          <h4 className="pd-font-light pd-text-xxsm pd-truncate pd-text-dark-600" title={name}>
            {name}
          </h4>
          {isLoading
            ? <ThreeDotsLoader />
            : <div className='pd-flex pd-gap-1 pd-items-center'>
              <p className="pd-text-xsm">${unitPriceAfterDiscount?.toFixed(2)}</p>
              {unitPriceAfterDiscount !== unitPrice && unitPriceAfterDiscount !== null
              && <p className="pd-text-xxsm pd-line-through pd-text-dark-500">${unitPrice?.toFixed(2)}</p>
              }
            </div>
          }
        </div>
        <div
          className="pd-flex pd-justify-center pd-align-middle  pd-self-start"
          onClick={() => onRemove && onRemove(id)}
          aria-label="Remove item"
        >
          <IconImporter name='x' size={24} className="pd-h-4 pd-w-4 hover:pd-cursor-pointer pd-transition-all pd-duration-300 pd-ease-in-out hover:pd-text-primary" />
        </div>
      </div>
      <div className="pd-flex pd-items-center pd-justify-between">
        <div className="pd-flex pd-items-center pd-gap-2 pd-w-1/2">
          <button
            className="pd-h-full pd-w-auto pd-p-1 pd-rounded-full hover:pd-cursor-pointer disabled:pd-cursor-not-allowed disabled:pd-bg-transparent pd-flex pd-items-center pd-justify-center hover:pd-bg-white pd-transition-border pd-duration-300 pd-ease-in-out"
            onClick={() => onDecrement && onDecrement(id)}
            disabled={quantity <= 1}
            aria-label="Decrease quantity"
          >
            <IconImporter name='minus' size={16} />
          </button>
          <input
            ref={inputRef}
            value={inputValue}
            onChange={handleChange}
            className={`pd-text-center pd-border pd-rounded-md pd-w-full pd-text-sm pd-py-1 ${isInvalid && 'focus-visible:pd-outline-negative'}`}
            max={100}
          />
          <button
            className="pd-h-full pd-w-auto pd-p-1 pd-rounded-full hover:pd-cursor-pointer pd-flex pd-items-center pd-justify-center hover:pd-bg-white pd-transition-border pd-duration-300 pd-ease-in-out"
            onClick={() => onIncrement && onIncrement(id)}
            aria-label="Increase quantity"
          >
            <IconImporter name='plus' size={16} />
          </button>
        </div>
        {isLoading ? <ThreeDotsLoader /> : <p className="pd-text-sm pd-font-medium">${subtotal?.toFixed(2)}</p> }
      </div>

      {applicableDiscounts && applicableDiscounts.length > 0
        && <DiscountBlockWrapper discounts={applicableDiscounts} applied={appliedDiscount} isLoading={isLoading} />
      }
    </div>
  );
};
