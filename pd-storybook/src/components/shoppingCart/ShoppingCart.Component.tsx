import React from 'react';

import { HiddenMenu } from '../hiddenMenu/HiddenMenu.Component';

import { EmptyCart } from './EmptyCart.Component';
import { ShoppingCartItem, ShoppingCartItemProps } from './ShoppingCartItem.Component';
import { ShoppingCartSummary, ShoppingCartSummaryProps } from './ShoppingCartSummary.Component';

export interface CartItemProps extends ShoppingCartItemProps {
  renderer?: (item: unknown) => React.ReactNode;
}

export interface ShoppingCartProps {
  isLoading?: boolean;
  isOpen: boolean;
  items: CartItemProps[];
  cartSummary: ShoppingCartSummaryProps;
  emptyCartLabel?: string;
  className?: string;
}

const RenderCartItems = (item: CartItemProps) => (
  <>
    {item.renderer ? item.renderer(item) : <ShoppingCartItem {...item} />}
  </>
);

export const ShoppingCart: React.FC<ShoppingCartProps> = ({
  isOpen,
  isLoading = true,
  items = [],
  cartSummary,
  emptyCartLabel = 'Tu carrito está vacío',
  className = '',
}) => (
  <HiddenMenu
    isOpen={isOpen}
    className={className}
    >
    <div className="pd-flex pd-flex-col pd-h-full pd-bg-white pd-shadow-lg">
      <div className="pd-sticky pd-top-0 pd-p-4 pd-z-10 pd-border-b pd-border-dark-200">
        <ShoppingCartSummary
          {...cartSummary}
          isLoading={isLoading}
          />
      </div>
      <div className='pd-flex-1 pd-overflow-y-auto'>
        {items.length === 0
            && <EmptyCart >
              {emptyCartLabel}
            </EmptyCart>}
        {items?.map((item) => <RenderCartItems key={item.id} {...item} isLoading={isLoading} />)}
      </div>
    </div>
  </HiddenMenu>
);
