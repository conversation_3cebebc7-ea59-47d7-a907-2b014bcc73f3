import React from 'react';

import { Button, ButtonProps } from '../button/Button.Component';
import { ThreeDotsLoader } from '../loaders/ThreeDotsLoader.Component';

export interface ShoppingCartSummaryProps {
  subtotalBeforeDiscount?: number;
  subTotal: number;
  total: number;
  taxesValue?: number;
  discountValue?: number;
  shippingCost?: number;
  checkoutButton?: ButtonProps;
  className?: string;
  isLoading?: boolean;
}

export const LABELS = {
  SUBTOTAL_BOFORE_DISCOUNT: 'Subtotal antes de descuento',
  TITLE: 'Carrito de compras',
  SUBTOTAL: 'Subtotal',
  DISCOUNT: 'Descuento',
  TAXES: 'Impuestos',
  SHIPPING: 'Envío',
  TOTAL: 'Total',
  FREE_SHIPPING: 'Envío gratis',
};

const SummaryRow: React.FC<{
  label: string;
  value: number | null | undefined;
  isLoading: boolean;
  prefix?: string;
  isBold?: boolean;
  isNegative?: boolean;
}> = ({
  label, value, isLoading, prefix = '$', isBold = false, isNegative = false,
}) => {
  const textClass = isBold
    ? 'pd-font-semibold pd-text-mdPlus'
    : 'pd-text-dark-500 pd-text-xsm';

  const valueClass = isBold
    ? 'pd-font-semibold pd-text-mdPlus'
    : 'pd-text-dark-600 pd-text-sm';

  const formattedValue = value !== null && value !== undefined && !isLoading
    ? `${isNegative ? '-' : ''}${prefix}${value.toFixed(2)}`
    : null;

  return (
    <div className="pd-flex pd-justify-between pd-min-h-7">
      <span className={`${textClass}`}>{label}</span>
      <span className={`pd-min-w-third pd-text-end pd-flex pd-justify-end pd-items-center ${valueClass}`}>
        {isLoading ? <ThreeDotsLoader /> : formattedValue}
      </span>
    </div>
  );
};

export const ShoppingCartSummary = ({
  subtotalBeforeDiscount = 0,
  isLoading = false,
  subTotal = 0,
  total = 0,
  taxesValue = 0,
  discountValue = 0,
  shippingCost = 0,
  checkoutButton,
  className = '',
}: ShoppingCartSummaryProps) => {
  let shippingDisplay: React.ReactNode = '';
  if (isLoading) {
    shippingDisplay = <ThreeDotsLoader />;
  } else if (!shippingCost || shippingCost === 0) {
    shippingDisplay = LABELS.FREE_SHIPPING;
  } else {
    shippingDisplay = `$${shippingCost.toFixed(2)}`;
  }

  return (
    <div className={`pd-space-y-2 pd-bg-white pd-rounded-lg ${className}`}>
      <span>{LABELS.TITLE}</span>
      <SummaryRow
        label={LABELS.SUBTOTAL_BOFORE_DISCOUNT}
        value={subtotalBeforeDiscount}
        isLoading={isLoading}
      />

      <SummaryRow
        label={LABELS.DISCOUNT}
        value={discountValue}
        isLoading={isLoading}
        isNegative={ discountValue !== 0}
        />

      <SummaryRow
        label={LABELS.SUBTOTAL}
        value={subTotal}
        isLoading={isLoading}
      />

      <SummaryRow
        label={LABELS.TAXES}
        value={taxesValue}
        isLoading={isLoading}
        />

      <div className="pd-flex pd-justify-between">
        <span className="pd-text-dark-500 pd-text-xsm">{LABELS.SHIPPING}</span>
        <span className='pd-text-dark-600 pd-text-sm pd-min-h-7'>{shippingDisplay}</span>
      </div>

      <SummaryRow
        label={LABELS.TOTAL}
        value={total}
        isLoading={isLoading}
        isBold
      />

      {checkoutButton && (
        <Button variant='outlined' className='!pd-w-full' {...checkoutButton} />
      )}
    </div>
  );
};
