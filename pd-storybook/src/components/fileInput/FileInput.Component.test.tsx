import { fireEvent, render, screen } from '@testing-library/react';

import { FileInput } from './FileInput.Component';

describe('FileInput Component', () => {
  it('should render all components correctly', () => {
    render(<FileInput name="test-file" />);

    expect(screen.getByRole('textbox')).toBeInTheDocument();
    expect(screen.getByRole('button')).toBeInTheDocument();
    expect(document.querySelector('input[type="file"]')).toBeInTheDocument();
  });

  it('should use default button label when not provided', () => {
    render(<FileInput name="test-file" />);

    expect(screen.getByRole('button')).toHaveTextContent('Search');
  });

  it('should use custom button label when provided', () => {
    const customLabel = 'Upload File';
    render(<FileInput name="test-file" fileInputButtonLabel={customLabel} />);

    expect(screen.getByRole('button')).toHaveTextContent(customLabel);
  });

  it('should display placeholder in visible input', () => {
    const placeholder = 'Choose a file...';
    render(<FileInput name="test-file" placeholder={placeholder} />);

    expect(screen.getByRole('textbox')).toHaveAttribute('placeholder', placeholder);
  });

  it('should display value in visible input', () => {
    const value = 'test-file.pdf';
    render(<FileInput name="test-file" value={value} />);

    expect(screen.getByRole('textbox')).toHaveValue(value);
  });

  it('should apply custom className to container', () => {
    const className = 'custom-class';
    render(<FileInput name="test-file" className={className} />);

    expect(document.querySelector(`.${className}`)).toBeInTheDocument();
  });

  it('should trigger hidden input click when button is clicked', () => {
    render(<FileInput name="test-file" />);

    const hiddenInput = document.querySelector('input[type="file"]') as HTMLInputElement;
    const button = screen.getByRole('button');

    const clickSpy = jest.spyOn(hiddenInput, 'click');

    fireEvent.click(button);

    expect(clickSpy).toHaveBeenCalled();
  });

  it('should call onChange when file is selected', () => {
    const handleChange = jest.fn();
    render(<FileInput name="test-file" onChange={handleChange} />);

    const hiddenInput = document.querySelector('input[type="file"]') as HTMLInputElement;
    const file = new File(['test'], 'test.pdf', { type: 'application/pdf' });

    fireEvent.change(hiddenInput, { target: { files: [file] } });

    expect(handleChange).toHaveBeenCalled();
  });
});
