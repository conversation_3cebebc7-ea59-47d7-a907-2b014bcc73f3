import { render } from '@testing-library/react';

import { StyledFileContainer, StyledHidenFileInput, SyledFileButton } from './FileInput.Style';

describe('FileInput Style Snapshots', () => {
  it('renders StyledHidenFileInput correctly', () => {
    const { asFragment } = render(<StyledHidenFileInput />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders StyledFileContainer correctly', () => {
    const { asFragment } = render(<StyledFileContainer />);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders SyledFileButton correctly', () => {
    const { asFragment } = render(<SyledFileButton>Button</SyledFileButton>);
    expect(asFragment()).toMatchSnapshot();
  });

  it('renders SyledFileButton with custom className correctly', () => {
    const { asFragment } = render(<SyledFileButton className="custom-button-class">Button</SyledFileButton>);
    expect(asFragment()).toMatchSnapshot();
  });
});
