import { useRef } from 'react';

import { InputProps } from '../input/Input.Component';
import { StyledInput } from '../input/Input.Style';

import { StyledFileContainer, StyledHidenFileInput, SyledFileButton } from './FileInput.Style';

export interface FileInputProps extends InputProps {
  fileInputButtonLabel?: string;
}

export const FileInput: React.FC<FileInputProps> = ({
  name,
  placeholder,
  inputClassName,
  className,
  value,
  fileInputButtonLabel = 'Search',
  ...props
}) => {
  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const handleButtonClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };
  return (
    <StyledFileContainer className={className}>
      <StyledHidenFileInput
        ref={fileInputRef}
        name={`${name}`}
        type='file'
        {...props}
            />
      <StyledInput
        type='text'
        placeholder={placeholder}
        className={inputClassName}
        readOnly
        value={value}
        onChange={props.onChange}
        error={props.error}
            >
      </StyledInput>
      <SyledFileButton
        btnType='button'
        onClick={handleButtonClick}
        >{fileInputButtonLabel}</SyledFileButton>
    </StyledFileContainer>
  );
};
