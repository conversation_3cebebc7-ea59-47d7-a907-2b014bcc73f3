import React from 'react';

import { DropdownSuggestions, DropdownSuggestionsProps } from '../dropdown/DropdownSuggestion';
import { InputComponent, InputProps } from '../input/Input.Component';

export type SelectedItem = {
  name: string;
  media?: { url: string }[];
};

interface AutocompleteInputProps {
  onBlur?: (event: React.FocusEvent<HTMLInputElement>) => void;
  dropdownSuggestionsProps: DropdownSuggestionsProps;
  inputProps: InputProps;
}
export const AutocompleteInput: React.FC<AutocompleteInputProps> = ({
  dropdownSuggestionsProps,
  inputProps,
}) => (
  <DropdownSuggestions
    {...dropdownSuggestionsProps}
  >
    <div className='relative flex items-center'>
      <div className='flex-1'>
        <InputComponent
          {...inputProps}
        />
      </div>
    </div>
  </DropdownSuggestions>
);
