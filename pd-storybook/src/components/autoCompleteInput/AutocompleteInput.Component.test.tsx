import '@testing-library/jest-dom';
import { fireEvent, render, screen } from '@testing-library/react';

import { DropdownSuggestionsProps } from '../dropdown/DropdownSuggestion';
import { OptionsDropdownProps } from '../dropdown/DropdownWithSearch.Component';

import { AutocompleteInput } from './AutocompleteInput.Component';

jest.mock('../dropdown/DropdownSuggestion', () => ({
  DropdownSuggestions: ({
    children, options, onSelect, ...props
  }: DropdownSuggestionsProps) => (
    <div data-testid="card-list-options" {...props}>
      {children}
      <div data-testid="options-list">
        {options.map((option: OptionsDropdownProps, index: number) => (
          <div key={index} data-testid={`option-${index}`} onClick={() => onSelect?.(option)}>
            {option.renderer ? option.renderer(option) : option.name}
          </div>
        ))}
      </div>
    </div>
  ),
}));

jest.mock('../input/Input.Component', () => ({
  InputComponent: (props: React.InputHTMLAttributes<HTMLInputElement>) => (
    <input data-testid="input-component" {...props} />
  ),
}));

describe('AutocompleteInput', () => {
  const mockOnSearch = jest.fn();
  const mockOnSelect = jest.fn();
  const mockOnBlur = jest.fn();

  const defaultCardListOptionsProps = {
    options: [
      { id: '1', name: 'Option 1' },
      { id: '2', name: 'Option 2' },
    ] as OptionsDropdownProps[],
    onSelect: mockOnSelect,
  };

  const defaultInputProps = {
    onChange: mockOnSearch,
    onBlur: mockOnBlur,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('debería renderizar el componente con props mínimas', () => {
    render(
      <AutocompleteInput
        dropdownSuggestionsProps={{ options: [] }}
        inputProps={{
          name: 'search-input',
        }}
      />,
    );
    expect(screen.getByTestId('card-list-options')).toBeInTheDocument();
    expect(screen.getByTestId('input-component')).toBeInTheDocument();
  });

  test('debería llamar a onSearch cuando el input cambia', () => {
    render(
      <AutocompleteInput
        dropdownSuggestionsProps={{ options: [] }}
        inputProps={{ ...defaultInputProps, name: 'search-input' }}
      />,
    );
    const input = screen.getByTestId('input-component');
    fireEvent.change(input, { target: { value: 'test' } });
    expect(mockOnSearch).toHaveBeenCalledWith(expect.any(Object));
  });

  test('debería llamar a onBlur cuando el input pierde el foco', () => {
    render(
      <AutocompleteInput
        dropdownSuggestionsProps={{ options: [] }}
        inputProps={{ ...defaultInputProps, name: 'search-input' }}
      />,
    );
    const input = screen.getByTestId('input-component');
    fireEvent.blur(input);
    expect(mockOnBlur).toHaveBeenCalledWith(expect.any(Object));
  });

  test('debería llamar a onSelect cuando se selecciona una opción', () => {
    render(
      <AutocompleteInput
        dropdownSuggestionsProps={defaultCardListOptionsProps}
        inputProps={{
          name: 'search-input',
        }}
      />,
    );
    const option1 = screen.getByText('Option 1');
    fireEvent.click(option1);
    expect(mockOnSelect).toHaveBeenCalled();
  });

  test('debería renderizar las opciones con el renderer por defecto si no se proporciona uno', () => {
    render(
      <AutocompleteInput
        dropdownSuggestionsProps={defaultCardListOptionsProps}
        inputProps={{
          name: 'search-input',
        }}
      />,
    );
    expect(screen.getByText('Option 1')).toBeInTheDocument();
    expect(screen.getByText('Option 2')).toBeInTheDocument();
  });

  test('debería renderizar las opciones usando el renderer proporcionado', () => {
    const customRenderer = (option: OptionsDropdownProps) => <span>Custom: {option.name}</span>;
    const optionsWithRenderer = [
      { id: '1', name: 'Option 1', renderer: () => customRenderer(optionsWithRenderer[0]) },
    ] as OptionsDropdownProps[];

    render(
      <AutocompleteInput
        dropdownSuggestionsProps={{ options: optionsWithRenderer, onSelect: mockOnSelect }}
        inputProps={{
          name: 'search-input',
        }}
      />,
    );
    expect(screen.getByText('Custom: Option 1')).toBeInTheDocument();
  });

  test('debería renderizar el input por defecto si no se proporciona un renderer en inputProps', () => {
    render(
      <AutocompleteInput
        dropdownSuggestionsProps={{ options: [] }}
        inputProps={{
          name: 'search-input',
        }}
      />,
    );
    expect(screen.getByTestId('input-component')).toBeInTheDocument();
    expect(screen.queryByTestId('custom-input-renderer')).not.toBeInTheDocument();
  });

  test('debería manejar opciones vacías sin errores', () => {
    render(
      <AutocompleteInput
        dropdownSuggestionsProps={{ options: [] }}
        inputProps={{
          name: 'search-input',
        }}
      />,
    );
    expect(screen.getByTestId('options-list')).toBeEmptyDOMElement();
  });
});
