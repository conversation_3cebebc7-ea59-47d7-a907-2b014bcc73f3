import { useEffect, useState } from 'react';

interface CountdownTimerProps {
  initialHours: number;
  initialMinutes: number;
  initialSeconds: number;
  className?: string;
}

export const CountdownTimer: React.FC<CountdownTimerProps> = ({
  initialHours,
  initialMinutes,
  initialSeconds,
  className = '',
}) => {
  const calculateTotalSeconds = () => initialHours * 3600 + initialMinutes * 60 + initialSeconds;

  const [secondsRemaining, setSecondsRemaining] = useState(calculateTotalSeconds());

  useEffect(() => {
    const timer = setInterval(() => {
      setSecondsRemaining((prev) => {
        if (prev <= 0) {
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const hours = Math.floor(secondsRemaining / 3600);
  const minutes = Math.floor((secondsRemaining % 3600) / 60);
  const seconds = secondsRemaining % 60;

  const formatTime = (value: number) => value.toString().padStart(2, '0');

  return (
    <div className={className}>
      {formatTime(hours)}:{formatTime(minutes)}:{formatTime(seconds)}
    </div>
  );
};
