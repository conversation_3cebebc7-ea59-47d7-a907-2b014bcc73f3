/* eslint-disable import/no-extraneous-dependencies */
import postcssImport from 'postcss-import';
import postcss from 'rollup-plugin-postcss';

const rollupOptions = [{
  input: 'src/styles/Global.Style.css',
  output: [{ file: 'dist/styles/Global.css' }],
  plugins: [
    postcss({
      extract: true,
      modules: false,
      inject: false,
      minimize: false,
      plugins: [postcssImport()],
    }),
  ],
}];

export default rollupOptions;
