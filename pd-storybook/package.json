{"name": "@pitsdepot/storybook", "version": "1.4.4", "private": false, "main": "./src/index.ts", "exports": {".": {"import": "./src/index.ts", "require": "./src/index.ts"}, "./styles/pd.css": "./dist/styles/Global.css", "./theme": "./src/configurations/Theme.Configuration.ts"}, "license": "UNLICENSED", "scripts": {"build": "rollup --config rollup.config.ts --configPlugin typescript", "build:dev": "rollup --config  rollup.config.dev.ts --configPlugin typescript", "build:dev:watch": "rollup --watch --config  rollup.config.dev.ts --configPlugin typescript", "build:watch": "rollup --watch --config rollup.config.dev.ts --configPlugin typescript", "clean": "rimraf ./dist", "prebuild": "pnpm clean", "start:dev": "storybook dev -p 3011", "build-storybook": "storybook build", "build-storybook-docs": "storybook build  --docs", "postbuild": "node ./scripts/buildPackageJson.mjs && cp .npmrc ./dist/.npmrc", "lint": "eslint .", "lint:fix": "eslint --fix .", "prepare": "husky", "test": "jest", "test:watch": "jest --watchAll", "test:coverage": "jest --coverage", "test:update": "jest --updateSnapshot", "artifactregistry-login": "npx google-artifactregistry-auth --repo-config=./.npmrc"}, "dependencies": {"@chromatic-com/storybook": "^2.0.2", "@emotion/jest": "^11.13.0", "@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@mui/material": "^7.0.2", "@mui/x-date-pickers": "8.0.0-beta.3", "@phosphor-icons/react": "^2.1.7", "@radix-ui/react-select": "^2.2.2", "@storybook/addon-actions": "^8.5.8", "@storybook/test": "^8.3.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "concurrently": "^6.1.0", "dayjs": "^1.11.13", "embla-carousel-react": "^8.6.0", "install": "^0.13.0", "react": "^18.3.1", "react-currency-input-field": "^3.10.0", "react-dom": "^18.3.1", "react-router-dom": "^6.26.2", "recharts": "^2.15.3", "shadcn": "2.3.0", "storybook": "^8.3.2", "tailwind-merge": "^3.2.0", "tw-animate-css": "^1.2.8"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-typescript": "^7.24.7", "@jest/types": "^29.6.3", "@rollup/plugin-commonjs": "^28.0.0", "@rollup/plugin-node-resolve": "^15.3.0", "@rollup/plugin-terser": "^0.4.4", "@rollup/plugin-typescript": "^11.1.6", "@storybook/addon-a11y": "^8.3.2", "@storybook/addon-essentials": "^8.3.2", "@storybook/addon-interactions": "^8.3.2", "@storybook/addon-links": "^8.3.2", "@storybook/blocks": "^8.3.2", "@storybook/react": "^8.3.2", "@storybook/react-vite": "^8.3.2", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.5.0", "@testing-library/react": "^16.0.1", "@types/jest": "^29.5.12", "@types/node": "^22.10.2", "@types/postcss-import": "^14.0.3", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@types/rollup-plugin-peer-deps-external": "^2.2.5", "@typescript-eslint/eslint-plugin": "^7.1.1", "@typescript-eslint/parser": "^7.1.1", "autoprefixer": "^10.4.20", "eslint": "^8.57.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-react": "^7.35.0", "husky": "^9.1.5", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.4.44", "postcss-import": "^16.1.0", "rimraf": "^6.0.1", "rollup": "^4.21.0", "rollup-plugin-dts": "^6.1.1", "rollup-plugin-import-css": "^3.5.1", "rollup-plugin-peer-deps-external": "^2.2.4", "rollup-plugin-postcss": "^4.0.2", "tailwindcss": "^3.4.10", "ts-jest": "^29.2.4", "ts-node": "^10.9.2", "tslib": "^2.7.0", "typescript": "^5.5.4"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.0 || ^18.0.0", "react-dom": "^16.8.0 || ^17.0.0 || ^18.0.0"}, "description": "Storybook design system template", "author": "Pits Depot", "packageManager": "pnpm@9.15.1+sha512.1acb565e6193efbebda772702950469150cf12bcc764262e7587e71d19dc98a423dff9536e57ea44c49bdf790ff694e83c27be5faa23d67e0c033b583be4bfcf"}