{"compilerOptions": {"target": "es2019", "lib": ["ESNext", "DOM"], "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "strict": true, "skipLibCheck": true, "jsx": "react-jsx", "jsxImportSource": "@emotion/react", "module": "ESNext", "declaration": true, "sourceMap": true, "outDir": "dist", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "emitDeclarationOnly": true}, "include": ["**/*.d.ts", "**/*.ts", "**/*.tsx"], "exclude": ["node_modules", "dist"]}